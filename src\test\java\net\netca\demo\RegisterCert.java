package net.netca.demo;

import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.codec.cmp.P10CertReqMessageCodec;
import net.netca.sdk.constants.IdentityTypeEnum;
import net.netca.sdk.constants.SignatureAlgorithmEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.*;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import okhttp3.*;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

import java.util.*;

public class RegisterCert {

    public static void main(String[] args) throws Exception {
        // 证书申请
        CmpRespResult result = regsterCert();
        System.out.println("sigCert:");
        System.out.println(result.getSignCertPem());
        System.out.println();

        System.out.println("encCert:");
        System.out.println(result.getEncCertPem());
        System.out.println();

        System.out.println("encKeyPair:");
        System.out.println(Base64.getEncoder().encodeToString(result.getEncPrivateKey()));
    }

    public static CmpRespResult regsterCert() throws Exception {
        // RA通讯证书，base64编码
        String raCommCertBase64 = "MIIB7DCCAZOgAwIBAgIUaOHjv3cKtuZ8mF79Ypf8QH9UTt4wCgYIKoEcz1UBg3UwVTELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEgMB4GA1UEAwwXTkVUQ0FfVGVzdDIwMTkgU00yIENBMDEwHhcNMjAwMjA0MTIzMDIwWhcNMzAwMjA0MTIzMDIwWjA2MQswCQYDVQQGEwJDTjEMMAoGA1UECgwDMTIzMRkwFwYDVQQDDBB0ZXN0X3JhX2NvbV9jZXJ0MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE94VXDtRI4oHSl1Hi8RCYspaDTuzg4HanrB85a47sTsLeIJh18OVj7oldPD68VdmkLb9cujOrZgo5rKRoVX/cHaNgMF4wHQYDVR0OBBYEFIbyPEB+0WBqfhQs8DV9EwylS6CgMA4GA1UdDwEB/wQEAwIGwDAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFNBBHunn3uwo364nD8m2qyrPEZ08MAoGCCqBHM9VAYN1A0cAMEQCIAXUnSVjgmxc4MCoVJ9CJlwM7phPxvADJqdlhiNRSGKQAiAJzW+NOv9xPDg+tNOSDYrP+g7HS0/51pnrplbEp9BmtQ==";
        // 第三方通讯证书，base64编码
        String thirdPartyServerCommCertBase64 = "MIID9TCCAt2gAwIBAgIUAnuKOT6/8ACO9itWARDQE90l1xEwDQYJKoZIhvcNAQELBQAwPjELMAkGA1UEBhMCQ04xDjAMBgNVBAoMBU5FVENBMR8wHQYDVQQDDBZURVNUMjAxOSBORVRDQSBSU0EgQ0ExMB4XDTIwMDQxNjA0MTcxN1oXDTI1MDQxNjA0MTcxN1owXDELMAkGA1UEBhMCQ04xDTALBgNVBAgMBHRlc3QxDTALBgNVBAcMBHRlc3QxDTALBgNVBAoMBHRlc3QxIDAeBgNVBAMMF25ldGNhcmFzZGst6YCa6K6v6K+B5LmmMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApFgbd56CK24YU1vzSrkIyTVL0f+gm3rHQMPupOUrsXCnBcCiNucWC8eXk8IJCacfZIHb9/P7NkNZGG+zv50rYKyt8fq7rbvat/UWOOKmRk3/vj88kVPIEehv3NT5eIACDrloEh4lrxBP6rOHOCbqbOEUi4e5myrKhCPGDn1u2Q1NcdPH2c5KMkaypOj6B256zcGNbx/r554Dke6IW098wcy+KnRtEP2+BJVoL6LyoCWB9W/4awfDdPBkbEM5aS5N4YCSGWuz3RVH38+KG2U/KOCyQdG8ODAd/zpGdL3ENRXfNsliRS1xNrB1fLdYwTcsCse6PKIRzKquZF+iTEBq4QIDAQABo4HMMIHJMB0GA1UdDgQWBBQb6XvERrmpSNiuaWB2N+CfaDTbhjAOBgNVHQ8BAf8EBAMCBaAwDAYDVR0TAQH/BAIwADBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vMTkyLjE2OC4yMDAuMTAxOjgwODAvY2FhZG1pbi9jb25maWdjcmx0ZW1wbGF0ZS9kb3dubG9hZENybC8yMB8GA1UdIwQYMBaAFEsdbaKdonEIiITHeoZjjJwWN55xMBMGA1UdJQQMMAoGCCsGAQUFBwMBMA0GCSqGSIb3DQEBCwUAA4IBAQBTEyStiQS6tuNf20UJmijtq8UiERqFO8rvNOCWt8UwQCkqLbBl6NqY0sFJw+818HQeGykRqIRHpaBKlWG+DLeVJUkRY/OhvdHqdFRp+IP2Olw4+GUm0AwNz+mtj+GaLC+cNPWJRG1Wlsuq7wARKYktcQpHKLVt3n4IWbVI/caqnjeIDK5uYoQcU58Bz+2ebUuwOcB34i3xZklxPBCYtrHwDm4ClZoWD8guhuyu+8/qMSN1weT5nHQ2iV/fWI0DzRd8BjSJciVIQEhL3X+v/gI3yo9PeVt0VbBtfqo7iEY1/zETBQmT9SU7YJOTbyHYwa2bXto8rCJLpmPy0yWplgk2";
        // 第三方通讯证书私钥，base64编码，格式：PrivateKeyInfo
        String thirdPartyServerCommCertKeyBase64 = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCkWBt3noIrbhhTW/NKuQjJNUvR/6CbesdAw+6k5SuxcKcFwKI25xYLx5eTwgkJpx9kgdv38/s2Q1kYb7O/nStgrK3x+rutu9q39RY44qZGTf++PzyRU8gR6G/c1Pl4gAIOuWgSHiWvEE/qs4c4Jups4RSLh7mbKsqEI8YOfW7ZDU1x08fZzkoyRrKk6PoHbnrNwY1vH+vnngOR7ohbT3zBzL4qdG0Q/b4ElWgvovKgJYH1b/hrB8N08GRsQzlpLk3hgJIZa7PdFUffz4obZT8o4LJB0bw4MB3/OkZ0vcQ1Fd82yWJFLXE2sHV8t1jBNywKx7o8ohHMqq5kX6JMQGrhAgMBAAECggEBAIbLQVQebVOjzE3q2CTMmVk23oZSbW+5B16mlcEnumuiI0wDkMkJRrrIrRljB7ikG4p7PGkl80Cxgo0ZaUvbZG25lD4p0e+QabeCTQTBWXHGneFzyoDEQA/4Hm/5OtjL3WQoTCS4F158OaAeOeUjfhlpj0lWPDT5pvs0wEi4NsfM4ePekbmRMDetnvShZyhTD3XInS4kMUabLWSvy8jhk+WYoZUoESHPo/Q1U8BC2omajaXOFBpBFQnOxaKYnp7FuDV8AvIi12dU6CYnX5y7m8zucrWKewcB8ao0xRlgVSXhYyTJ9aveiuV34A6UHq/vqcv1847u0oryo/Cp2oTLOuUCgYEAzczPvrQeRwJwPHzinyaU06lS8mmy5S+uovCoplxfKo+jgotiLp+QTN0a+aQ2+42BIa24mkCt6nHdl13quA+LI0wuzFuIZ9RVbmS0WMjwXciaqE9s6C/8hPVtJq1JV3+ubph+W9TIWT6RuxxSn5n3cM1hW8EQLutMh+Kh9V6d5McCgYEAzG6VKoSAFCMn6Xhctclky70BArAwb3dnzEOAsp6q13cAN5kZyVBqLkjJN+zYiFjBa+nM87iu0vRgj7e87UWYVOlgfwDV8Lix1MU8uNH3pHmfkj7f1QBTELvukeSuKvJXwIXsOFsa+vcj2ZotI3i+XQss7QB2KBS3aQJeQK/xOxcCgYBw2yb1ekq8QA8SbJ3ODtdAdzNgcixDWHGZMwYZLMCXU2ukNBFkZeY48GtRiQcJzaCJun1oQZ3d2hFdZ4hG5Wc0tRtYv70+ywcKYbkwkwiVNWPydpPPhDkjJH913UNB82fRiFiKvVYc8p3yEOcSVNmHh2kjau0X/brbLaIJL5jAkwKBgEGvEixsED7oIM3KB3e9Mq6nlqiHdbiK1AX0dmhtxJSgPqief4OIW+VtK4HVjqkYY2VJ5Lk2UK2lKVVkAsb1woEdzwxfd1316eVkTiNQC1KEZXn2y7JDfWz2BSxJHpDb9sf7/68Hzr5uI82p1PD9vaRlj9TCfjRYm6mYwjHklgyXAoGAI5fobow5NHvOsxndKHKfxrVZCcQqoCTfmmKFqQ4YCrlCXX7q8YzLY6pzMx1njnGX9DUa1uTjQ4T3BjJsRqquZnYe2BWaJfzbV0JQiygO95I6uEjWqETcTh9SHMaBYiYJXOZvrh4abUzefg9ckz17s5VfnGXiMBAWt9itnmeN9Vo=";
        byte[] thirdPartyServerCommCertKeyBlob = Base64.getDecoder().decode(thirdPartyServerCommCertKeyBase64);
        // 请求数据签名算法，调用第三方通讯证书私钥对请求数据进行签名
        String thirdPartyServerCommCertSignAlgoName = SignatureAlgorithmEnum.SHA256WithRSA.name();

        X509Certificate raCommCert = new X509Certificate(raCommCertBase64);
        X509Certificate thirdPartyServerCommCert = new X509Certificate(thirdPartyServerCommCertBase64);
        PrivateKeyInfo thirdPartyServerCommCertKey = PrivateKeyInfo.decode(thirdPartyServerCommCertKeyBlob);

        // 接入标识
        String caCommCertSha256 = "53e8bd21afaebfdfb6de56d9423b8bf398adf420abace78e29714e9a0ab7ee4b";
        // 证书模板编号
        String templateId = "T202112200001";

        // 自行构造p10，Base64编码形式
        String p10Str = "MIHcMIGCAgEAMCAxETAPBgNVBAMMCG1vY2tVc2VyMQswCQYDVQQGEwJDTjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABHEppWkHFoWeqvMViU1I+gPXYOQvRvWO1Ll0esMA2C5JPczX7qH2nBIzyNljKKuI1tGKkFmopnkBJWNExxf/2NigADAKBggqgRzPVQGDdQNJADBGAiEA2Q6ruz7e/9au/inJEWa1A6mXAU3KPi6IhWJq9bfsPp8CIQCDdGxsNRuWDsHnCl4Oi6WsmnZd7NIJdOM38Qf4sCGWAg==";
        // 证书请求号，业务标识，主要是唯一标识一个请求，由调用方自行赋值，需保证来自同一个调用方的证书请求号唯一
        long requestId = Math.abs(new Random().nextLong());
        //设置sender和recipient 自行构造
        String senderStr = thirdPartyServerCommCert.getSubject().getLdapName();
        String recipientStr = raCommCert.getSubject().getLdapName();
        // 有效期开始时间
        Date startTime = new Date();
        // 有效期结束时间
        Date endTime = new Date(startTime.getTime());
        endTime.setYear(startTime.getYear() + 1);

        // 证书用户信息
        UserInfo userInfo = UserInfo.builder()
                .name("测试用户")
                .identityType(IdentityTypeEnum.OTHERPERSONIDENTITYTYPE.getCode())
                .identity("12345678")
                .build();
        // 证书主题信息
        SubjectInfo subjectInfo = SubjectInfo.builder()
                .cn("证书申请" + System.currentTimeMillis())
                .c("CN")
                .build();
        // 证书有效期
        Validity validity = Validity.builder()
                .startTime(startTime)
                .endTime(endTime)
                .build();
        // 构建 P01 证书注册 CMP 请求所需的参数，包括：senderStr、recipientStr、p10、证书有效期（必选）、业务扩展（可选）和用于覆盖p10内的主题项信息（可选）
        // 若CustomFreeText设置SubjectInfo，则使用SubjectInfo信息作为证书主题信息；否则，使用p10主题作为证书主题信息
        // 若P10CertReqMessage设置requestExtensions，则会将这些额外扩展也签发到证书中（前提是证书模板无对应扩展）
        List<Extension> additionalExtensions = new ArrayList<>();
        additionalExtensions.add(new Extension("*********", false, Base64.getDecoder().decode("MAaHBH8AAAE=")));
        CustomFreeText customFreeText = CustomFreeText.builder()
                .validity(validity)
                .userInfo(userInfo)
                .subjectInfo(subjectInfo) // (选填)
                .certReqId(requestId)
                .build();
        P10CertReqMessage p10CertReqMessage = P10CertReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .p10Base64(p10Str)
                .customFreeText(customFreeText)
                .certRequestId(requestId)
                .requestExtensions(additionalExtensions) // (选填)
                .build();
        CmpMessageConfigManagement cmpMessageConfigManagement = CmpMessageConfigManagement.builder()
                .communicationCert(raCommCert)
                .thirdPartyServerCommCert(thirdPartyServerCommCert)
                .thirdPartyServerCommCertPrivateKeyInfo(thirdPartyServerCommCertKey)
                .thirdPartyServerCommCertSignAlgoName(thirdPartyServerCommCertSignAlgoName)
                .build();
        // 创建编码器
        MessageEncoder<CmpMessage> p10CertReqMessageEncoder = P10CertReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        // 创建证书请求上下文
        SingleCertReqContext context = new SingleCertReqContext(p10CertReqMessage);
        // 消息编码
        p10CertReqMessageEncoder.encode(context, p10CertReqMessage);
        // 获取编码结果：PKIMessages
        PKIMessages reqPkiMessages = context.getPKIMessages();
        byte[] reqData = reqPkiMessages.getEncoded();
        System.out.println("request:");
        System.out.println(Base64.getEncoder().encodeToString(reqData));
        System.out.println();

        // 发送请求
        String url = "http://192.168.200.102:8084/raserver/cmp/" + caCommCertSha256 + templateId;
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.get("application/pkixcmp"), reqData))
                .build();
        Response response = new OkHttpClient().newCall(request).execute();
        if (response.body() == null) {
            throw new IllegalStateException("response body should not be null: " + response.toString());
        }
        byte[] resData = response.body().bytes();
        System.out.println("response:");
        System.out.println(Base64.getEncoder().encodeToString(resData));
        System.out.println();

        // 解析响应
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(resData));
        // 创建解码器
        MessageDecoder<PKIMessage> p10CertReqMessageDecoder = P10CertReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        // 消息解码
        p10CertReqMessageDecoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);
        // 获取解码结果
        CmpRespResult cmpRespResult = context.getCmpRespResult();
        // 签名证书，pem编码
        String sigCertPem = cmpRespResult.getSignCertPem();
        // 加密证书，pem编码
        String encCertPem = cmpRespResult.getEncCertPem();
        // 加密密钥对
        byte[] encKeyPair = cmpRespResult.getEncPrivateKey();

        return cmpRespResult;
    }

}
