package net.netca.cloudkey.base.dto.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.netca.cloudkey.base.dto.abstractclass.HumanReadable;
import net.netca.cloudkey.base.dto.abstractclass.ICert;
import net.netca.cloudkey.base.dto.abstractclass.IUser;
import net.netca.cloudkey.base.dto.cert.Cert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class SelfUnlockSmsValidCodeReqDTO extends ImgValidCodeReq implements IUser, ICert, HumanReadable {

    @NotBlank(message = "userUid参数不能为空")
    private String userUid;

    @NotBlank(message = "name参数不能为空")
    private String name;

    @NotNull(message = "cert参数不能为空")
    private Cert cert;

}
