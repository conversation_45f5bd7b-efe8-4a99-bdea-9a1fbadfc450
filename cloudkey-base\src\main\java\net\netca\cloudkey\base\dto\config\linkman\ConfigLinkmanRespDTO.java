package net.netca.cloudkey.base.dto.config.linkman;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/1 14:46
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ConfigLinkmanRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer identityType;

    private String identity;

    private String name;

    private String phone;

    private String email;

    private String address;

    private Integer status;

    /**
     * 院区ID
     */
    private Integer districtId;

    /**
     * 院区名称
     */
    private String districtName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
