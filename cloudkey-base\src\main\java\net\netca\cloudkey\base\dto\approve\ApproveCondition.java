package net.netca.cloudkey.base.dto.approve;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApproveCondition {

    //用户uid
    private String userUid;

    //用户名称
    private String userName;

    //业务类型
    private Integer businessType;

    //审核状态
    private Integer approvePresentStatus;

    //证书序列号
    private String certSn;

    //项目id
    private String[] projectIds;

    //院区id
    private Integer districtId;

}
