# 通过 netcarasdk 对接长江 CA 平台需求规范

## 1. 功能概述

本功能旨在通过 netcarasdk 实现与长江 CA 平台的对接，扩展现有的证书生命周期管理系统。通过实现新的 ICertificateLifecycleManager 具体实现类，支持基于 CMP 协议的证书申请、查询、下载、注销等操作，并通过系统配置实现不同 CA 平台实现的动态切换。

**重要说明：** 长江 CA 实现不支持 `decryptAdminPin` 接口，该方法在长江 CA 实现中将抛出 `UnsupportedOperationException` 异常。长江 CA 平台使用不同的密钥保护机制，不依赖传统的管理员PIN解密功能。

## 2. 需求详情

### 2.1 长江 CA 平台对接实现

**用户故事：** 作为系统管理员，我希望能够通过 netcarasdk 对接长江 CA 平台，以便支持基于 CMP 协议的证书管理服务。

**验收标准：**
1. 当系统配置为使用长江 CA 实现时，系统应该创建 ChangjiangCACertificateLifecycleManager 实例
2. 如果 netcarasdk 依赖不可用，那么系统必须记录错误日志并回退到默认实现
3. 在长江 CA 实现中，系统应当使用 CMP 协议进行证书操作
4. 当长江 CA 平台连接失败时，系统应该抛出明确的异常信息
5. 如果证书模板配置错误，那么系统必须在初始化时进行验证并报告错误

### 2.2 证书申请功能

**用户故事：** 作为业务用户，我希望通过长江 CA 平台申请证书，以便获得符合 CMP 标准的数字证书。

**验收标准：**
1. 当用户提交证书申请时，系统应该构造符合 CMP 协议的 P10CertReqMessage
2. 如果用户信息不完整，那么系统必须返回详细的验证错误信息
3. 在证书申请过程中，系统应当支持个人证书、员工证书和组织证书类型
4. 当申请成功时，系统应该返回包含申请状态和请求ID的响应
5. 如果申请失败，那么系统必须记录详细的错误信息并返回用户友好的错误消息

### 2.3 证书状态查询功能

**用户故事：** 作为业务用户，我希望能够查询证书申请状态，以便了解证书处理进度。

**验收标准：**
1. 当用户查询证书状态时，系统应该使用 PollReqMessage 向长江 CA 平台发起查询
2. 如果请求ID不存在，那么系统必须返回明确的"请求不存在"错误
3. 在查询过程中，系统应当返回详细的状态信息（待审核、已签发、已拒绝等）
4. 当证书已签发时，系统应该自动触发证书下载流程
5. 如果查询超时，那么系统必须进行重试并记录重试次数

### 2.4 证书下载功能

**用户故事：** 作为业务用户，我希望能够下载已签发的证书，以便在业务系统中使用。

**验收标准：**
1. 当证书状态为已签发时，系统应该自动从长江 CA 平台下载证书
2. 如果下载失败，那么系统必须进行重试并记录失败原因
3. 在下载成功后，系统应当验证证书的完整性和有效性
4. 当证书包含加密证书时，系统应该同时处理签名证书和加密证书
5. 如果证书格式不正确，那么系统必须拒绝保存并记录错误信息

### 2.5 证书注销功能

**用户故事：** 作为业务用户，我希望能够注销不再需要的证书，以便维护证书的安全性。

**验收标准：**
1. 当用户申请注销证书时，系统应该构造 RevocationReqMessage 发送到长江 CA 平台
2. 如果证书已被注销，那么系统必须返回"证书已注销"的状态信息
3. 在注销过程中，系统应当支持不同的注销原因（密钥泄露、停止使用等）
4. 当注销成功时，系统应该更新本地证书状态为已注销
5. 如果注销失败，那么系统必须记录失败原因并通知用户

### 2.6 配置管理功能

**用户故事：** 作为系统管理员，我希望能够通过配置切换不同的证书生命周期管理实现，以便支持多种 CA 平台。

**验收标准：**
1. 当配置项设置为"CJCA"时，系统应该使用长江 CA 实现
2. 如果配置项为空或无效，那么系统必须使用默认的 BPMS 实现
3. 在配置变更时，系统应当支持热更新而无需重启
4. 当新实现初始化失败时，系统应该回退到默认实现并记录错误
5. 如果配置的实现类不存在，那么系统必须在启动时报告配置错误

### 2.7 错误处理和日志记录

**用户故事：** 作为系统运维人员，我希望系统能够提供详细的错误信息和日志记录，以便快速定位和解决问题。

**验收标准：**
1. 当发生网络连接错误时，系统应该记录详细的连接信息和错误原因
2. 如果 CMP 协议解析失败，那么系统必须记录原始消息内容用于调试
3. 在每个关键操作节点，系统应当记录操作日志包含用户ID、操作类型、时间戳
4. 当系统异常时，系统应该提供用户友好的错误消息而不暴露技术细节
5. 如果配置错误，那么系统必须在启动时进行验证并提供修复建议


### 2.8 接口实现范围限制

**用户故事：** 作为开发人员，我需要明确长江 CA 实现的接口范围，以便正确实现和使用相关功能。

**验收标准：**
1. 当实现长江 CA 证书生命周期管理器时，系统不应该实现 `decryptAdminPin` 方法
2. 如果业务代码调用 `decryptAdminPin` 方法，那么系统必须抛出 `UnsupportedOperationException` 异常
3. 在长江 CA 实现中，系统应当明确文档说明不支持管理员PIN解密功能
4. 如果配置错误导致调用不支持的方法，那么系统必须提供清晰的错误提示

### 2.9 密钥对管理兼容性

**用户故事：** 作为业务用户，我希望长江 CA 实现能够正确处理密钥对管理，保持与现有业务逻辑的一致性。

**验收标准：**
1. 当处理密钥对时，系统应该保持与现有业务逻辑完全一致的处理流程
2. 如果业务逻辑中涉及管理员PIN解密，那么系统必须在调用`decryptAdminPin`时抛出`UnsupportedOperationException`异常
3. 在密钥对存储和更新操作中，系统应当复用现有的业务逻辑和数据结构
4. 当密钥对操作中需要管理员PIN解密时，系统应该提供明确的异常信息说明该功能不被支持
5. 如果业务流程依赖管理员PIN解密结果，那么系统必须提供适当的错误处理和用户提示

### 2.10 兼容性要求

**用户故事：** 作为开发人员，我希望新的长江 CA 实现能够与现有系统完全兼容，以便平滑迁移和部署。

**验收标准：**
1. 当使用长江 CA 实现时，系统应该保持与现有 API 接口的兼容（除 `decryptAdminPin` 方法外）
2. 如果现有业务逻辑依赖特定返回格式，那么系统必须进行适配转换
3. 在数据库结构方面，系统应当复用现有的表结构和字段定义
4. 当进行版本升级时，系统应该支持向后兼容
5. 如果出现不兼容情况，那么系统必须提供迁移工具和详细文档
