package net.netca.sdk.entity;


import lombok.Data;
import net.netca.sdk.constants.PKIFailureInfoEnum;
import net.netca.sdk.constants.PKIStatusEnum;

/**
 * 消息解析结果
 *
 * <AUTHOR>
 * @date 2020-03-04 16:03
 */
@Data
public class CmpRespResult {

    /**
     * 服务端响应的CMP状态
     *
     * @see PKIStatusEnum
     */
    private PKIStatusEnum pkiStatusEnum;

    /**
     * 签名证书的pem编码
     */
    private String signCertPem;

    /**
     * 签名证书的DER编码
     */
    private byte[] signCertDer;
    /**
     * 加密证书的pem编码
     */
    private String encCertPem;

    /**
     * 签名证书的DER编码
     */
    private byte[] encCertDer;


    /**
     * 加密密钥对
     */
    private byte[] encPrivateKey;

    /**
     * 注销的旧签名证书序列号
     */
    private String revokedSigCertSn;

    /**
     * 对应申请请求中的certRequestId
     */
    private long respCertRequestId;


    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 协议错误码
     *
     * @see PKIFailureInfoEnum#code
     */
    private Integer failureCode;
}
