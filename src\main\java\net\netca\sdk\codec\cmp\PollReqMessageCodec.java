package net.netca.sdk.codec.cmp;


import net.netca.pki.encoding.asn1.pki.PublicKey;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.PollReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.validator.PKIMessageValidator;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PollRepContent;

/**
 * {@link PollReqMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/9 18:03
 */
public class PollReqMessageCodec extends CmpMessageCodec {


    private PollReqMessageCodec() {

    }

    private static PollReqMessageEncoder encoder;


    private static PollReqMessageDecoder decoder;

    public static PollReqMessageCodec createEncoder(CmpMessageConfigManagement configManagement) {
        encoder = new PollReqMessageEncoder(configManagement);
        return new PollReqMessageCodec();
    }

    public static PollReqMessageCodec createDecoder(CmpMessageConfigManagement configManagement) {
        decoder = new PollReqMessageDecoder(configManagement);
        return new PollReqMessageCodec();
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }

    final static class PollReqMessageEncoder extends CmpMessageToAnyObjectEncoder {


        PollReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }


        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws CmpEncodeException {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof PollReqMessage) {
                PollReqMessage pollReqMessage = (PollReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                try {
                    singleCertReqContext.setEncodeResult(generatePollReqData(pollReqMessage));
                } catch (Exception e) {
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }


        /**
         * 通过 {@link CmpMessage} 产生 cmp 证书轮询的请求数据 {@link PKIMessage}
         *
         * @param pollReqMessage {@link PollReqMessage}
         * @return {@link PKIMessage}
         */
        private PKIMessage generatePollReqData(PollReqMessage pollReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstance(pollReqMessage);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generatePollReq(pollReqMessage.getCertRequestId());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }

    }


    final static class PollReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {
        public PollReqMessageDecoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage pollRespPkiMessage) throws CmpDecodeException {
            checkDecodeArgs(context, pollRespPkiMessage);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                try {
                    if (singleCertReqContext.getCmpReqMessage() instanceof PollReqMessage) {
                        PKIMessage pollPkiMessage = singleCertReqContext.getEncodeResult();
                        PublicKey publicKey = new X509Certificate(communicationCert.derEncode()).getSubjectPublicKeyInfo().getPublicKey();
                        PKIMessageValidator.checkRespHeader(pollRespPkiMessage, pollPkiMessage.getHeader(), publicKey);
                        CmpRespResult cmpRespResult = singleCertReqContext.getCmpRespResult();
                        if (PKIBody.TYPE_POLL_REP == pollRespPkiMessage.getBody().getType()) {
                            // 轮训成功 或 轮询失败
                            PollRepContent pollRepContent = (PollRepContent) pollRespPkiMessage.getBody().getContent();
                            if (pollRepContent.getReason(0) == null) {
                                // 轮询成功，业务还在处理中
                                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.WAITING);
                            } else {
                                // 无错误码，只能设置错误原因
                                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.REJECTION);
                                cmpRespResult.setFailureReason(pollRepContent.getReason(0).getStringAt(0).toString());
                            }
                        } else {
                            // 证书轮询成功，业务已完成
                            // 获取签名证书
                            BusinessTypeEnum businessTypeEnum = singleCertReqContext.getCmpReqMessage().getBusinessTypeEnum();
                            switch (businessTypeEnum) {
                                case P10_REGISTER:
                                case RENEWAL:
                                case UPDATE_KEY:
                                    dealWithP10CertReqOrKeyUpdateRespPKIMessage(singleCertReqContext, pollRespPkiMessage);
                                    break;
                                case REVOKE:
                                case SUSPEND:
                                case UNSUSPEND:
                                    dealWithRevocationRespPKIMessage(singleCertReqContext, pollRespPkiMessage);
                                    break;
                                default:
                                    throw new CmpDecodeException("未知业务类型");
                            }
                        }
                    }
                } catch (Exception e) {
                    throw new CmpDecodeException(e.getMessage(), e);
                }
            }
        }
    }


}
