package net.netca.cloudkey.base.dto.superclass;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.IUserToken;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.constraints.NotBlank;

/**
 * @descriptions:
 * 用户登陆后标志身份的userToken
 * @date: 2019/6/13
 * @author: cxy
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserToken extends TimestampToken implements IUserToken {

    @NotBlank(message = "UserToken 不能为空")
    private String userToken;

}
