package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 系统类型
 */
public enum SystemTypeConstant implements IBaseEnum {
	CLOUDKEY_ADMIN("cloudkeyadmin", "云密钥管理系统"),
    CLOUDKEY_SERVER("cloudkeyserver", "云密钥服务系统"),
    CLOUDKEY_USER("cloudkeyuser", "云密钥用户系统");

	private String code;
	private String description;

	private SystemTypeConstant(String code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public String getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

}
