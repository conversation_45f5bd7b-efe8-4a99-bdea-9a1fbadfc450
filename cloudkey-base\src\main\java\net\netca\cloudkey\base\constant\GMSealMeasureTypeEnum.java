package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @date 2025/5/13
 */
@Getter
@AllArgsConstructor
public enum GMSealMeasureTypeEnum implements IBaseEnum {
    ORG_CIRCLE_SEAL(1,"单位圆形印章"),
    ORG_OVAL_SEAL(2,"单位椭圆印章"),
    BUSINESS_SEAL(3,"业务专用章"),
    AGREEMENT_SEAL(4,"合同专用章"),
    OTHER(5,"其他");

    private final Integer code;
    private final String desc;

    @Override
    public String getDescription() {
        return desc;
    }
}
