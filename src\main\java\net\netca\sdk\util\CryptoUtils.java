package net.netca.sdk.util;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.*;
import net.netca.sdk.constants.SignatureAlgorithmEnum;

import java.security.PrivateKey;
import java.security.SecureRandom;
import java.util.stream.LongStream;


public abstract class CryptoUtils {

    private static SecureRandom secureRandom = new SecureRandom();


    public static String hexEncode(byte[] code) {
        return Hex.encode(false, code);
    }

    public static String hexEncode(boolean cap, byte[] code) {
        return Hex.encode(cap, code);
    }

    public static byte[] hexDecode(String code) throws PkiException {
        return Hex.decode(code);
    }


    /**
     * PKCS#1 签名
     *
     * @param privateKey 私钥
     * @param tbs        原文
     * @param signAlgo   签名算法
     * @return
     */
    public static byte[] p1Sign(PrivateKey privateKey, byte[] tbs, String signAlgo) throws PkiException {
        JCESigner jceSigner = new JCESigner(privateKey);
        AlgorithmIdentifier algorithmIdentifier = AlgorithmIdentifier.CreateAlgorithmIdentifier(SignatureAlgorithmEnum.getByName(signAlgo).getCode());
        return jceSigner.sign(algorithmIdentifier, tbs, 0, tbs.length);
    }

    /**
     * PKCS#1 签名
     *
     * @param privateKeyInfo 私钥
     * @param tbs            原文
     * @param signAlgoOid    签名算法的OID
     * @return
     */
    public static byte[] p1SignWithOid(PrivateKeyInfo privateKeyInfo, byte[] tbs, String signAlgoOid) throws PkiException {
        JCESigner jceSigner = new JCESigner(privateKeyInfo);
        AlgorithmIdentifier algorithmIdentifier = AlgorithmIdentifier.CreateAlgorithmIdentifier(signAlgoOid);
        return jceSigner.sign(algorithmIdentifier, tbs, 0, tbs.length);
    }

    public static boolean p1Verify(byte[] tbs, byte[] signature, PublicKey publicKey, String signAlgo) throws PkiException {
        JCEVerifier verifier = new JCEVerifier();
        AlgorithmIdentifier algorithmIdentifier = AlgorithmIdentifier.CreateAlgorithmIdentifier(SignatureAlgorithmEnum.getByName(signAlgo).getCode());
        return verifier.verify(publicKey, algorithmIdentifier, tbs, 0, tbs.length, signature);
    }


    public static long generateRandomLong() {
        return secureRandom.nextLong();
    }

    public static void generateRandomBytes(byte[] value) {
        secureRandom.nextBytes(value);
    }

    public static LongStream generateRandomLongStream(int size) {
        return secureRandom.longs(size);
    }
}
