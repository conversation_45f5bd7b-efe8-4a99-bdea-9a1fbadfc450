package net.netca.cloudkey.base.constraints.check;

import net.netca.cloudkey.base.constant.OrgIdentityTypeEnum;
import net.netca.cloudkey.base.constant.OrganizationTypeEnum;
import net.netca.cloudkey.base.constant.PersonIdentityTypeEnum;
import net.netca.cloudkey.base.javaconfig.RegExpConfig;
import net.netca.cloudkey.base.util.EnumUtil;

import java.util.regex.Pattern;

public enum CheckType implements IChecker {

	TELNUM(new IChecker() {
		private Pattern regex;

		@Override
		public boolean check(String value) {// 检查是否电话号码，暂时只检查数字或符号-()
			if (regex == null) {
				regex = Pattern.compile(RegExpConfig.telPhone);
			}
			return regex.matcher(value).matches();
		}
	}, "电话号码"), 
	
	MOBILE(new IChecker() {
		private Pattern regex;

		@Override
		public boolean check(String value) {//检查手机号以13、14、15、16、17、18、19开头的11位数字
			if (regex == null) {
				regex = Pattern.compile(RegExpConfig.mobilePhone);
			}
			return regex.matcher(value).matches();
		}
	}, "手机号码"),

	EMAIL(new IChecker() {
		private Pattern regex;

		@Override
		public boolean check(String value) {
			if (regex == null) {
				regex = Pattern.compile(RegExpConfig.email);
			}
			return regex.matcher(value).matches();
		}
	}, "手机号码"),


	GENDER(new IChecker() {

		@Override
		public boolean check(String value) {
			return "0".equals(value) || "1".equals(value);
		}
	}, "性别"),

	ORGIDENTITYTYPE(new IChecker() {

		@Override
		public boolean check(String value) {
			boolean isValid = true;
			try {
				Integer integer = Integer.valueOf(value);
				// 根据value值查找枚举类，查到表示值范围是正确的，查不到则是非法的取值
				EnumUtil.getEnumObj(OrgIdentityTypeEnum.class, integer);
			} catch (Exception exception) {
				isValid = false;
			}
			return isValid;
		}
	}, "机构证件类型"),

	PERSONIDENTITYTYPE(new IChecker() {

		@Override
		public boolean check(String value) {
			boolean isValid = true;
			try {
				Integer integer = Integer.valueOf(value);
				// 根据value值查找枚举类，查到表示值范围是正确的，查不到则是非法的取值
				EnumUtil.getEnumObj(PersonIdentityTypeEnum.class, integer);
			} catch (Exception exception) {
				isValid = false;
			}
			return isValid;
		}
	}, "个人证件类型"),

	ORGANIZATIONTYPE(new IChecker() {

		@Override
		public boolean check(String value) {
			boolean isValid = true;
			try {
				Integer integer = Integer.valueOf(value);
				// 根据value值查找枚举类，查到表示值范围是正确的，查不到则是非法的取值
				EnumUtil.getEnumObj(OrganizationTypeEnum.class, integer);
			} catch (Exception exception) {
				isValid = false;
			}
			return isValid;
		}
	}, "机构类型");
	


	private CheckType(IChecker checker, String description) {
		this.checker = checker;
		this.description = description;
	}
	
	private IChecker checker;
	private String description;

	@Override
	public boolean check(String value){
		return checker.check(value);
	}
	
	public String getDescription() {
		return description;
	}


}
