package net.netca.test.asn1.format;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1Exception;
import net.netca.pki.encoding.asn1.UTF8String;
import net.netca.pki.encoding.asn1.pki.OtherName;

public class OtherNameFormat {
	/**
	 * typeId 为oid 
	 */
	private String typeId;
	private String value;
	public String getTypeId() {
		return typeId;
	}
	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public OtherName generateOtherName() throws ASN1Exception, PkiException {
		return new OtherName(typeId, new UTF8String(value));
	}
}
