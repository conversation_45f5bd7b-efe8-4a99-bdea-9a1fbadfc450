package net.netca.cloudkey.base.dto.area;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class AreaDTO implements Serializable {

    private static final long serialVersionUID = 2687917006324583050L;

    private Integer id;

    private String name;

    private String spelling;

    private List<CityDTO> citys;

    @Data
    @Builder
    public static class CityDTO implements Serializable {

        private static final long serialVersionUID = 2373936914101287785L;

        private String name;

        private String spelling;

        private Integer id;

        private Integer provinceId;
    }
}
