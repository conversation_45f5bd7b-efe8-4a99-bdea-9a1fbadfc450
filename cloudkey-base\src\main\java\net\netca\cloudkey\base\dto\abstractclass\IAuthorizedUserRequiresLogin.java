package net.netca.cloudkey.base.dto.abstractclass;

import net.netca.cloudkey.base.dto.cert.Cert;

/**
 * created by zaj on 2019/5/9
 */
public interface IAuthorizedUserRequiresLogin {

    /**
     * 被授权用户ID
     *
     * @return
     */
    Integer getAuthorizedUserId();

    /**
     * @return 登录的令牌
     */
    String getUserToken();


    /**
     * @return 授权人的证书信息
     */
    Cert getCert();



}
