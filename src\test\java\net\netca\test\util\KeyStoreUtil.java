package net.netca.test.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

/**
 * <AUTHOR>
 * @date 2019-12-30 14:43
 */
public abstract class KeyStoreUtil {

    /**
     * 获取 {@link KeyStore}
     *
     * @param filePath 文件路径
     * @param type     KeyStore的类型（jks or pkcs12）
     * @return {@link KeyStore}
     * @throws KeyStoreException
     * @throws IOException
     */
    public static KeyStore generateKeyStore(String filePath, String type, String pwd) throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException {
        try (FileInputStream fileInputStream = new FileInputStream(new File(filePath))) {
            KeyStore jks = KeyStore.getInstance(type.toLowerCase());
            jks.load(fileInputStream, pwd.toCharArray());
            return jks;
        }
    }
}
