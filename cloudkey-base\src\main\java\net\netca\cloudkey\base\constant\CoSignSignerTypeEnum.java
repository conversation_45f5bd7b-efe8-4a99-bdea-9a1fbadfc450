package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 签署者类型枚举：1-用户个体，2-角色，3-某一组织
 */
@Getter
@AllArgsConstructor
public enum CoSignSignerTypeEnum implements IBaseEnum {

    /**
     * 用户个体
     */
    USER(1, "用户"),

    /**
     * 角色
     */
    ROLE(2, "角色"),

    /**
     * 某一组织
     */
    ORGANIZATION(3, "组织");

    // 枚举值
    private final Integer code;

    // 描述信息
    private final String description;
}
