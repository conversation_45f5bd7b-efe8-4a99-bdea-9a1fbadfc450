package net.netca.sdk.codec.cmp;


import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.RevocationReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

/**
 * {@link RevocationReqMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/9 18:06
 */
public class RevocationReqMessageCodec extends CmpMessageCodec {

    private RevocationReqMessageCodec() {

    }

    private static RevocationReqMessageEncoder encoder;


    private static RevocationReqMessageDecoder decoder;

    public static RevocationReqMessageCodec createEncoder(CmpMessageConfigManagement configManagement) {
        encoder = new RevocationReqMessageEncoder(configManagement);
        return new RevocationReqMessageCodec();
    }

    public static RevocationReqMessageCodec createDecoder(CmpMessageConfigManagement configManagement) {
        decoder = new RevocationReqMessageDecoder(configManagement);
        return new RevocationReqMessageCodec();
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }

    final static class RevocationReqMessageEncoder extends CmpMessageToAnyObjectEncoder {
        RevocationReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

//        @Override
//        public boolean isValidMessage(CmpMessage message) {
//            return message instanceof RevocationReqMessage;
//        }

        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws CmpEncodeException {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof RevocationReqMessage) {

                RevocationReqMessage revocationReqMessage = (RevocationReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                try {
                    switch (revocationReqMessage.getBusinessTypeEnum()) {
                        case REVOKE:
                            singleCertReqContext.setEncodeResult(generateRevokeReqData(revocationReqMessage));
                            break;
                        case SUSPEND:
                            singleCertReqContext.setEncodeResult(generateSuspendedReqData(revocationReqMessage));
                            break;
                        case UNSUSPEND:
                            singleCertReqContext.setEncodeResult(generateUnSuspendedReqData(revocationReqMessage));
                            break;
                        default:
                            throw new IllegalArgumentException("businessType is illegal !");
                    }
                } catch (Exception e) {
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }


        /**
         * 通过 {@link CmpMessage} 产生 cmp 证书注销 的请求数据
         *
         * @param revocationReqMessage
         * @return
         */
        private PKIMessage generateRevokeReqData(RevocationReqMessage revocationReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = getPkiHeaderParams(revocationReqMessage);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generateRRPkiBody(revocationReqMessage.getIssuer(), revocationReqMessage.getCertSn(), revocationReqMessage.getReason());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }

        private PKIMessageGenerator.PKIHeaderParams getPkiHeaderParams(RevocationReqMessage revocationReqMessage) {
            return PKIMessageGenerator.PKIHeaderParams.newInstanceBuilder(revocationReqMessage)
                    .customFreeText(revocationReqMessage.getFreeText())
                    .build();
        }


        /**
         * 通过 {@link CmpMessage} 产生 cmp 证书挂失 的请求数据
         *
         * @param revocationReqMessage
         * @return
         */
        private PKIMessage generateSuspendedReqData(RevocationReqMessage revocationReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = getPkiHeaderParams(revocationReqMessage);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generateRRPkiBody(revocationReqMessage.getIssuer(), revocationReqMessage.getCertSn(), RevokeReasonEnum.CERTIFICATE_HOLD.getCode());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }


        /**
         * 通过 {@link CmpMessage} 产生 cmp 证书解挂 的请求数据
         *
         * @param revocationReqMessage
         * @return
         */
        private PKIMessage generateUnSuspendedReqData(RevocationReqMessage revocationReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = getPkiHeaderParams(revocationReqMessage);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generateRRPkiBody(revocationReqMessage.getIssuer(), revocationReqMessage.getCertSn(), RevokeReasonEnum.REMOVE_FROM_CRL.getCode());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }




    }

    final static class RevocationReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {

        public RevocationReqMessageDecoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage revokeRespPkiMessage) throws CmpDecodeException {
            checkDecodeArgs(context, revokeRespPkiMessage);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                if (singleCertReqContext.getCmpReqMessage() instanceof RevocationReqMessage) {
                    try {
                        dealWithRevocationRespPKIMessage(singleCertReqContext, revokeRespPkiMessage);
                        singleCertReqContext.setDecoded(true);
                    }catch (Exception e){
                        throw new CmpDecodeException(e.getMessage(), e);
                    }
                }
            }
        }


    }

}
