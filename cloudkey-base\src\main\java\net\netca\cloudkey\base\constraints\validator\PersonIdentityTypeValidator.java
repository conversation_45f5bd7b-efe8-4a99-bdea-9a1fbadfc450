package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.PersonIdentityType;
import net.netca.cloudkey.base.constraints.check.CheckType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class PersonIdentityTypeValidator implements ConstraintValidator<PersonIdentityType, Integer> {


    @Override
    public void initialize(PersonIdentityType constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (Objects.nonNull(value)) {
            isValid = CheckType.PERSONIDENTITYTYPE.check(value + "");
        }
        return isValid;
    }
}
