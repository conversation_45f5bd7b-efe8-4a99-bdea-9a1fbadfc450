package net.netca.cloudkey.base.dto.signature;

import lombok.Data;
import net.netca.cloudkey.base.dto.cert.Cert;
import net.netca.cloudkey.base.dto.superclass.UserToken;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * {
 * "userToken":"用户登录后后台返回的令牌",
 * "tbsHashAlgo":16834,
 * "tbs":"待签名数据（Base64不换行编码）",
 * "cert":{
 *          "keySN":"设备序列号",
 *          "keyType":"102,设备类行",
 *          "certSN":"证书序列号-大写",
 *          "issuer":"证书颁发者DN的16进制编码-大写",
 *          "certThumbPrints":[
 *              {
 *              "algorithm":16834,
 *              "hash":"证书微缩图16进制编码-大写"
 *              }
 *                     ]
 *            }
 *       }
 * <p>
 * <p>
 * created by zaj on 2019/1/16
 */
@Data
public class SignatureReqDTO extends UserToken {

    /**
     * 摘要算法
     */
    @NotNull(message = "tbsHashAlgo不能为空")
    private Integer tbsHashAlgo;
    /**
     * 签名原文
     */
    @NotBlank(message = "tbs不能为空")
    private String tbs;
}
