package net.netca.cloudkey.base.dto.inspection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 获取应用的院区名称
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDistrictNameReqDTO extends TimestampToken {

    @NotBlank(message = "AppId不能为空")
    private String appId;

}
