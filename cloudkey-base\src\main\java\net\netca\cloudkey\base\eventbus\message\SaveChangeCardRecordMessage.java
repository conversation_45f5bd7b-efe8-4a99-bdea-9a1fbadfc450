package net.netca.cloudkey.base.eventbus.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存换卡记录消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveChangeCardRecordMessage{

    public static final int SOURCE_TYPE_LOGIN = 1;
    public static final int SOURCE_TYPE_UNLOCK_PIN = 2;
    public static final int SOURCE_TYPE_CHANGE_PIN = 3;

    /**
     * SM3的用户PIN（Hex编码）
     */
    private String pinSM3Hash;

    /**
     * 证书ID
     */
    private Integer certId;

    /**
     * 来源，1：来自登录接口，2 来自修改PIN接口
     */
    private Integer sourceType;


    /**
     * 用户Token
     */
    private String userToken;

    /**
     * 用户签名密钥对数据
     */
    private byte[] userKeypairBytes;
}
