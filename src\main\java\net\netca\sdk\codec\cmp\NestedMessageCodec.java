package net.netca.sdk.codec.cmp;


import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.SignatureAlgorithmEnum;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.execption.CmpValidatingException;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.message.cmp.config.CompositeMessageConfigManagement;
import net.netca.sdk.message.cmp.NestedMessage;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

import java.util.HashMap;
import java.util.Map;

/**
 * {@link NestedMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/9 19:13
 */
public class NestedMessageCodec extends CmpMessageCodec {


    private NestedMessageCodec() {

    }

    private static NestedMessageEncoder encoder;


    private static NestedMessageDecoder decoder;

    public static NestedMessageCodec createEncoder(CompositeMessageConfigManagement configManagement) {
        encoder = new NestedMessageEncoder(configManagement);
        return new NestedMessageCodec();
    }

    public static NestedMessageCodec createDecoder(CompositeMessageConfigManagement configManagement) {
        decoder = new NestedMessageDecoder(configManagement);
        return new NestedMessageCodec();
    }


    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    final static class NestedMessageEncoder extends CmpMessageToAnyObjectEncoder {

        /**
         * 第三方操作员证书
         */
        private X509Certificate operatorCert;

        /**
         * 第三方操作员证书的私钥，用于签名
         */
        private PrivateKeyInfo operatorCertPrivateKeyInfo;

        /**
         * 第三方操作员证书签名所用的签名算法
         *
         * @see SignatureAlgorithmEnum
         */
        private String operatorCertSignAlgoName;


        /**
         * key: see {@link BusinessTypeEnum}
         * value: MessageEncoder
         */
        private Map<Integer, MessageEncoder<CmpMessage>> encoderMap = new HashMap<>();

        public NestedMessageEncoder(CompositeMessageConfigManagement configManagement) {
            super(configManagement);
            this.operatorCert = configManagement.getOperatorCert();
            this.operatorCertPrivateKeyInfo = configManagement.getOperatorCertPrivateKeyInfo();
            this.operatorCertSignAlgoName = configManagement.getCertSignAlgoName();
        }


        @Override
        public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
            checkEncodeArgs(context, message);
            if (context instanceof SingleCertReqContext && message instanceof NestedMessage) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                NestedMessage nestedMessage = (NestedMessage) message;
                CmpMessage cmpMessage = nestedMessage.getInlineMessage();
                BusinessTypeEnum businessTypeEnum = cmpMessage.getBusinessTypeEnum();
                MessageEncoder<CmpMessage> encoder;
                CmpMessageConfigManagement configManagement = CmpMessageConfigManagement.builder()
                        .thirdPartyServerCommCert(operatorCert)
                        .thirdPartyServerCommCertPrivateKeyInfo(operatorCertPrivateKeyInfo)
                        .thirdPartyServerCommCertSignAlgoName(operatorCertSignAlgoName).build();
                switch (businessTypeEnum) {
                    case P10_REGISTER:
                        if (encoderMap.containsKey(businessTypeEnum.getCode())) {
                            encoder = encoderMap.get(businessTypeEnum.getCode());
                        } else {
                            encoder = P10CertReqMessageCodec.createEncoder(configManagement);
                        }
                        break;
                    case RENEWAL:
                    case UPDATE_KEY:
                        encoder = KeyUpdateReqMessageCodec.createEncoder(configManagement);
                        break;
                    case RECOVERY:
                        encoder = KeyRecoveryReqMessageCodec.createEncoder(configManagement);
                        break;
                    case REVOKE:
                    case SUSPEND:
                    case UNSUSPEND:
                        encoder = RevocationReqMessageCodec.createEncoder(configManagement);
                        break;
                    default:
                        throw new CmpValidatingException("unknown business type, not support encode");
                }
                try {
                    doEncode(message, singleCertReqContext, cmpMessage, encoder);
                } catch (Exception e) {
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }

        private void doEncode(CmpMessage message, SingleCertReqContext singleCertReqContext, CmpMessage cmpMessage, MessageEncoder<CmpMessage> encoder) throws Exception {
            encoder.encode(singleCertReqContext, cmpMessage);
            PKIMessages inlineMessages = singleCertReqContext.getPKIMessages();
            PKIMessage nestedMessage = generateNestedMessage(message, inlineMessages);
            singleCertReqContext.setEncodeResult(nestedMessage);
        }

        /**
         * 创建外层 嵌套消息
         *
         * @param message
         * @param inlineMessages
         * @return {@link PKIMessage}
         * @throws Exception
         */
        private PKIMessage generateNestedMessage(CmpMessage message, PKIMessages inlineMessages) throws Exception {
            //sender为第三方系统通讯证书的主题
            //recipient为接收CA系统的通讯证书的主题项
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstance(message);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = new PKIBody(PKIBody.TYPE_NESTED, inlineMessages);
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }
    }


    final static class NestedMessageDecoder extends PKIMessageToCmpRespResultDecoder {

        public NestedMessageDecoder(CompositeMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
            checkDecodeArgs(context, message);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                BusinessTypeEnum businessTypeEnum = singleCertReqContext.getCmpReqMessage().getBusinessTypeEnum();
                MessageDecoder<PKIMessage> decoder;
                CmpMessageConfigManagement configManagement = CmpMessageConfigManagement.builder().communicationCert(communicationCert).build();
                switch (businessTypeEnum) {
                    case P10_REGISTER:
                        decoder = P10CertReqMessageCodec.createDecoder(configManagement);
                        break;
                    case RENEWAL:
                    case UPDATE_KEY:
                        decoder = KeyUpdateReqMessageCodec.createDecoder(configManagement);
                        break;
                    case RECOVERY:
                        decoder = KeyRecoveryReqMessageCodec.createDecoder(configManagement);
                        break;
                    case REVOKE:
                    case SUSPEND:
                    case UNSUSPEND:
                        decoder = RevocationReqMessageCodec.createDecoder(configManagement);
                        break;
                    default:
                        throw new CmpValidatingException("unknown business type, not support decode");
                }
                try {
                    decoder.decode(singleCertReqContext, message);
                } catch (Exception e) {
                    throw new CmpDecodeException(e.getMessage(), e);
                }
            }
        }
    }
}
