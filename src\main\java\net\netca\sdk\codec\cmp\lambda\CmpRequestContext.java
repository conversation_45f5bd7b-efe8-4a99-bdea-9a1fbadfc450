package net.netca.sdk.codec.cmp.lambda;

import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;

/**
 * @author: zys
 * @date: 2020/3/3 17:50
 */
public abstract class CmpRequestContext implements RequestContext {
    CmpMessageConfigManagement cmpMessageConfigManagement;

    public CmpRequestContext(CmpMessageConfigManagement cmpMessageConfigManagement) {
        this.cmpMessageConfigManagement = cmpMessageConfigManagement;
    }

    public CmpMessageConfigManagement getCmpMessageConfigManagement() {
        return cmpMessageConfigManagement;
    }


    public void destroy(){
        cmpMessageConfigManagement = null;
    }
}
