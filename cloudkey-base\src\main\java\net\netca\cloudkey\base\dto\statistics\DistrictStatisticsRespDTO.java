package net.netca.cloudkey.base.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Description 院区首页统计查询
 * @Date 16:47 2020/9/4
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistrictStatisticsRespDTO {


    /**
     * 有证书无签章
     */
    private Integer userWithCertWithoutSeal;

    /**
     * 有证书有签章
     */
    private Integer userWithCertWithSeal;

    /**
     * 无证书
     */
    private Integer userWithoutCert;

    /**
     * 申请中
     */
    private Integer userWithApplying;

    /**
     * 本月内过期
     */
    private Integer expireWithinThisMonth;

    /**
     * 三月内过期
     */
    private Integer expireWithinThreeMonth;

    /**
     * 六月内过期
     */
    private Integer expireWithinSixMonth;

    /**
     * 已申请证书总量
     */
    private Integer totalCertApplyDone;

    /**
     * 已注销证书总量
     */
    private Integer totalCertRevoke;

    /**
     * 7天内第三方接口日志成功的条数
     */
    private Integer thirdSystemSuccessWithin7Days;

    /**
     * 7天内第三方接口日志失败的条数
     */
    private Integer thirdSystemErrorWithin7Days;

}
