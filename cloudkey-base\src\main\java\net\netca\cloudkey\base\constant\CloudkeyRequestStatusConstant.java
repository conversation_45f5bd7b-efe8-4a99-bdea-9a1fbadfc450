package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 云密钥业务状态
 */
public enum CloudkeyRequestStatusConstant implements IBaseEnum {
	/**-1	全部类型 */
	REQ_STATUS_FAIL(-1, "失败"),
	REQ_STATUS_SUCCESS(0, "成功"),
	REQ_STATUS_APPLY_CERT_WAIT_APPROVE(1, "待审核"),
	REQ_STATUS_NOTPASS(2, "不通过"),
	REQ_STATUS_CLOSE(3, "关闭");


	private int code;
	private String description;

	private CloudkeyRequestStatusConstant(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

	public static String getDescByCode(Integer code) {
		for (CloudkeyRequestStatusConstant value : CloudkeyRequestStatusConstant.values()) {
			if (value.getCode().equals(code)) {
				return value.getDescription();
			}
		}
		return null;
	}

}
