package net.netca.sdk.codec.cmp;


import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.message.cmp.CertConfirmReqMessage;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

/**
 * {@link CertConfirmReqMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/9 17:15
 */
public class CertConfirmReqMessageCodec extends CmpMessageCodec {

    private static CertConfirmReqMessageEncoder encoder;


    private static CertConfirmReqMessageDecoder decoder;

    private CertConfirmReqMessageCodec() {
    }

    public static CertConfirmReqMessageCodec createDecoder(CmpMessageConfigManagement configManagement) {
        decoder = new CertConfirmReqMessageDecoder(configManagement);
        return new CertConfirmReqMessageCodec();
    }


    public static CertConfirmReqMessageCodec createEncoder(CmpMessageConfigManagement configManagement) {
        encoder = new CertConfirmReqMessageEncoder(configManagement);
        return new CertConfirmReqMessageCodec();
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }


    final static class CertConfirmReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {

        public CertConfirmReqMessageDecoder(CmpMessageConfigManagement cmpMessageConfigManagement) {
            super(cmpMessageConfigManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage decodedMsg) throws CmpDecodeException {
            checkDecodeArgs(context, decodedMsg);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                if (singleCertReqContext.getCmpReqMessage() instanceof CertConfirmReqMessage) {
                    // todo CertConfirmReqMessageDecoder
                    singleCertReqContext.setDecoded(true);
                }
            }
        }
    }

    final static class CertConfirmReqMessageEncoder extends CmpMessageToAnyObjectEncoder {

        CertConfirmReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }


        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws CmpEncodeException {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof CertConfirmReqMessage) {
                CertConfirmReqMessage certConfirmReqMessage = (CertConfirmReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                try {
                    singleCertReqContext.setEncodeResult(generateCertConfirmReqData(certConfirmReqMessage));
                } catch (Exception e) {
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }


        /**
         * 通过 {@link CmpMessage} 产生 cmp 证书确认的请求数据 {@link PKIMessage}
         *
         * @param certConfirmReqMessage
         * @return
         */
        private PKIMessage generateCertConfirmReqData(CertConfirmReqMessage certConfirmReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstance(certConfirmReqMessage);
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generateCertConfReq(certConfirmReqMessage.getCertRequestId());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }



    }


}
