package net.netca.cloudkey.base.dto.cert;


import lombok.ToString;
import net.netca.cloudkey.base.exception.CodecFailException;
import net.netca.cloudkey.base.util.CodecUtil;

@ToString
public class ResUserCertDTO {

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 证书用法
     * @see CertUsageEnum
     */
    private Integer certUsage;
    /**
     * 用户证书内容Base64编码
     */
    private byte[] certContents;

    private Integer certId;

    private String projectUid;

    public Integer getCertId() {
        return certId;
    }

    public void setCertId(Integer certId) {
        this.certId = certId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCertContents()  {
        if(certContents == null) return null;
        try {
            return CodecUtil.base64Encode(certContents);
        } catch (CodecFailException e) {
            return null;
        }
    }

    public void setCertContents(byte[] certContents) {
        this.certContents = certContents;
    }

    public Integer getCertUsage() {
        return certUsage;
    }

    public void setCertUsage(Integer certUsage) {
        this.certUsage = certUsage;
    }

    public String getProjectUid() {
        return projectUid;
    }

    public void setProjectUid(String projectUid) {
        this.projectUid = projectUid;
    }
}
