package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成
 */
@Getter
@AllArgsConstructor
public enum CoordSignSignerSignStatusEnum implements IBaseEnum {

    /**
     * 未签名
     */
    NOT_SIGNED(0, "未签名"),

    /**
     * 已拒绝
     */
    REJECTED(2, "已拒绝"),

//    /**
//     * 已撤回
//     */
//    WITHDRAWN(4, "已撤回"),
    /**
     * 已完成
     */
    COMPLETED(5, "已完成");

    private final Integer code;
    private final String description;



}
