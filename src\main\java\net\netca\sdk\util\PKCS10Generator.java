package net.netca.sdk.util;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1TypeManager;
import net.netca.pki.encoding.asn1.SetOf;
import net.netca.pki.encoding.asn1.SetOfType;
import net.netca.pki.encoding.asn1.pki.*;
import net.netca.sdk.constants.AttributeEncodeEnum;
import net.netca.sdk.constants.AttributeEnum;
import net.netca.sdk.constants.SignatureAlgorithmEnum;
import net.netca.sdk.execption.NETCACryptoException;
import net.netca.sdk.message.SubjectInfo;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @create 2019-07-08 19:21
 */
public abstract class PKCS10Generator {

    /**
     * 生成P10
     *
     * @param publicKeyEncode
     * @param signer
     * @param signAlgoName
     * @param subject
     * @return
     * @throws PkiException
     * @throws NETCACryptoException
     */
    public static byte[] generateP10(byte[] publicKeyEncode, Signable signer, String signAlgoName, SubjectInfo subject) throws PkiException, NETCACryptoException {
        SignatureAlgorithmEnum signatureAlgorithmEnum = SignatureAlgorithmEnum.getByName(signAlgoName);
        return doGenerateP10(publicKeyEncode, signer, subject, signatureAlgorithmEnum.getCode());
    }


    /**
     * 生成P10
     *
     * @param publicKeyEncode 公钥
     * @param signer
     * @param algorithmOid 算法的OID
     * @param subject {@link SubjectInfo} 主题项
     * @return
     * @throws PkiException
     */
    public static byte[] generateP10(byte[] publicKeyEncode, Signable signer, SubjectInfo subject, String algorithmOid) throws PkiException {
        return doGenerateP10(publicKeyEncode, signer, subject, algorithmOid);
    }


    private static byte[] doGenerateP10(byte[] publicKeyEncode, Signable signer, SubjectInfo subject, String algorithmOid) throws PkiException {
        AlgorithmIdentifier algorithmIdentifier = AlgorithmIdentifier.CreateAlgorithmIdentifier(algorithmOid);
        SubjectPublicKeyInfo subjectPublicKeyInfo = new SubjectPublicKeyInfo(publicKeyEncode);
        CertificationRequestBuilder certRequestBuilder = new CertificationRequestBuilder();
        certRequestBuilder.setSubjectPublicKeyInfo(subjectPublicKeyInfo);
        certRequestBuilder.setSubject(generateX500Name(generateAttributeTypeAndValueConverters(subject)));

        CertificationRequest certRequest = certRequestBuilder.sign(signer, algorithmIdentifier);
        return certRequest.derEncode();
    }

    private static TreeSet<AttributeTypeAndValueConverter> generateAttributeTypeAndValueConverters(SubjectInfo subject) {
        Objects.requireNonNull(subject);
        TreeSet<AttributeTypeAndValueConverter> attributeTypeAndValueConverters = new TreeSet<>();
        for (String fieldName : SubjectInfoHelper.getFieldNames()) {
            String getMethodName = "get" + StringUtils.capitalize(fieldName);
            if (SubjectInfoHelper.getSubjectGetterMap().containsKey(getMethodName)){
                Method method = SubjectInfoHelper.getSubjectGetterMap().get(getMethodName);
                String value;
                try {
                    value = (String) method.invoke(subject);
                    if (StringUtils.isBlank(value)) {
                        continue;
                    }
                } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                    continue;
                }
                attributeTypeAndValueConverters.add(AttributeTypeAndValueConverter.newInstance(fieldName, value));
            }
        }
        return attributeTypeAndValueConverters;
    }

    /**
     * 根据 {@link SubjectInfo} 生成 {@link X500Name}
     * @param subjectInfo {@link SubjectInfo} 主题项
     * @return {@link X500Name}
     * @throws PkiException
     */
    public static X500Name generateX500Name(SubjectInfo subjectInfo) throws PkiException {
        return generateX500Name(generateAttributeTypeAndValueConverters(subjectInfo));
    }

    private static X500Name generateX500Name(TreeSet<AttributeTypeAndValueConverter> attributeTypeAndValueConverters) throws PkiException {

        X500Name subject = new X500Name();
        for (AttributeTypeAndValueConverter attributeTypeAndValueConverter : attributeTypeAndValueConverters) {
            SetOf setOf = new SetOf((SetOfType) ASN1TypeManager.getInstance().get("RelativeDistinguishedName"));
            setOf.add(attributeTypeAndValueConverter.convert().getASN1Object());
            RelativeDistinguishedName rdn = new RelativeDistinguishedName(setOf);
            subject.add(rdn);
        }
        return subject;
    }


    @AllArgsConstructor
    @Getter
    @EqualsAndHashCode
    static class AttributeTypeAndValueConverter implements Comparable<AttributeTypeAndValueConverter> {
        private final String oid;
        private final AttributeEncodeEnum encode;
        private final String value;
        private final int order;

        public static AttributeTypeAndValueConverter newInstance(String type, String value) {
            AttributeEnum attribute = AttributeEnum.getAttribute(type);
            if (Objects.isNull(attribute)) {
                throw new IllegalArgumentException("Invalid type: " + type);
            }
            return new AttributeTypeAndValueConverter(attribute.getOid(), attribute.getEncode(), value, attribute.getOrder());
        }

        public AttributeTypeAndValue convert() throws PkiException {
            return new AttributeTypeAndValue(oid, encode.convert2ASN1Object(value));
        }

        @Override
        public int compareTo(AttributeTypeAndValueConverter o) {
            if (Objects.isNull(o)) {
                return -1;
            }
            return this.order - o.getOrder();
        }
    }

}
