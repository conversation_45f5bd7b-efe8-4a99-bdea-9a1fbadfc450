package net.netca.sdk.message.cmp;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.Validity;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * 证书变更的消息体（主题项信息不能为空，证书有效期可选）
 * <AUTHOR>
 * @date 2020-04-22 16:53
 */
@SuperBuilder(toBuilder = true)
@Getter
public class ModifiedCertReqMessage extends KeyUpdateReqMessage {

    /**
     * 证书有效期
     */
    private Validity validity;

    /**
     * 主题项信息
     */
    private SubjectInfo subject;

    /**
     * 默认 {@link ModifiedCertReqMessage}
     * @return {@link ModifiedCertReqMessage}
     */
    public static ModifiedCertReqMessage defaultMessage() {
        return ModifiedCertReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.MODIFICATION)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
