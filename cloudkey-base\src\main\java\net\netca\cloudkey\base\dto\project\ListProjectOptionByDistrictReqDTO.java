package net.netca.cloudkey.base.dto.project;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * @Date Created in 14:55 2024/3/6
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListProjectOptionByDistrictReqDTO {

    /**
     * 院区ID列表
     */
    private List<Integer> districtIds;
}
