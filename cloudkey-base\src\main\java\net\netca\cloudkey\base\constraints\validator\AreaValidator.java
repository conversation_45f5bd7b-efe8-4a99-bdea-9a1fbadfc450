package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.Area;
import net.netca.cloudkey.base.dto.abstractclass.IAreaReqDTO;
import net.netca.cloudkey.base.util.AreaUtil;
import net.netca.cloudkey.base.util.SpringContextUtil;
import net.netca.cloudkey.base.util.CustomStringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class AreaValidator implements ConstraintValidator<Area, IAreaReqDTO> {

    @Override
    public void initialize(Area constraintAnnotation) {

    }

    @Override
    public boolean isValid(IAreaReqDTO value, ConstraintValidatorContext context) {

        AreaUtil areaUtil = (AreaUtil) SpringContextUtil.getBean("areaUtil");

        boolean isValid = true;

        String countryName = value.getCountryName();
        String province = value.getProvince();
        String city = value.getCity();
        if (!CustomStringUtils.isBlank(countryName)) {
            // 只当需要校验的值为空时，才进行校验
            if (!Objects.equals(countryName, "CN")) {
                isValid = false;
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("国家值非法，值只能是CN").addConstraintViolation();
                return isValid;
            }
        }

        if (!CustomStringUtils.isBlank(province)) {
            boolean provinceCorrect = areaUtil.isProvinceCorrect(province);
            if (!provinceCorrect) {
                isValid = false;
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("省份值非法").addConstraintViolation();
                return isValid;
            }
        }

        if (!CustomStringUtils.isBlank(city)) {
            boolean cityCorrect = areaUtil.isCityCorrect(city);
            if (!cityCorrect) {
                isValid = false;
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("城市值非法").addConstraintViolation();
                return isValid;
            }


            if (!CustomStringUtils.isBlank(province)) {
                boolean cityBelongProvince = areaUtil.isCityBelongProvince(province, city);
                if (!cityBelongProvince) {
                    isValid = false;
                    context.disableDefaultConstraintViolation();
                    context.buildConstraintViolationWithTemplate("城市不属于该省份").addConstraintViolation();
                    return isValid;
                }
            }
        }

        return isValid;
    }
}
