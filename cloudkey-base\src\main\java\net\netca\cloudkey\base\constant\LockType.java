package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.po.BusinessCert;
import net.netca.cloudkey.base.po.BusinessUser;

/**
 * @descriptions:
 * @date: 2019/7/22
 * @author: <PERSON>romChars
 */
public enum LockType implements IBaseEnum {

    LOCK_BUSINESS_USER(0, "证书用户锁定", "LOCK_BUSINESS_USER", BusinessUser.class),
    LOCK_WECHAT_ACCESSTOKEN(1, "wechat token 排他锁定", "LOCK_WECHAT_ACCESSTOKEN", null),
    LOCK_BUSINESS_CERT_DOWNLOAD(2, "下载用户证书锁定", "LOCK_BUSINESS_CERT_DOWNLOAD", BusinessCert.class),
    LOCK_CORP_WECHAT_ACCESS_TOKEN(3, "企业微信 token 排他锁定", "LOCK_CORP_WECHAT_ACCESS_TOKEN", null)
    ;

    private Integer code;
    private String description;
    private String lockPrefix;
    private Class transFormClass;

    LockType(Integer code, String description, String lockPrefix, Class aClass) {
        this.code = code;
        this.description = description;
        this.lockPrefix = lockPrefix;
        this.transFormClass = aClass;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public Class getTransFormClass() {
        return transFormClass;
    }

    public String getLockPrefix() {
        return lockPrefix;
    }
}
