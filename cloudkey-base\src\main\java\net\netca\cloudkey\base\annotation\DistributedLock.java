package net.netca.cloudkey.base.annotation;

import net.netca.cloudkey.base.constant.LockType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @descriptions: 分布式锁
 * 防止重复提交，适用于参数限定
 * @date: 2019/7/22
 * @author: SaromChars
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {

    LockType value();

    int waitTime() default 5;

    int leaseTime() default 60;

}
