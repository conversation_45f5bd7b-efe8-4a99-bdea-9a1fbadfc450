package net.netca.cloudkey.base.constant;

/**
 * @descriptions:
 * 签章图片存储类型
 * @date: 2019/6/17
 * @author: cxy
 */
public enum SealFileStoreTypeEnum {

    BASE64_CONTENT(1, "base64编码文本存储")
    , FILE_URL(2, "文件存储")
    ;

    private Integer code;
    private String description;

    SealFileStoreTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
