package net.netca.sdk.constants;


import java.util.ArrayList;
import java.util.Arrays;

public enum KeyPairAlgorithmEnum implements IBaseEnum, IEnumHasChildrens {
    RSA(0, "RSA",
            KeyPairSupportLenEnum.LENGTH1024,
            KeyPairSupportLenEnum.LENGTH2048,
            KeyPairSupportLenEnum.LENGTH4096
    ),
    ECC(1, "ECC", KeyPairSupportLenEnum.LENGTH256),
    SM2(2, "SM2", KeyPairSupportLenEnum.LENGTH256);

    private KeyPairAlgorithmEnum(Integer code, String description, KeyPairSupportLenEnum... supportLength) {
        this.code = code;
        this.description = description;
        this.supportLength.addAll(Arrays.asList(supportLength));
    }

    private Integer code;
    private String description;
    private ArrayList<IBaseEnum> supportLength = new ArrayList<>(); //支持的密钥长度

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public ArrayList<IBaseEnum> getSupportLength() {
        return supportLength;
    }

    @Override
    public ArrayList<IBaseEnum> getChildren() {
        return supportLength;
    }

    public static KeyPairAlgorithmEnum getByCode(Integer code) {
        for (KeyPairAlgorithmEnum examType : KeyPairAlgorithmEnum.values()) {
            if (code.equals(examType.getCode())) {
                return examType;
            }
        }
        throw new RuntimeException(String.format("不支持code=%d的类型", code));
    }
}
