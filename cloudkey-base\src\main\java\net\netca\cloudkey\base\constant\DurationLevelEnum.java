package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 耗时区间
 */
public enum DurationLevelEnum implements IBaseEnum {

    LEVEL_LT_500MS(1, "0ms-500ms"),
    LEVEL_500MS_1S(2, "500ms-1s "),
    LEVEL_1S_5S(3, "1s-5s"),
    LEVEL_5S_10S(4, "5s-10s"),
    LEVEL_GT_10S(5, "10s-"),

    ;

    private Integer code;
    private String description;

    private DurationLevelEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public static int getDurationLevelEnumByDuration(int duration) {

        if (duration <= 500) {
            return DurationLevelEnum.LEVEL_LT_500MS.getCode();
        } else if (duration <= 1000) {
            return DurationLevelEnum.LEVEL_500MS_1S.getCode();
        } else if (duration <= 5000) {
            return DurationLevelEnum.LEVEL_1S_5S.getCode();
        } else if (duration <= 10000) {
            return DurationLevelEnum.LEVEL_5S_10S.getCode();
        } else {
            return DurationLevelEnum.LEVEL_GT_10S.getCode();
        }
    }
}
