package net.netca.cloudkey.base.dto.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PastOrPresent;
import java.util.Date;

@Data
@NoArgsConstructor
public class AuditAppLogSearchDTO {

    private Integer districtId;
    private String appId;
    private Integer userType;
    private String userName;
    private String memo;
    private Integer durationLevel;
    private String description;
    private Integer success;

    // V2.41 增加查询条件
    /**
     * 订单流水id
     */
    private String orderId;

    /**
     * 患者id
     */
    private String patientId;

    @NotNull(message = "日志时间范围不允许为空")
    @PastOrPresent(message = "日志时间范围仅允许为历史时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @NotNull(message = "日志时间范围不允许为空")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
