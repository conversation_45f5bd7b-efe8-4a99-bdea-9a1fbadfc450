package net.netca.test.asn1.pojo;

import net.netca.test.exception.ExtensionFormatException;

/**
 * <AUTHOR>
 * @date 2020-03-23 11:50
 */
public class TNEntry {

    public static final String NAME_SPC = "spc";
    public static final String NAME_RANGE = "range";
    public static final String NAME_ONE = "one";

    private String spc;
    private TelephoneNumberRange range;
    private String one;

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public TelephoneNumberRange getRange() {
        return range;
    }

    public void setRange(TelephoneNumberRange range) {
        this.range = range;
    }

    public String getOne() {
        return one;
    }

    public void setOne(String one) {
        this.one = one;
    }

    public void validate() throws ExtensionFormatException {
        boolean onlyHasSpc = spc != null && range == null && one == null;
        boolean onlyHasRange = spc == null && range != null && one == null;
        boolean onlyHasOne = spc == null && range == null && one != null;
        if (!onlyHasSpc && !onlyHasRange && !onlyHasOne) {
            throw new ExtensionFormatException("TNEntry should not contain multiple item.");
        }

        if (onlyHasRange) {
            range.validate();
        }

        if (onlyHasOne) {
            TelephoneNumber.validate(one);
        }
    }

    @Override
    public String toString() {
        return "TNEntry{" +
                "spc='" + spc + '\'' +
                ", range=" + range +
                ", one='" + one + '\'' +
                '}';
    }
}
