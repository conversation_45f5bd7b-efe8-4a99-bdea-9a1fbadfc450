package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 签章图片审核常量类
 */
public enum SealPicReviewEnum implements IBaseEnum {

    AUTO_REVIEW(0, "自动审核"),
    MANUAL_REVIEW(1, "人工审核"),
    MANUAL_REVIEW_IGNORE_FIRST(2, "仅初次审核自动通过");
    ;

    private Integer code;
    private String description;

    SealPicReviewEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
