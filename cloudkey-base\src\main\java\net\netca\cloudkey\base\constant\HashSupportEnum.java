package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.pki.Hash;

import java.util.Arrays;
import java.util.Optional;

/**
 * @descriptions: 基于中间件的，Hash算法的编号
 * @date: 2019/6/13
 * @author: cxy
 */
public enum HashSupportEnum implements IBaseEnum {

    SHA1(Hash.SHA1, "SHA1"),
    SHA256(Hash.SHA256, "SHA256"),
    SHA384(Hash.SHA384, "SHA384"),
    SHA512(Hash.SHA512, "SHA512"),
    SM3(Hash.SM3, "SM3");

    private Integer code;
    private String description;

    HashSupportEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public static HashSupportEnum getHashAlgoByCode(Integer code) {
        Optional<HashSupportEnum> first = Arrays.stream(HashSupportEnum.values())
                .filter(hashSupportEnum -> hashSupportEnum.code.equals(code))
                .findFirst();


        if (!first.isPresent()) {
            throw new CloudKeyRuntimeException(WebResultEnum.ERROR.getCode(), "非法的hash算法编号");
        }

        return first.get();
    }
}
