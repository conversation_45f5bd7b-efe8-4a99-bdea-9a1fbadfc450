package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.OrganizationIdentityType;
import net.netca.cloudkey.base.constraints.check.CheckType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class OrganizationIdentityTypeValidator implements ConstraintValidator<OrganizationIdentityType, Integer> {


    @Override
    public void initialize(OrganizationIdentityType constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (Objects.nonNull(value)) {
            isValid = CheckType.ORGIDENTITYTYPE.check(value + "");
        }
        return isValid;
    }
}
