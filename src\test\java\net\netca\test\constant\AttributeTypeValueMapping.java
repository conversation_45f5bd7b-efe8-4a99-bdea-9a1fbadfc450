package net.netca.test.constant;

import net.netca.sdk.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

public class AttributeTypeValueMapping {

	private AttributeTypeValueMapping(){}

	/**
	 * CN ==> 2.5.4.3
	 */
	private final static Map<String, String> attributeType2Oid;
	/**
	 * 2.5.4.3 ==> CN
	 */
	private final static Map<String, String> oid2AttributeType;

	/**
	 * 2.5.4.3 ==> UTF8String
	 */
	private final static Map<String, String> attributeOid2EncodeType;


	static {
		attributeType2Oid = CertSubjectAttributeTypeEnum.getAttributeType2Oid();

		oid2AttributeType = CertSubjectAttributeTypeEnum.getOid2AttributeType();

		attributeOid2EncodeType = CertSubjectAttributeTypeEnum.getAttributeOid2EncodeType();
	}

	public static String getOidFromAttributeType(String type){
		String value = attributeType2Oid.get(type);
		if(StringUtils.isBlank(value)){
			return type;
		}
		return value;
	}
	
	public static String getAttributeTypeFromOid(String oid){
		String value = oid2AttributeType.get(oid);
		if(StringUtils.isBlank(value)){
			return oid;
		}
		return value;
	}

	//转换成key="",value=""的map，方便前端获取
	public static ArrayList<LinkedHashMap<String, String>> toKVMap(){
		ArrayList<LinkedHashMap<String, String>> result = new ArrayList<>();
		if(attributeType2Oid != null){
			for(Entry<String, String> entry : attributeType2Oid.entrySet()){
				if ("EMAIL".equals(entry.getKey()) || "ST".equals(entry.getKey())) {
					continue;
				}
				LinkedHashMap<String, String> map = new LinkedHashMap<>();
				map.put("key", entry.getKey());
				map.put("value", entry.getValue());
				result.add(map);
			}
		}
		return result;
	}
	
	public static String getEncodeTypeFromOid(String oid){
		return attributeOid2EncodeType.get(oid);
	}
	
	public static ArrayList<LinkedHashMap<String, String>> getAttributeOid2EncodeTypeToKVMap() {
		ArrayList<LinkedHashMap<String, String>> result = new ArrayList<>();
		if(attributeOid2EncodeType != null){
			for(Entry<String, String> entry : attributeOid2EncodeType.entrySet()){
				LinkedHashMap<String, String> map = new LinkedHashMap<>();
				map.put("key", entry.getKey());
				map.put("value", entry.getValue());
				result.add(map);
			}
		}
		return result;
	}

	public static boolean hasKeyInAttributeType2Oid(String key){
		return attributeType2Oid.containsKey(key);
	}

}
