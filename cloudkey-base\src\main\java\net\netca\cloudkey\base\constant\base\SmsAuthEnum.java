package net.netca.cloudkey.base.constant.base;

/**
 * <AUTHOR>
 * @Description  被授权人信息短信发送格式
 * @Date 16:21 2021/3/29
 **/
public enum SmsAuthEnum implements IBaseEnum {

    NAME(0, "被授权人姓名"),
    NAME_UID(1, "被授权人姓名(工号)");

    private Integer code;
    private String description;

    private SmsAuthEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }





}
