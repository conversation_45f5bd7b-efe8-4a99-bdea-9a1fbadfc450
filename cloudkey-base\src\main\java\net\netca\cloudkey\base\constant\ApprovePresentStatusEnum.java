package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @Description  审核任务的当前状态,方便筛选
 * @Date 16:03 2020/12/9
 **/
public enum ApprovePresentStatusEnum implements IBaseEnum {

    APPROVE_FAIL(-1,"审核不通过"),
    APPROVE_FINISH(-0,"审核完成"),
    APPROVE_WAIT_FIRST(1,"待初审"),
    APPROVE_WAIT_SECOND(2,"待复审");

    private Integer code;
    private String description;

    private ApprovePresentStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        for (ApprovePresentStatusEnum approvePresentStatusEnum : values()) {
            if(approvePresentStatusEnum.getCode() == code){
                obj = approvePresentStatusEnum.getDescription();
            }
        }
        return obj.toString();
    }
}
