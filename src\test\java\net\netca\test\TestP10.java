package net.netca.test;

import net.netca.pki.PkiException;
import net.netca.test.cert.TestCmpProtocol;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-07-30 16:35
 */
public class TestP10 extends TestCmpProtocol {
    @Test
    public void testRSAP10() throws PkiException {
        System.out.println(generateRSAP10());
    }


    @Test
    public void testSM2P10() throws PkiException {
        System.out.println(generateSM2P10());
    }
}
