package net.netca.cloudkey.base.constant;

import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备状态
 * @see StatusConstant 以前用的是这个 但是后面新增了状态，所以新建了一个 建议code不要轻易修改
 **/
@Getter
public enum DeviceStatusEnum implements IBaseEnum {

    NORMAL(0,"正常"),
    FREEZE(1,"冻结"),
    DELETE(2,"删除"),
    INVALID(3,"失效"),
    APPLY_UNFREEZE(4,"申请激活"),
    ;


    private final Integer code;
    private final String description;

    DeviceStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DeviceStatusEnum getByCode(Integer code) {
        for (DeviceStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Integer> getCodeList() {
        return Arrays.stream(DeviceStatusEnum.values())
                .map(DeviceStatusEnum::getCode)
                .collect(Collectors.toList());
    }

}
