package net.netca.cloudkey.base.constant.base;

/**
 * 允许自动审核常量类
 */
public enum AllowApiUploadSealPicEnum implements IBaseEnum {

    //WARN!!!!
    // 使用小写描述图片类型
    NOT_ALLOW(0, "不允许"),
    ALLOW(1, "允许"),
    ;

    private final Integer code;
    private final String description;

    private AllowApiUploadSealPicEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
