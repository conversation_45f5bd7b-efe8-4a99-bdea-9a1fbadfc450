package net.netca.sdk.constants;

/**
 * 证书注销原因常量
 * <AUTHOR>
 */
public enum RevokeReasonEnum implements IBaseEnum, IEnumFilter {
    UNSPECIFIED(0, "未指定(unspecified)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    KEY_COMPROMISE(1, "密钥丢失(keyCompromise)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    CA_COMPROMISE(2, "CA妥协(cACompromise)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    AFFILIATION_CHANGED(3, "从属关系已改变(affiliationChanged)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    SUPERSEDED(4, "被取代(superseded)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    CESSATION_OF_OPERATION(5, "停止使用(cessationOfOperation)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    CERTIFICATE_HOLD(6, "certificateHold") {
        @Override
        public boolean filter() {
            return true;
        }
    },
    REMOVE_FROM_CRL(8, "removeFromCRL") {
        @Override
        public boolean filter() {
            return true;
        }
    },
    PRIVILEGE_WITHDRAWN(9, "特权撤销(privilegeWithdrawn)") {
        @Override
        public boolean filter() {
            return false;
        }
    },
    A_A_COMPROMISE(10, "aa妥协(aACompromise)") {
        @Override
        public boolean filter() {
            return false;
        }
    };

    RevokeReasonEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static boolean contain(Integer reasonCade) {
        if (!UNSPECIFIED.getCode().equals(reasonCade) &&
                !KEY_COMPROMISE.getCode().equals(reasonCade) &&
                !CA_COMPROMISE.getCode().equals(reasonCade) &&
                !AFFILIATION_CHANGED.getCode().equals(reasonCade) &&
                !SUPERSEDED.getCode().equals(reasonCade) &&
                !CESSATION_OF_OPERATION.getCode().equals(reasonCade) &&
                !CERTIFICATE_HOLD.getCode().equals(reasonCade) &&
                !PRIVILEGE_WITHDRAWN.getCode().equals(reasonCade) &&
                !REMOVE_FROM_CRL.getCode().equals(reasonCade) &&
                !A_A_COMPROMISE.getCode().equals(reasonCade)
        ) {
            return false;
        }

        return true;
    }
}
