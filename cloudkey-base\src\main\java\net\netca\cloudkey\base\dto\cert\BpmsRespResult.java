package net.netca.cloudkey.base.dto.cert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * @Description 业务平台请求的结果
 * @Date Created in 16:27 2024/1/17
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmsRespResult {

    private Integer code;
    private String message;

    private CertContentInfoDTO data;

    /**
     * 业务失败了
     */
    public static final Integer FAIL_CODE = -1;

    /**
     * 业务完整的成功，返回证书
     */
    public static final Integer COMPLETE_SUCCESS_CODE = 0;
    /**
     * 业务受理成功，但是证书未生成
     */
    public static final Integer AFFIRM_SUCCESS_CODE = 1;

    /**
     * 业务完整的成功，返回证书
     * @param data
     * @return
     */
    public static BpmsRespResult completeSuccess(CertContentInfoDTO data,String tipMsg){
        return BpmsRespResult.builder()
                .code(COMPLETE_SUCCESS_CODE)
                .message(Optional.ofNullable(tipMsg).orElse("成功"))
                .build();
    }

    /**
     * 业务受理成功，但是证书未生成
     * @param tipMsg 提示信息
     * @return
     */
    public static BpmsRespResult affirmSuccess(String tipMsg){
        return BpmsRespResult.builder()
                .code(AFFIRM_SUCCESS_CODE)
                .message(Optional.ofNullable(tipMsg).orElse("成功"))
                .build();
    }

    /**
     * 业务失败
     * @param tipMsg 提示信息
     * @return
     */
    public static BpmsRespResult fail(String tipMsg){
        return BpmsRespResult.builder()
                .code(FAIL_CODE)
                .message(tipMsg)
                .build();
    }

    /**
     * 是否是业务完整的成功，返回证书
     * @return
     */
    public boolean isCompleteSuccess(){
        return COMPLETE_SUCCESS_CODE.equals(this.getCode());
    }

    /**
     * 业务受理成功，但是证书未生成
     * @return
     */
    public boolean isAffirmSuccess(){
        return AFFIRM_SUCCESS_CODE.equals(this.getCode());
    }


    /**
     * 业务受理成功 或者 业务完整的成功，返回证书
     * @return
     */
    public boolean isOk(){
        return AFFIRM_SUCCESS_CODE.equals(this.getCode()) || COMPLETE_SUCCESS_CODE.equals(this.getCode());
    }
}
