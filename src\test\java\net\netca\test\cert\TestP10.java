package net.netca.test.cert;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.Base64;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-07-30 16:35
 */
public class TestP10 extends TestCmpProtocol {
    @Test
    public void testRSAP10() throws PkiException {
        System.out.println(generateRSAP10());
    }


    @Test
    public void testSM2P10() throws PkiException {
        System.out.println(generateSM2P10());
    }

    @Test
    public void check() throws PkiException {
        String check = "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";
        PKIMessage instance = PKIMessage.getInstance(Base64.decode(false, check));

    }
}
