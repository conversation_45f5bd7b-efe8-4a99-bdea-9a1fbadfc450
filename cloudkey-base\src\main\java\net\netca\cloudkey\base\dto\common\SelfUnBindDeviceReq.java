package net.netca.cloudkey.base.dto.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.netca.cloudkey.base.dto.abstractclass.IUserToken;
import net.netca.cloudkey.base.dto.superclass.UserToken;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 自助解绑设备请求
 *
 * <AUTHOR>
 * @date 2021-10-28 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelfUnBindDeviceReq extends UserToken implements IUserToken {
    @NotBlank(message = "phoneValidCode参数不能为空")
    private String phoneValidCode;

    List<String> accessoryDeviceIds;
}
