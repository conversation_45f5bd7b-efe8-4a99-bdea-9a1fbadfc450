---
inclusion: always
---

# CloudKey 项目开发规范

## 语言与文档规范
- **代码注释**: 所有Java类、方法、字段的注释必须使用中文
- **文档编写**: 项目文档、README、配置说明等均使用中文
- **AI助手交互**: Kiro的回复应以中文为主，技术术语可保留英文
- **变量命名**: 使用英文驼峰命名，但注释说明使用中文

## 架构模式遵循
- **模块依赖**: 避免循环依赖，公共工具类放在 cloudkey-base 模块
- **日志记录**: 使用SLF4J + Log4j2，关键操作必须记录日志

## 数据库操作规范
- **ORM使用**: 优先使用MyBatis Plus，复杂查询可使用原生SQL
- **事务管理**: 使用Spring声明式事务，明确事务边界
- **缓存策略**: 使用Redis缓存热点数据，设置合理的过期时间

## 接口设计规范
- **REST API**: 遵循RESTful设计原则，使用标准HTTP状态码
- **参数校验**: 使用JSR-303注解进行参数校验
- **响应格式**: 统一使用封装的响应对象，包含code、message、data字段
- **版本控制**: API版本通过URL路径管理，如 `/api/v1/`

## 测试与部署
- **单元测试**: 使用Mockito进行mock测试，覆盖核心业务逻辑
- **集成测试**: 关键流程必须有集成测试用例
- **配置管理**: 区分开发、测试、生产环境配置
- **构建部署**: 使用Maven进行构建，支持模块化部署 