package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @Description  审核状态,用于business_approve_progress的approve_status
 * @Date 10:52 2020/12/7
 **/
public enum ApproveStatusEnum implements IBaseEnum {

    APPROVE_WAITING(0,"待审核"),
    APPROVE_PASS(1,"审核通过"),
    APPROVE_FAIL(2,"审核不通过");


    private Integer code;
    private String description;

    private ApproveStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
