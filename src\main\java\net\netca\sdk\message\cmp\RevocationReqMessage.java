package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.CustomFreeText;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * @author: zys
 * @date: 2020/3/2 17:26
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Getter
public class RevocationReqMessage extends CmpMessage {
    /**
     * 注销的原因
     */
    private Integer reason;

    /**
     * 证书序列号，用于 证书注销|挂失|解挂
     *
     * @see org.bouncycastle.asn1.crmf.CertTemplate#getSerialNumber()
     */
    private String certSn;


    //============== CMP 证书注销|挂失|解挂 ====================
    /**
     * 颁发者
     */
    private String issuer;

    private CustomFreeText freeText;


    public static RevocationReqMessage defaultMessage(){
        return RevocationReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.REVOKE)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }

}
