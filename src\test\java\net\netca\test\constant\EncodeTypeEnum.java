package net.netca.test.constant;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1Exception;
import net.netca.pki.encoding.asn1.ASN1Object;
import net.netca.pki.encoding.asn1.BMPString;
import net.netca.pki.encoding.asn1.pki.DisplayText;
import net.netca.pki.encoding.asn1.pki.PostalAddress;
import net.netca.test.util.DateUtil;

public enum EncodeTypeEnum implements IBaseEnum{
	
	BMPString("BMPString", "BMPString"){

		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new BMPString(value);
		}

		@Override
		public DisplayText createDisplayText(String value) throws PkiException {
			return DisplayText.NewBMPString(value);
		}
		
	},
	
	VisibleString("VisibleString", "VisibleString"){

		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new net.netca.pki.encoding.asn1.VisibleString(value);
		}

		@Override
		public DisplayText createDisplayText(String value) throws PkiException {
			return DisplayText.NewVisibleString(value);
		}
		
	},
	
	IA5String("IA5String", "IA5String"){

		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new net.netca.pki.encoding.asn1.IA5String(value);
		}

		@Override
		public DisplayText createDisplayText(String value) throws PkiException {
			return DisplayText.NewIA5String(value);
		}
		
	},

	PrintableString("PrintableString", "PrintableString"){

		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new net.netca.pki.encoding.asn1.PrintableString(value);
		}

		@Override
		public DisplayText createDisplayText(String value) throws PkiException {
			return null;
		}
		
	},
	
	UTF8String("UTF8String", "UTF8String"){

		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new net.netca.pki.encoding.asn1.UTF8String(value);
		}

		@Override
		public DisplayText createDisplayText(String value) throws PkiException {
			return DisplayText.NewUTF8String(value);
		}
		
	},

	DirectoryString("DirectoryString", "DirectoryString") {
		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			try {
				return net.netca.pki.encoding.asn1.pki.DirectoryString.NewUTF8String(value).getASN1Object();
			} catch (PkiException e) {
				throw new ASN1Exception(e.getMessage());
			}
		}
	},

	GeneralizedTime("GeneralizedTime", "GeneralizedTime"){
		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			return new net.netca.pki.encoding.asn1.GeneralizedTime(DateUtil.stringToDate(value));
		}
	},

	PostalAddress("PostalAddress", "PostalAddress"){
		@Override
		public ASN1Object createASN1Object(String value) throws ASN1Exception {
			try {
				net.netca.pki.encoding.asn1.pki.PostalAddress postalAddress = new PostalAddress();
				postalAddress.add(net.netca.pki.encoding.asn1.pki.DirectoryString.NewUTF8String(value));
				return postalAddress.getASN1Object();
			} catch (PkiException e) {
				throw new ASN1Exception(e.getMessage());
			}
		}
	};
	
	
	public static EncodeTypeEnum getEncodeTypeEnum(String encodeStr){
		if("BMPString".equalsIgnoreCase(encodeStr)){
			return BMPString;
		}
		
		if("VisibleString".equalsIgnoreCase(encodeStr)){
			return VisibleString;
		}
		
		if("IA5String".equalsIgnoreCase(encodeStr)){
			return IA5String;
		}
		
		if("PrintableString".equalsIgnoreCase(encodeStr)){
			return PrintableString;
		}
		
		if("UTF8String".equalsIgnoreCase(encodeStr)){
			return UTF8String;
		}

		if ("GeneralizedTime".equalsIgnoreCase(encodeStr)){
			return GeneralizedTime;
		}

		if ("PostalAddress".equalsIgnoreCase(encodeStr)){
			return PostalAddress;
		}

		if("DirectoryString".equalsIgnoreCase(encodeStr)){
			return DirectoryString;
		}
		return null;
	}
	
	
	EncodeTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}
	
	private String code;
	private String description;

	@Override
	public String getCode(){
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	@Override
	public String toString() {
		return description;
	}

	public  abstract ASN1Object createASN1Object(String value) throws ASN1Exception;

	public DisplayText createDisplayText(String value) throws PkiException {
		return null;
	}


}
