package net.netca.cloudkey.base.dto.authuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.constant.AuthorizeOperationConstant;
import net.netca.cloudkey.base.constant.AuthorizeStatusConstant;
import net.netca.cloudkey.base.util.CommonUtil;

import java.util.Date;
import java.util.List;

/**
 * @descriptions:新版本的授权分页查询
 * @date: 2019/10/15
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthrizeInfoVO {

    private Integer id;

    private String name;

    private Integer authStatus;

    private Integer certId;

    private String phone;

    private boolean allowAuthorized;

    //KeyX客户端保留, 等同于解锁
    private boolean allowReAuthorized;

    //app端等同于冻结
    private boolean allowUnAuthorized;

    private boolean allowActiveAuthorized;

    private boolean allowRenewAuthorized;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEnd;

    private Integer allowCallCount;

    private String memo;

    /**
     * 对象实例化后，调用该方法可以初始化操作许可
     */
    private void initAllowOper(Integer authStatus) {
        this.allowAuthorized = false;
        this.allowReAuthorized = false;
        this.allowUnAuthorized = false;
        this.allowActiveAuthorized = false;
        this.allowRenewAuthorized = false;

        List<AuthorizeOperationConstant> allowOperations = AuthorizeStatusConstant
                .getAuthorizeStatusConstant(authStatus)
                .getAllowOperations();

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_COFIRM)){
            this.allowAuthorized = true;
        }

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_UNLOCK)){
            this.allowReAuthorized = true;
        }

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_FREEZE)){
            this.allowUnAuthorized = true;
        }

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_ACTIVE)){
            this.allowActiveAuthorized = true;
        }

        //延长有效期
        this.allowRenewAuthorized = true;
    }

    public void setAuthStatus(Integer authStatus) {
        this.authStatus = authStatus;
        initAllowOper(authStatus);
    }

    public String getPhone() {
        if (!CommonUtil.isStringEmpty(phone)) {
            return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return phone;
    }

}
