package net.netca.cloudkey.base.dto.cert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *  "certThumbPrint":[
 *                 {
 *                 "algorithm":222,
 *                 "hash":"证书微缩图（大写)"
 *                 },
 *                 {
 *  *                 "algorithm":222,
 *  *                 "hash":"证书微缩图（大写)"
 *  *              }
 *                   ]
 *
 *  created by zaj on 2019/1/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CertThumbPrint {

    /**
     * 证书微缩图摘要算法，中间件常量
     */
    private Integer algorithm;

    /**
     * 证书微缩图（大写）
     */
    private String hash;
}
