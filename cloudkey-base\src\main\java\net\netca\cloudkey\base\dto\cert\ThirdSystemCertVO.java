package net.netca.cloudkey.base.dto.cert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * @descriptions:
 * @date: 2019/1/31
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class ThirdSystemCertVO {

    private String userUid;

    private String userUidAlias;

    private String userName;

    private int locked;

    private int status;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityEnd;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date reserveRequestTime;

    private Integer reserveRequestType;

}
