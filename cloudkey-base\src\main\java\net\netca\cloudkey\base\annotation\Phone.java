package net.netca.cloudkey.base.annotation;

import cn.hutool.core.util.PhoneUtil;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/3/6 11:32
 */
@Documented
@Constraint(validatedBy = Phone.PhoneValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface Phone {

    String message() default "电话号码格式不正确";

    boolean allowNull() default true; // 是否允许空值，默认 true

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class PhoneValidator implements ConstraintValidator<Phone, String> {

        private boolean allowNull;

        @Override
        public void initialize(Phone constraintAnnotation) {
            allowNull = constraintAnnotation.allowNull();
        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null && allowNull) {
                return true;
            }
            // 使用Hutool提供的中国大陆手机号码校验方法
            // 支持 座机号码、手机号码（中国大陆、中国香港、中国台湾、中国澳门）
            return PhoneUtil.isPhone(value);
        }
    }

}

