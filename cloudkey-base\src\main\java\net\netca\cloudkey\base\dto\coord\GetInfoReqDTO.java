package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2023-06-05 17:21:38
 */
@Data
public class GetInfoReqDTO {

    @NotNull(message = "日志编号不允许为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @NotNull(message = "请求时间不允许为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;
}
