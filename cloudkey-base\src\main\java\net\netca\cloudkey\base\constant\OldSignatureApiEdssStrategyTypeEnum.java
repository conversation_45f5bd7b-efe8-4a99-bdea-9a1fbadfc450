package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @Description 旧签名API 电子存证（EDSS）存储签名值策略类型枚举
 * @Date Created in 15:42 2024/9/20
 * <AUTHOR>
 */
@AllArgsConstructor
public enum OldSignatureApiEdssStrategyTypeEnum implements IBaseEnum {
    ALLOW_VISIT_STORAGE(1, "允许访问并且存储签名数据"),
    NOT_ALLOW_VISIT(2, "不允许访问"),
    ALLOW_VISIT_NO_STORAGE(3, "允许访问但不存储签名数据");
    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
