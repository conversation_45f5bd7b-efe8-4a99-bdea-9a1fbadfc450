package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 注销原因
 * @Date 14:12 2020/7/31
 **/
public enum RevokeReasonTypeEnum implements IBaseEnum {

    NOT_APPOINT(0, "未指定"),
    KEY_LOST(1, "密钥丢失"),
    CHANGE_OF_RELATION(3, "从属关系改变"),
    KEY_HAS_UPDATED(4, "密钥已被更新"),
    STOP_USING(5, "停止使用");

    private int code;
    private String description;

    RevokeReasonTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static String getDescription(int code){
        Optional<RevokeReasonTypeEnum> optionalRevokeReasonTypeEnum =Arrays.asList(RevokeReasonTypeEnum.values()).stream()
                .filter(authorizeOperatorConstant -> authorizeOperatorConstant.getCode().equals(code)).findFirst();
        if (!optionalRevokeReasonTypeEnum.isPresent()) {
            throw new CloudKeyRuntimeException("错误的注销原因");
        }
        return optionalRevokeReasonTypeEnum.get().description;
    }

}
