package net.netca.cloudkey.base.dto.cert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.pki.Certificate;

/**
 * @descriptions:
 * 云密钥中，证书 和 密钥对 的临时容器
 * @date: 2020/1/16
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CertKeypairEntry {
    private byte[] certDerCode;
    private byte[] keypair;
}
