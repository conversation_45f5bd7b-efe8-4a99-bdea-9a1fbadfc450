package net.netca.cloudkey.base.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 业务管理 - 分页查询结果 Response DTO
 *
 * <AUTHOR>
 * @date 2024/3/5 15:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RequestPageRespDTO {

    private String requestId;

    private String bpmsReqId;

    private Integer userId;

    private String userName;

    private Integer certId;

    private String signCertSn;

    private String encCertSn;

    private Long linkmanId;

    private String linkmanName;

    private Integer requestType;

    private Integer requestStatus;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date successTime;

    private String memo;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private String operatorName;

    private String projectName;

    private String projectId;

    private Integer districtId;

}
