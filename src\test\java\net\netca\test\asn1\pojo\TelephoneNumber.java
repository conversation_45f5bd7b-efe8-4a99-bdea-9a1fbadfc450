package net.netca.test.asn1.pojo;


import net.netca.test.exception.ExtensionFormatException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020-03-23 11:54
 */
public class TelephoneNumber {

    private static final Pattern telephoneNumberPattern = Pattern.compile("^[0-9#\\*]{1,15}$");

    static void validate(String text) throws ExtensionFormatException {
        if (text == null) {
            throw new ExtensionFormatException("telephoneNumber should not be empty.");
        }

        int minSize = 1;
        int maxSize = 15;
        if (text.length() < minSize || text.length() > maxSize) {
            throw new ExtensionFormatException(String.format("telephoneNumber's size should be between %s and %s.",
                    minSize, maxSize));
        }

        Matcher matcher = telephoneNumberPattern.matcher(text);
        if (!matcher.matches()) {
            throw new ExtensionFormatException("telephoneNumber should be from '0123456789#*'.");
        }
    }

}
