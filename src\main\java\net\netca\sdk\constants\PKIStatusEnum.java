package net.netca.sdk.constants;

/**
 * 客户端响应状态
 */
public enum PKIStatusEnum implements IBaseEnum {
    /**
     *
     */
    ACCEPTED(org.bouncycastle.asn1.cmp.PKIStatus.GRANTED, "已受理"),
    GRANTED_WITH_MODS(org.bouncycastle.asn1.cmp.PKIStatus.GRANTED_WITH_MODS, "无权限受理"),
    REJECTION(org.bouncycastle.asn1.cmp.PKIStatus.REJECTION, "拒绝"),
    WAITING(org.bouncycastle.asn1.cmp.PKIStatus.WAITING, "处理中"),
    REVOCATION_WARNING(org.bouncycastle.asn1.cmp.PKIStatus.REVOCATION_WARNING, "注销警告"),
    REVOCATION_NOTIFICATION(org.bouncycastle.asn1.cmp.PKIStatus.REVOCATION_NOTIFICATION, "注销提示"),
    KEY_UPDATE_WARNING(org.bouncycastle.asn1.cmp.PKIStatus.KEY_UPDATE_WARNING, "密钥更新警告");

    private Integer code;
    private String description;

    PKIStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
