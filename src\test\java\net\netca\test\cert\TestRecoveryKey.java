package net.netca.test.cert;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.AlgorithmIdentifier;
import net.netca.pki.encoding.asn1.pki.ECCPublicKey;
import net.netca.pki.encoding.asn1.pki.SubjectPublicKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.KeyRecoveryReqMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 密钥恢复
 *
 * @author: zys
 * @date: 2020/3/13 15:43
 */
public class TestRecoveryKey extends TestCmpProtocol {

    @Test
    public void testRecoverKey() throws Exception {
        X509Certificate signCert = getNewSignCert();
        KeyRecoveryReqMessage keyRecoveryReqMessage = getKeyRecoveryReqMessage(signCert);
//        NestedMessage nestedMessage = getNestedMessage(keyRecoveryReqMessage, BusinessTypeEnum.RECOVERY);
        List<CmpMessage> cmpMessageList = new ArrayList<>();
        cmpMessageList.add(keyRecoveryReqMessage);
        MultipleCmpReqContext multipleCmpReqContext = new MultipleCmpReqContext();
        multipleMessageEncoder.encode(multipleCmpReqContext, cmpMessageList);
        PKIMessages pkiMessages = multipleCmpReqContext.getPKIMessages();
        Assert.assertNotNull(pkiMessages);
        byte[] respData = send(pkiMessages.getEncoded());
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        multipleMessageDecoder.decode(multipleCmpReqContext, respPkiMessages);
        List<CmpRespResult> cmpRespResults = multipleCmpReqContext.getCmpRespResults();
        Assert.assertFalse(cmpRespResults.isEmpty());
        cmpRespResults.forEach(
                cmpRespResult -> {
                    Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
                    byte[] signCertDer = cmpRespResult.getSignCertDer();
                    Assert.assertNotNull(signCertDer);
                    try {
                        X509Certificate newSignCert = new X509Certificate(signCertDer);
//                        Assert.assertArrayEquals(SubjectPublicKeyInfo.decode(mockKeyPair.getPublic().getEncoded()).getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue());
                        Assert.assertArrayEquals(SubjectPublicKeyInfo.decode(mockKeyPair.getPublic().getEncoded()).getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue());
                    } catch (PkiException e) {
                        e.printStackTrace();
                    }

                }
        );
    }

    @Test
    public void testRecoverKeyWithNestedMessagae() throws Exception {
        X509Certificate signCert = getNewSignCert();
        KeyRecoveryReqMessage keyRecoveryReqMessage = getKeyRecoveryReqMessage(signCert);
        NestedMessage nestedMessage = getNestedMessage(keyRecoveryReqMessage, BusinessTypeEnum.RECOVERY);
        List<CmpMessage> cmpMessageList = new ArrayList<>();
        cmpMessageList.add(nestedMessage);
        MultipleCmpReqContext multipleCmpReqContext = new MultipleCmpReqContext();
        multipleMessageEncoder.encode(multipleCmpReqContext, cmpMessageList);
        PKIMessages pkiMessages = multipleCmpReqContext.getPKIMessages();
        Assert.assertNotNull(pkiMessages);
        byte[] respData = send(pkiMessages.getEncoded());
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        multipleMessageDecoder.decode(multipleCmpReqContext, respPkiMessages);
        List<CmpRespResult> cmpRespResults = multipleCmpReqContext.getCmpRespResults();
        Assert.assertFalse(cmpRespResults.isEmpty());
        cmpRespResults.forEach(
                cmpRespResult -> {
                    Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
                    byte[] signCertDer = cmpRespResult.getSignCertDer();
                    Assert.assertNotNull(signCertDer);
                    try {
                        X509Certificate newSignCert = new X509Certificate(signCertDer);
//                        Assert.assertArrayEquals(SubjectPublicKeyInfo.decode(mockKeyPair.getPublic().getEncoded()).getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue());
                        Assert.assertArrayEquals(SubjectPublicKeyInfo.decode(mockKeyPair.getPublic().getEncoded()).getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue());
                    } catch (PkiException e) {
                        e.printStackTrace();
                    }

                }
        );
    }

    private KeyRecoveryReqMessage getKeyRecoveryReqMessage(X509Certificate signCert) throws PkiException {
        String serialNumber = Hex.encode(false, signCert.getSerialNumber());
        String issuer = signCert.getIssuer().getLdapName();
        String p10Str = generateRSAP10();
//        String p10Str = generateSM2P10();
        final long certRequestId =  new Random().nextLong();
        // 模拟参数，具体值自行决定
        String senderStr = "CN=netcabpmssystem,O=netca";
        String recipientStr = "CN=test";
//        byte[] publicKey = new ECCPublicKey(AlgorithmIdentifier.SM2Curve_OID, mockSM2KeyPair.getPublicKey().getX(), mockSM2KeyPair.getPublicKey().getY()).toSubjectPublicKeyInfo().getASN1Object().encode();
        byte[] publicKey = mockRSAKeyPair.getPublic().getEncoded();

        // 构建密钥更新 CMP 请求所需的参数，只需证书序列号、certRequestId（必有），有效期、主题、密钥对可选
        // p10 用于代替 POP 验证
        CustomFreeText customFreeText = CustomFreeText.builder()
                .p10(p10Str)
                .certReqId(certRequestId)
                .build();
        return KeyRecoveryReqMessage.defaultMessage().toBuilder()
                .businessTypeEnum(BusinessTypeEnum.RECOVERY)
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .issuer(issuer)
                .serialNumber(serialNumber)
                .certRequestId(certRequestId)
                .publicKey(publicKey)
                .customFreeText(customFreeText)
                .build();
    }
}
