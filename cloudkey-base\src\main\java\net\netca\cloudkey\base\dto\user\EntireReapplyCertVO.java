package net.netca.cloudkey.base.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;
import net.netca.cloudkey.base.constraints.annotation.Area;
import net.netca.cloudkey.base.constraints.annotation.Email;
import net.netca.cloudkey.base.dto.abstractclass.IAreaReqDTO;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class EntireReapplyCertVO  {


    private Integer id;

    private String uid;

    private String name;

    private Integer gender;

    @SecurityDataField
    private String email;

    private String countryName;

    private String province;

    private String city;

    @SecurityDataField
    private String officialResidence;

    private Integer identityType;

    @SecurityDataField
    private String identity;

    private Integer organizationId;

    private String department;

}
