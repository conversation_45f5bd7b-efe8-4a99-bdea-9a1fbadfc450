package net.netca.test.cert;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.util.StringUtils;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 证书注册
 *
 * @author: zys
 * @date: 2020/3/3 15:24
 */
@CommonsLog
public class TestRegisterByP10 extends TestCmpProtocol {

    /**
     * 证书申请 - 双证书
     *
     * @throws Exception
     */
    @Test
    public void testNormalRegisterDoubleCertWithCmpMessageCodec() throws Exception {
        CmpRespResult cmpRespResult = getRegisterDoubleCertCmpRespResult();
        Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
        if (cmpRespResult.getFailureReason() != null) {
            log.error(cmpRespResult.getFailureReason());
            log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
        }
        Assert.assertNotNull(cmpRespResult.getSignCertPem());
//        Assert.assertNotNull(cmpRespResult.getEncCertPem());
        Assert.assertNotNull(cmpRespResult.getSignCertDer());
//        Assert.assertNotNull(cmpRespResult.getEncCertDer());
        log.info(cmpRespResult.getSignCertPem());
    }


    /**
     * 证书申请 - 人工审核
     *
     * @throws Exception
     */
    @Test
    public void registerCertByHumanApprove() throws Exception {
        CmpRespResult cmpRespResult = getRegisterDoubleCertCmpRespResult();
        Assert.assertEquals(PKIStatusEnum.WAITING, cmpRespResult.getPkiStatusEnum());
        CmpRespResult pollCmpRespResult;
        do {
            log.info("start poll....");
            long respCertRequestId = cmpRespResult.getRespCertRequestId();
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.P10_REGISTER;
            pollCmpRespResult = getPollReqCmpRespResult(respCertRequestId, businessTypeEnum);
            String failureReason = pollCmpRespResult.getFailureReason();
            if (StringUtils.hasText(failureReason)) {
                log.info("error code: " + pollCmpRespResult.getFailureCode());
                log.info("error reason: " + failureReason);
            }
            log.info("poll end....");
            Thread.sleep(1000);
        } while (PKIStatusEnum.WAITING.equals(pollCmpRespResult.getPkiStatusEnum()));
        Assert.assertNotNull(pollCmpRespResult.getSignCertPem());
        Assert.assertNotNull(pollCmpRespResult.getEncCertPem());
        Assert.assertNotNull(pollCmpRespResult.getSignCertDer());
        Assert.assertNotNull(pollCmpRespResult.getEncCertDer());
        X509Certificate x509Certificate = new X509Certificate(pollCmpRespResult.getSignCertDer());
        String serialNumber = Hex.encode(false, x509Certificate.getSerialNumber());
        // 被续期证书的证书颁发者
        String issuer = x509Certificate.getIssuer().getLdapName();
        log.info(pollCmpRespResult.getSignCertPem());
        log.info("certSn: " + serialNumber);
        log.info("issuer: " + issuer);
    }

    /**
     * 证书注册轮询
     * 适用场景： 第三方由于某些原因未能保存到证书信息，通过证书轮询重新获取证书信息
     */
    @Test
    public void testRegisterPoll() throws Exception {
        // 先注册，然后假设第三方因某些原因，没有保存到结果
        CmpRespResult registerResult = getRegisterDoubleCertCmpRespResult();
        Assert.assertNotNull(registerResult.getSignCertPem());
        Assert.assertNotNull(registerResult.getEncCertPem());
        Assert.assertNotNull(registerResult.getSignCertDer());
        Assert.assertNotNull(registerResult.getEncCertDer());
        long respCertRequestId = registerResult.getRespCertRequestId();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.P10_REGISTER;
        CmpRespResult pollCmpRespResult = getPollReqCmpRespResult(respCertRequestId, businessTypeEnum);
        Assert.assertEquals(registerResult.getSignCertPem(), pollCmpRespResult.getSignCertPem());
        Assert.assertEquals(registerResult.getEncCertPem(), pollCmpRespResult.getEncCertPem());
        Assert.assertArrayEquals(registerResult.getSignCertDer(), pollCmpRespResult.getSignCertDer());
        Assert.assertArrayEquals(registerResult.getEncCertDer(), pollCmpRespResult.getEncCertDer());
    }


    /**
     * 双证书注册
     *
     * @return {@link CmpRespResult}
     * @throws Exception e
     */
    private CmpRespResult getRegisterDoubleCertCmpRespResult() throws Exception {
        // 构造p10
        String p10Str = generateRSAP10();
        // 构造证书申请需要的消息体
        P10CertReqMessage p10CertReqMessage = getP10CertReqMessage(p10Str);
        // 创建上下文
        SingleCertReqContext context = new SingleCertReqContext(p10CertReqMessage);
        // 消息编码
        p10CertReqMessageEncoder.encode(context, p10CertReqMessage);
        // 获取 PKIMessage
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        // 发送请求
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        // 解析响应
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        p10CertReqMessageDecoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);
        // 获取解码结果
        return context.getCmpRespResult();
    }


    @Test
    public void testRegisterDoubleCertWithNestedMessage() throws Exception {
        String p10Str = generateRSAP10();
        // 构造证书申请需要的消息体
        P10CertReqMessage p10CertReqMessage = getP10CertReqMessage(p10Str);
        NestedMessage nestedMessage = getNestedMessage(p10CertReqMessage, BusinessTypeEnum.P10_REGISTER);
        doNormalRegister(nestedMessage);
    }


    /**
     * 证书注册，预期是成功的
     *
     * @param cmpMessage
     * @throws Exception
     */
    private void doNormalRegister(CmpMessage cmpMessage) throws Exception {
        MultipleCmpReqContext context = new MultipleCmpReqContext();
        List<CmpMessage> cmpMessages = new ArrayList<>();
        cmpMessages.add(cmpMessage);
        multipleMessageEncoder.encode(context, cmpMessages);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        Assert.assertNotNull(respPkiMessages);
        multipleMessageDecoder.decode(context, respPkiMessages);
        context.getCmpRespResults().forEach(
                cmpRespResult -> {
                    Assert.assertSame(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
                    if (cmpRespResult.getFailureReason() != null) {
                        log.error(cmpRespResult.getFailureReason());
                        log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
                    }
                    Assert.assertNotNull(cmpRespResult.getSignCertPem());
                    Assert.assertNotNull(cmpRespResult.getSignCertDer());
                    Assert.assertNotNull(cmpRespResult.getEncCertPem());
                    Assert.assertNotNull(cmpRespResult.getEncCertDer());
                }
        );
    }

    @Test
    public void temp() throws Exception {
        String encodeVal = "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";
        byte[] respData = send(Base64.getDecoder().decode(encodeVal));
        System.out.println(Base64.getEncoder().encodeToString(respData));
    }
}
