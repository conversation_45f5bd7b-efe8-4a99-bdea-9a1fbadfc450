package net.netca.cloudkey.base.dto.authuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * @descriptions:
 * @date: 2019/4/23
 * @author: cxy
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorizedUserVO {

    private Integer id;

    private Integer relAuthId;

    private String uid;

    private Integer locked;

    private Integer authorizeStatus;

    private String signCertSn;

    private String encCertSn;

    private String userName;

    private String userUid;

    private String authorizeUserName;

    private String memo;

    @JsonFormat(timezone = "GMT+8",pattern = "YYYY-MM-dd HH:mm:ss")
    private Date validStart;

    @JsonFormat(timezone = "GMT+8",pattern = "YYYY-MM-dd HH:mm:ss")
    private Date validEnd;

    private Integer validCount;

    private Integer allowCallCount;

    @SecurityDataField
    private String phone;

    private Integer authorizeValidityId;

    private String projId;

    private String hasBindDevice;

    private String hasBindSeparatingKey;
}
