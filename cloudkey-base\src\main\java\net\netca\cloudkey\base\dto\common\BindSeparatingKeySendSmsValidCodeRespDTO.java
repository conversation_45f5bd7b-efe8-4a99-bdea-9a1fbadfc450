package net.netca.cloudkey.base.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 绑定分割密钥 - 发送短信验证码
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BindSeparatingKeySendSmsValidCodeRespDTO {

    private String phone;

    /**
     * 短信验证码可以产生的最小周期(单位 秒)
     */
    private Integer smsCodeGenMinCycle;

    // 为null时不返回该属性，而不是返回null
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String phoneValidCode;
    private String shareInfo;
    private String masterKeypairId;
}
