package net.netca.cloudkey.base.dto.common;


/**
 * 用户真实的密钥对
 * created by zaj on 2019/1/18
 */
public class UserKeypairBytes {

    /**
     * 签名密钥对
     */
    private byte[] signKeypair;

    /**
     * 加密密钥对
     */
    private byte[] encKeypair;

    public UserKeypairBytes() {}

    public UserKeypairBytes(byte[] signKeypair, byte[] encKeypair) {
        this.signKeypair = signKeypair;
        this.encKeypair = encKeypair;
    }

    public byte[] getSignKeypair() {
        return signKeypair;
    }

    public void setSignKeypair(byte[] signKeypair) {
        this.signKeypair = signKeypair;
    }

    public byte[] getEncKeypair() {
        return encKeypair;
    }

    public void setEncKeypair(byte[] encKeypair) {
        this.encKeypair = encKeypair;
    }
}
