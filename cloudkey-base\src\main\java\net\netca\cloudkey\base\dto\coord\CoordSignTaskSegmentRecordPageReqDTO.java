package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 协同签名任务片段记录查询DTO
 * 包含签名任务ID、签名人、签署时间、签名人UID以及签署状态等信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "协同签名任务签署记录分页查询-请求DTO")
public class CoordSignTaskSegmentRecordPageReqDTO {

    @ApiModelProperty(value = "签名任务ID")
    private Long taskId; // 签名任务ID

    @ApiModelProperty(value = "签名任务编号")
    private String taskCode;

    @ApiModelProperty(value = "签名人UID")
    private String signerUid; // 签名人UID

    @ApiModelProperty(value = "签名人")
    private String signerName; // 签名人

    @ApiModelProperty(value = "签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成")
    private Integer signStatus; // 签署状态

    @ApiModelProperty(value = "签署间隔(开始)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTimeFrom; // 签署时间

    @ApiModelProperty(value = "签署时间(结束)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTimeUntil; // 签署时间


}
