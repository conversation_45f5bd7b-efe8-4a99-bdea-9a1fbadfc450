package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.EnumUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;

public enum OrgIdentityTypeEnum implements IBaseEnum {

    ORGID(501, "组织机构代码证"),
    BUSINESSLICENSE(502, "营业执照"),
    INSTITUTION(503, "事业单位登记证"),
    TAXID(504, "税务登记证"),
    COMMUNITYGROUP(505, "社会团体登记证"),
    CIVILGROUP(506, "人民团体登记证"),
    CORPORATIONLICENSE(507, "企业法人营业执照"),
    INSTITUTIONREPRESENT(508, "事业单位法人登记证"),
    COMMUNITYGROUPREPRESENT(509, "社会团体法人登记证"),
    CIVILGROUPREPRESENT(510, "人民团体法人登记证"),
    SOCIALINSURANCEID(511, "社会保险登记证"),
    OTHERORGIDENTITYTYPE(512, "其他机构证件"),
    UNIFIEDSOCIALCREDITCODE(513, "统一社会信用代码");

    private Integer code;
    private String description;

    private OrgIdentityTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static String getDescription(Integer code) {
        String obj = null;
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(OrgIdentityTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = (String) linkedHashMap.get("value");
                break;
            }
        }
        return obj;
    }
}
