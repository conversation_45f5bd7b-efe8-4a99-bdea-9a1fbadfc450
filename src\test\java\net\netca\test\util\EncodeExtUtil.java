package net.netca.test.util;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.Base64;
import net.netca.pki.encoding.asn1.*;
import net.netca.pki.encoding.asn1.pki.*;
import net.netca.sdk.util.StringUtils;
import net.netca.test.asn1.JWTClaimConstraints;
import net.netca.test.asn1.TnAuthorizationList;
import net.netca.test.asn1.pojo.*;
import net.netca.test.exception.ExtensionFormatException;

import java.lang.Integer;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-25 18:32
 */
public class EncodeExtUtil {

    private static AlgorithmIdentifier SHA1_ALGORITHM_ID;

    private static AlgorithmIdentifier SM3_ALGORITHM_ID;
    private static AlgorithmIdentifier SHA256_ALGORITHM_ID;

    private static JCEHasher HASH = new JCEHasher();

    static {
        try {
            SHA1_ALGORITHM_ID = AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA1_OID);
            SM3_ALGORITHM_ID = AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SM3_OID);
            SHA256_ALGORITHM_ID = AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA256_OID);
        } catch (PkiException e) {
            e.printStackTrace();
        }
    }


    public static Extension generatePhoneSubjectAtName(String phone) throws PkiException {
        if (!StringUtils.hasText(phone)) {
            return null;
        }
        GeneralNames gns = new GeneralNames();
        PrintableString printableString = new PrintableString(phone);
        OtherName otherName = new OtherName("********", ASN1Object.decode(printableString.encode(), AnyType.getInstance()));
        GeneralName gn = GeneralName.NewOtherName(otherName);
        gns.add(gn);
        return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
    }


    public static Extension generatePhoneWithDirectoryName(String phone) throws PkiException {
        if (!StringUtils.hasText(phone)) {
            return null;
        }
        X500Name subject = new X500Name();
        PrintableString printableString = new PrintableString(phone);
        SetOf setOf=new SetOf((SetOfType)ASN1TypeManager.getInstance().get("RelativeDistinguishedName"));
        setOf.add(new AttributeTypeAndValue("********", printableString).getASN1Object());
        subject.add(new RelativeDistinguishedName(setOf));
        GeneralNames gns = new GeneralNames();
        GeneralName generalName = GeneralName.NewDirectoryName(subject);
        gns.add(generalName);
        return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
    }

    public static Extension generateSubjectAltName(String email, String ip, String domain) throws PkiException {

        GeneralNames gns = new GeneralNames();
        GeneralName gn = null;
        if (email != null) {
            gn = GeneralName.NewRFC822Name(email);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }

        if (ip != null) {
            byte[] byteValue;
            try {//IPV4
                InetAddress inetAddress = InetAddress.getByName(ip);
                byteValue = inetAddress.getAddress();
            } catch (UnknownHostException e1) { //IPV6
                try {
                    InetAddress inet6Address = Inet6Address.getByName(ip);
                    byteValue = inet6Address.getAddress();
                } catch (UnknownHostException e2) {
                    throw new PkiException(e2);
                }
            }

            gn = GeneralName.NewIPAddress(byteValue);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }

        if (domain != null) {
            gn = GeneralName.NewDNSName(domain);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }


        return null;
    }

    public static Extension generateUserCertServiceId(String userCertServiceId) throws PkiException {

        return new Extension(Extension.NETCA_USERCERTSERVICEID_OID, false, new UTF8String(userCertServiceId).encode());

    }

    //企业身份标识符

    /**
     * @param idType 证件类型
     * @param code   证件号
     * @return
     * @throws Exception
     */
    public static Extension generateEnterprise(Integer idType, String code) throws Exception {
        /**
         *  定义骨架,详细描述查看 http://netca-moss/product/技术规范/SiteAssets/SitePages/主页/NETCA自定义扩展格式规范.pdf 2.2 章节
         *  Netca Crypto 已经内置了此扩展的定义，可以直接使用
         */
        SequenceOfType sequenceOfType = (SequenceOfType) ASN1TypeManager.getInstance().get("NetcaIdentificationItems");
        //IdentificationItems
        SequenceOf identificationItems = new SequenceOf(sequenceOfType);

        //IdentificationItem
        SequenceType sequenceType = (SequenceType) ASN1TypeManager.getInstance().get("NetcaIdentificationItem");


        //原文（UTF-8）
        Sequence identificationItemUtf8 = new Sequence(sequenceType);
        identificationItemUtf8.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemUtf8.set("encode", new net.netca.pki.encoding.asn1.Integer(1L));// utf8编码

        identificationItemUtf8.set("value", new OctetString(code.getBytes(StandardCharsets.UTF_8)));

        identificationItems.add(identificationItemUtf8);


        //Base64
        Sequence identificationItemBase64 = new Sequence(sequenceType);
        identificationItemBase64.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemBase64.set("encode", new net.netca.pki.encoding.asn1.Integer(2L));//base64编码

        identificationItemBase64.set("value", new OctetString(Base64.encode(code.getBytes(StandardCharsets.UTF_8)).getBytes(StandardCharsets.US_ASCII)));

        identificationItems.add(identificationItemBase64);

        // SHA1
        Sequence identificationItemSHA1 = new Sequence(sequenceType);
        identificationItemSHA1.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA1.set("encode", new net.netca.pki.encoding.asn1.Integer(20L));//sha1编码
        byte[] sha1 = HASH.hash(SHA1_ALGORITHM_ID, code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSHA1.set("value", new OctetString(sha1));

        identificationItems.add(identificationItemSHA1);

        return new Extension(Extension.NETCA_ENTERPRISEID_ITEMS_OID, false, identificationItems.encode());
    }

    //个人身份标识符

    /**
     * @param idType 证件类型
     * @param code   证件号
     * @return
     * @throws Exception
     */
    public static Extension generatePersonExt(Integer idType, String code) throws Exception {

        /**
         *  定义骨架,详细描述查看 http://netca-moss/product/技术规范/SiteAssets/SitePages/主页/NETCA自定义扩展格式规范.pdf 2.3 章节
         *  Netca Crypto 已经内置了此扩展的定义，可以直接使用
         */
        SequenceOfType sequenceOfType = (SequenceOfType) ASN1TypeManager.getInstance().get("NetcaIdentificationItems");
        //IdentificationItems
        SequenceOf identificationItems = new SequenceOf(sequenceOfType);

        //IdentificationItem
        SequenceType sequenceType = (SequenceType) ASN1TypeManager.getInstance().get("NetcaIdentificationItem");
        // SHA1
        Sequence identificationItemSHA1 = new Sequence(sequenceType);
        identificationItemSHA1.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA1.set("encode", new net.netca.pki.encoding.asn1.Integer(20L));//sha1编码

        byte[] sha1 = HASH.hash(SHA1_ALGORITHM_ID, code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSHA1.set("value", new OctetString(sha1));

        identificationItems.add(identificationItemSHA1);

        //SHA256
        Sequence identificationItemSHA256 = new Sequence(sequenceType);
        identificationItemSHA256.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA256.set("encode", new net.netca.pki.encoding.asn1.Integer(30L));//sha256编码

        byte[] sha256 = HASH.hash(SHA256_ALGORITHM_ID, code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSHA256.set("value", new OctetString(sha256));

        identificationItems.add(identificationItemSHA256);


        //SM3
        Sequence identificationItemSM3 = new Sequence(sequenceType);
        identificationItemSM3.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSM3.set("encode", new net.netca.pki.encoding.asn1.Integer(40L));//sm3编码

        byte[] sm3 = HASH.hash(SM3_ALGORITHM_ID, code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSM3.set("value", new OctetString(sm3));

        identificationItems.add(identificationItemSM3);

        return new Extension(Extension.NETCA_IDENTIFY_ITEMS_OID, false, identificationItems.encode());
    }


    public static Extension generateJWTClaimConstraintsExt(String oid, boolean isCritical, JWTClaimConstraintsDTO jwtClaimConstraintsDTO) throws PkiException {
        List<String> mustInclude = jwtClaimConstraintsDTO.getMustInclude();
        SequenceOf mustIncludeSeq = new SequenceOf(JWTClaimConstraints.JWT_CLAIM_NAMES_TYPE);
        for (String jwtClaimNameStr : mustInclude) {
            IA5String jwtClaimName = new IA5String(jwtClaimNameStr);
            mustIncludeSeq.add(jwtClaimName);
        }
        List<JWTClaimPermittedValues> permittedValues = jwtClaimConstraintsDTO.getPermittedValues();
        SequenceOf permittedValuesSeq = new SequenceOf(JWTClaimConstraints.JWT_CLAIM_PERMITTED_VALUES_LIST_TYPE);
        for (JWTClaimPermittedValues permittedValue : permittedValues) {
            Sequence jwtClaimPermittedValuesSeq = new Sequence(JWTClaimConstraints.JWT_CLAIM_PERMITTED_VALUES_TYPE);
            IA5String jwtClaimName = new IA5String(permittedValue.getClaim());
            jwtClaimPermittedValuesSeq.set("claim", jwtClaimName);
            SequenceOf permittedSeq = new SequenceOf(JWTClaimConstraints.PERMITTED_TYPE);
            for (String permittedStr : permittedValue.getPermitted()) {
                UTF8String permitted = new UTF8String(permittedStr);
                permittedSeq.add(permitted);
            }
            jwtClaimPermittedValuesSeq.set("permitted", permittedSeq);
            permittedValuesSeq.add(jwtClaimPermittedValuesSeq);
        }
        TaggedValue mustIncludeTaggedValue = new TaggedValue(JWTClaimConstraints.JWT_CLAIM_NAMES_TAGGED_TYPE, mustIncludeSeq);
        TaggedValue permittedValuesTaggedValue = new TaggedValue(JWTClaimConstraints.JWT_CLAIM_PERMITTED_VALUES_LIST_TAGGED_TYPE, permittedValuesSeq);
        byte[] extValue = new JWTClaimConstraints(mustIncludeTaggedValue, permittedValuesTaggedValue).getASN1Object();
        return new Extension(oid, isCritical, extValue);
    }


    public static Extension generateTnAuthorizationListExt(String oid, boolean isCritical, TnAuthorizationListDTO tnAuthorizationListDTO) throws PkiException {
        List<TNEntry> tnAuthList = tnAuthorizationListDTO.getTnAuthList();
        if (TnAuthorizationList.NEED_VALIDATE_TN_AUTH_LIST) {
            validate(tnAuthList);
        }

        SequenceOf entries = new SequenceOf(TnAuthorizationList.TNAuthorizationListType);
        for (TNEntry TnEntry : tnAuthList) {
            if (TnEntry == null) {
                continue;
            }

            ASN1Object entry;
            if (TnEntry.getSpc() != null) {
                entry = new TaggedValue(TnAuthorizationList.ServiceProviderCodeTaggedType, new IA5String(TnEntry.getSpc()));
            } else if (TnEntry.getRange() != null) {
                TelephoneNumberRange range = TnEntry.getRange();
                Sequence telephoneNumberRange = new Sequence(TnAuthorizationList.TelephoneNumberRangeType);
                telephoneNumberRange.set(TelephoneNumberRange.NAME_START, new IA5String(range.getStart()));
                telephoneNumberRange.set(TelephoneNumberRange.NAME_COUNT, new net.netca.pki.encoding.asn1.Integer(range.getCount()));
                entry = new TaggedValue(TnAuthorizationList.TelephoneNumberRangeTaggedType, telephoneNumberRange);
            } else if (TnEntry.getOne() != null) {
                entry = new TaggedValue(TnAuthorizationList.TelephoneNumberTaggedType, new IA5String(TnEntry.getOne()));
            } else {
                throw new PkiException("TNEntry must contain one item.");
            }

            entries.add(entry);
        }

        return new Extension(oid, isCritical, entries.encode());
    }

    public static Extension generateSubjectAlternativeName() {
        return null;
    }

    private static void validate(List<TNEntry> TnAuthList) throws PkiException {
        if (TnAuthList == null || TnAuthList.isEmpty()) {
            throw new PkiException("TNAuthorizationList must not be empty.");
        }

        for (TNEntry TnEntry : TnAuthList) {
            try {
                TnEntry.validate();
            } catch (ExtensionFormatException e) {
                throw new PkiException(e.getMessage(), e);
            }
        }
    }

}
