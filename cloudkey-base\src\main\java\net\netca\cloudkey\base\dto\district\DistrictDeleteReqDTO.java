package net.netca.cloudkey.base.dto.district;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.NotNull;

/**
 * 院区管理 - 删除院区 Request DTO
 *
 * <AUTHOR>
 * @date 2024/2/28 19:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistrictDeleteReqDTO implements ISignature {

    /**
     * 院区ID
     */
    @NotNull(message = "id 不允许为空")
    private Integer id;

    private transient OperatorSignature operatorSignature;

}
