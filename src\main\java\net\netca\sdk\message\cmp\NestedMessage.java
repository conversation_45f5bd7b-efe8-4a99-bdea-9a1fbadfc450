package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * @author: zys
 * @date: 2020/3/9 16:49
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Getter
public class NestedMessage extends CmpMessage {
    private CmpMessage inlineMessage;

    public static NestedMessage defaultMessage(){
        return NestedMessage.builder()
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .build();
    }
}
