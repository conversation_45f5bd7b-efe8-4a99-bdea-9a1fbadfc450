package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @Description: 定义流程配置类型
 * @Author: dyr
 * @CreateDate: 2020/8/12 9:14
 */
@Getter
@AllArgsConstructor
public enum ConfigProcessTypeEnum implements IBaseEnum {

    //初始化流程
    INITIALIZATION(1, "初始化流程");

    private final Integer code;
    private final String description;

}
