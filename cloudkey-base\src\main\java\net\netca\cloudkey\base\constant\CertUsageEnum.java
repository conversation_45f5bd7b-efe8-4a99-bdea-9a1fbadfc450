package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.pki.Certificate;

/**
 * 证书用法
 */
public enum CertUsageEnum implements IBaseEnum {

    ENCRYPT(Certificate.USERCERT_TYPE_ENCRYPT, "加密"), //
    SIGNATURE(Certificate.USERCERT_TYPE_SIGN, "签名");

    private Integer code;
    private String description;

    CertUsageEnum(Integer code, String description) {

        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        for (CertUsageEnum certUsageEnum : values()) {
            if(certUsageEnum.getCode() == code){
                obj = certUsageEnum.getDescription();
            }
        }
        return obj.toString();
    }

}
