package net.netca.sdk.codec.cmp.lambda;

import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

/**
 * @author: zys
 * @date: 2020/3/5 11:21
 */
public class SingleCertReqContext extends CmpRequestContext {

    private CmpMessage cmpReqMessage;

    private PKIMessage pkiMessage;


    private CmpRespResult cmpRespResult;

    private boolean decoded;

    private Throwable error;

    public SingleCertReqContext(CmpMessageConfigManagement cmpMessageConfigManagement, CmpMessage cmpReqMessage) {
        super(cmpMessageConfigManagement);
        this.cmpReqMessage = cmpReqMessage;
        this.cmpRespResult = new CmpRespResult();
        decoded = false;
    }

    public PKIMessages getPKIMessages() {
        if (pkiMessage != null) {
            return new PKIMessages(pkiMessage);
        }
        return null;
    }

    public boolean isEncoded() {
        return pkiMessage != null;
    }


    public boolean isDecoded() {
        return decoded;
    }


    public CmpRespResult getCmpRespResult() {
        return cmpRespResult;
    }

    public void setPkiMessage(PKIMessage pkiMessage) {
        this.pkiMessage = pkiMessage;
    }

    public PKIMessage getPkiMessage() {
        return pkiMessage;
    }

    public void setCmpRespResult(CmpRespResult cmpRespResult) {
        this.cmpRespResult = cmpRespResult;
    }

    public CmpMessage getCmpReqMessage() {
        return cmpReqMessage;
    }

    public void setDecoded(boolean decoded) {
        this.decoded = decoded;
    }

    public Throwable getError() {
        return error;
    }

    public void setError(Throwable error) {
        this.error = error;
    }
}
