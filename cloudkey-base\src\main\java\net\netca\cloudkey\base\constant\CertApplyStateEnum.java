package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.EnumUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * @descriptions:
 * @date: 2019/1/15
 * @author: SaromChars
 */
public enum CertApplyStateEnum implements IBaseEnum {

    BEFORE_APPLY(0, "未申请")
    , APPLYING(1, "申请中")
    , AFTER_APPLY(2, "制证完成")
    // 二级审核中 之前的代码使用了用户表中的status为冻结来表示二级审核中
    // 但是现在需要展示这个状态 因此加上这个状态 但数据库不会存在这个状态
    , LEVEL2_REVIEW(3, "二级审核中")
    , REAPPLYING(4, "变更申请中")
    , REAPPLY_FAIL(5, "变更失败")
    , REAPPLY_SUCCESS(6, "变更完成");

    private Integer code;
    private String description;

    CertApplyStateEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        String obj = null;
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CertApplyStateEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = (String) linkedHashMap.get("value");
                break;
            }
        }
        return obj;
    }

    public static boolean isNormal(int code) {
        return code == CertApplyStateEnum.AFTER_APPLY.getCode() || code == CertApplyStateEnum.REAPPLYING.getCode()
                || code == CertApplyStateEnum.REAPPLY_FAIL.getCode() || code == CertApplyStateEnum.REAPPLY_SUCCESS.getCode();

    }
}
