package net.netca.cloudkey.base.dto.cert;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.netca.cloudkey.base.dto.operator.SignatureDTO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @descriptions:
 * @date: 2020/3/23
 * @author: SaromChars
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RevokeCertDTO extends SignatureDTO {

    @NotEmpty(message = "选择证书不能为空")
    private List<Integer> certIds;

    @NotNull(message = "注销原因不能为空")
    private Integer revokeReason;



    private String memo;
}