package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.util.EnumUtil;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * 用户状态常量
 *
 * <AUTHOR>
 * @since 2018年10月17日12:51:46
 */
public enum CertAlgoTypeEnum implements IBaseEnum {

    RSA(0, "RSA算法证书"),
    SM2(1, "SM2算法证书");

    private Integer code;
    private String description;

    private CertAlgoTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CertAlgoTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = linkedHashMap.get("value");
                break;
            }
        }
        return obj.toString();
    }

}
