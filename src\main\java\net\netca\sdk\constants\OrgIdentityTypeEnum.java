package net.netca.sdk.constants;

public enum OrgIdentityTypeEnum implements IBaseEnum {

    ORGID(501, "组织机构代码证"),
    BUSINESSLICENSE(502, "营业执照"),
    INSTITUTION(503, "事业单位登记证"),
    TAXID(504, "税务登记证"),
    COMMUNITYGROUP(505, "社会团体登记证"),
    CIVILGROUP(506, "人民团体登记证"),
    CORPORATIONLICENSE(507, "企业法人营业执照"),
    INSTITUTIONREPRESENT(508, "事业单位法人登记证"),
    COMMUNITYGROUPREPRESENT(509, "社会团体法人登记证"),
    CIVILGROUPREPRESENT(510, "人民团体法人登记证"),
    SOCIALINSURANCEID(511, "社会保险登记证"),
    OTHERORGIDENTITYTYPE(512, "其他机构证件"),
    UNIFIEDSOCIALCREDITCODE(513, "统一社会信用代码");

    private final Integer code;
    private final String description;

    private OrgIdentityTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static boolean isValid(Integer code) {
        for (OrgIdentityTypeEnum constant : values()) {
            if (constant.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

}
