package net.netca.cloudkey.base.constraints.check;

import net.netca.cloudkey.base.javaconfig.RegExpConfig;

/**
 * 对应js类
 * HKIdCardUtils.js
 * 
 * 香港身份证验证
 * 来自维基百科https://zh.wikipedia.org/zh/%E9%A6%99%E6%B8%AF%E8%BA%AB%E4%BB%BD%E8%AD%89
 * 驗證
    將起首字母轉為字母在A至Z中的次序，A轉為1，B轉為2，如此類推。
    若括弧內為A，轉為10
    將所有數字施加比重，比重為：
        雙字母時，第一個起首字母為9
        第二個或唯一一個起首字母為8
        第一個數字為7
        第二個數字為6
        第三個數字為5
        第四個數字為4
        第五個數字為3
        第六個數字為2
        括弧內數字為1//可能为字母
    將數字和比重的乘積相加，得積項和
    將積項和除以11，得餘數
    從餘數判斷其有效度：
        餘數為0：該組號碼有效
        餘數非0：該組號碼無效
        
   	无性别地址信息
   	
   	http://www.ablmcc.edu.hk/~scy/home/<USER>/idcard.htm
 */

public class HKIdCardUtil {

	/**
	 * 检查身份证号
	 * @param hkidnum
	 * @return
	 */
	public static boolean checkIdNum(String hkidnum) {
		int sum = -1;
		if (checkPattern(hkidnum)) {
			String reverseHKIdnum = formatForCal(hkidnum);
			if (reverseHKIdnum != null) {//格式正确
				sum = 0;
				for (int i = 0; i < 9; i++) {
					char thisChar = reverseHKIdnum.charAt(i);
					if (i == 0 && thisChar == 65) {//首字母为A
						sum += 10;
					} else {
						sum += changeLetter(thisChar) * (i + 1);
					}
				}
			}
		}
		return sum % 11 == 0;
	}

	/***
	 * 转换大写字母和左右括号
	 * @param letter
	 * @return
	 */
	private static int changeLetter(char letter) {
		if (" ".charAt(0) == letter) {
			return 36;
		}
		int charCode = (int) letter;// 获取ASCII码
		if (charCode >= 65 && charCode <= 90) {// A~Z 为65~90
			return charCode - 65 + 10;
		}
		return Integer.valueOf(letter + "");
	}

	//检查格式
	private static boolean checkPattern(String hkidnum){
		String p = RegExpConfig.hkIdCard;
		return hkidnum.matches(p);
	}
	
	//为方便计算返回 9位 的  倒序的 去除()的 香港身份证号码
	private static String formatForCal(String hkidnum){
		if(hkidnum == null)return null;
		String reserveStr = new StringBuffer(hkidnum).reverse().toString().replaceFirst("\\(", "").replaceFirst("\\)", "");
		// return reserveStr.length() == 8 ? reserveStr + "0" : reserveStr;//补个0
		return reserveStr.length() == 8 ? reserveStr + " " : reserveStr;
	}

}
