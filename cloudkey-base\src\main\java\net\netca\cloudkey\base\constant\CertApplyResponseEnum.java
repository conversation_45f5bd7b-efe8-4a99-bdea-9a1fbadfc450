package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @descriptions:
 * @date: 2019/9/1
 * @author: SaromChars
 */
public enum CertApplyResponseEnum implements IBaseEnum {
    APPLY_SUCCESS(0,"证书自动签发成功"),
    APPLY_WAITING(1,"证书审核中");

    private Integer code;
    private String description;

    private CertApplyResponseEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
