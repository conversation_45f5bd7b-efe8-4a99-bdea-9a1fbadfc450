package net.netca.cloudkey.base.dto.audit;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2023-06-05 17:21:38
 */
@Data
public class SignatureParseReqDTO {

    @NotBlank(message = "signedData 不允许为空")
    private String signedData;
}
