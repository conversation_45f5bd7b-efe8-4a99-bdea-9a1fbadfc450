package net.netca.cloudkey.base.dto.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 说明：归档数据库启用时间
 *
 * <AUTHOR>
 * @date 2023-05-31 15:05:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArchiveStartTimeRespDTO {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditAppLogStartTime;
}
