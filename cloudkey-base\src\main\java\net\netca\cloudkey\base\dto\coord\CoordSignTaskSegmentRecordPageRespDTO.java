package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 协同签名任务片段记录查询响应DTO
 * 包含签名记录ID、签名任务ID、发起人、发起人UID、签名人、签名人ID、签署状态以及签署时间等信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "协同签名任务片段记录查询-响应DTO")
public class CoordSignTaskSegmentRecordPageRespDTO {

    @ApiModelProperty(value = "签名记录ID")
    private Long recordId; // 签名记录ID

    @ApiModelProperty(value = "签名任务ID")
    private Long taskId; // 签名任务ID

    @ApiModelProperty(value = "签名任务编号")
    private String taskCode; // 签名任务编号

    @ApiModelProperty(value = "发起人")
    private String starterName; // 发起人

    @ApiModelProperty(value = "发起人UID")
    private String starterUid; // 发起人UID

    @ApiModelProperty(value = "签名人")
    private String signerName; // 签名人

    @ApiModelProperty(value = "签名人ID")
    private String signerUid; // 签名人ID

    @ApiModelProperty(value = "签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成")
    private Integer signStatus; // 签署状态

    @ApiModelProperty(value = "签署时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTime; // 签署时间

}
