# 第三方通讯证书的keystore
keyStorePath=src/test/resources/netcarasdk-test.jks
# keystore的类型
keyStoreType=JKS
# keystore的密码
keyStorePwd=123456
# 密钥对密码
privateKeyPwd=123456
# 密钥对别名
keyStoreAlias=netcarasdk-test
# 第三方通讯证书的别名
certAlias=netcarasdk-cert

# 存储用于生成模拟P10的密钥对的jks文件
mockKeyPairPath=src/test/resources/mockKeyPair.jks
mockStoreType=JKS
# jks密码
mockKeyStorePwd=123456
# 密钥对别名
mockKeyStoreAlias=mockKeyPair
# 密钥对密码
mockPrivateKeyPwd=123456
# 密钥对别名
mockKeyStoreAlias2=mockKeyPair2
# 密钥对密码
mockPrivateKeyPwd2=123456

# 第三方操作员证书的keystore
operatorKeyStorePath=src/test/resources/operator-keypair.jks
# keystore的类型
operatorKeyStoreType=JKS
# jks密码
operatorKeyStorePwd=123456
# 密钥对密码
operatorPrivateKeyPwd=123456
# 密钥对别名
operatorKeyStoreAlias=operator-keypair
# 第三方操作员证书别名
operatorCertAlias=operatorCert