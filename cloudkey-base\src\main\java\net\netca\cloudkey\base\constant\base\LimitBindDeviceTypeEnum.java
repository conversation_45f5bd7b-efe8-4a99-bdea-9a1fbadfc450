package net.netca.cloudkey.base.constant.base;

/**
 * <AUTHOR>
 * @Description  绑定设备类型
 * @Date 16:20 2021/3/3
 **/
public enum LimitBindDeviceTypeEnum implements IBaseEnum {

    NO_LIMIT(0,"无限制"),
    LIMIT_ONE(1,"限制绑定一个设备"),
    LIMIT_COVER(2,"覆盖绑定");

    private Integer code;
    private String description;

    private LimitBindDeviceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
