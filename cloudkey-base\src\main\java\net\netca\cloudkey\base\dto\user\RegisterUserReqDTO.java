package net.netca.cloudkey.base.dto.user;

import lombok.Data;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON><PERSON> on 2019-11-18
 */
@Data
public class RegisterUserReqDTO extends TimestampToken {

    private String cloudkeyProjectId;

    @NotNull(message = "userType不能为空")
    private Integer userType;

    @NotNull(message = "user节点不能为空")
    @Valid
    private UserReqDTO user;

    private String userPin;

    private Boolean overwriteUser = false;

    private List<SubmitFileDTO> submitFiles;

    private String imgValidCodeIdentity;

    private String imgValidCode;

    /**
     * 是否需要印章：0-否，1-是，为空则代表否。选填
     */
    private Integer isNeedSealRequest;

    /**
     * 印章类型，选填（需要印章时必填）
     * <p>
     * 1-单位圆形印章
     * 2-单位椭圆印章
     * 3-业务专用章
     * 4-合同专用章
     * 5-其他
     */
    private Integer sealPicType;

    /**
     * 印章宽度，单位：毫米，选填（需要印章时必填）
     */
    private Integer sealPicWidth;

    /**
     * 印章高度，单位：毫米，选填（需要印章时必填）
     */
    private Integer sealPicHeight;

    /**
     * 印章名称，选填（需要印章时必填）
     */
    private String sealName;

}
