package net.netca.cloudkey.base.dto.uidalias;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2022-06-24 16:25:52
 */
@Data
public class UserUidAliasPageVO {
    private Integer id;
    private String uid;
    private String name;
    private String uidAlias;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    private Integer districtId;

    private String districtName;

}
