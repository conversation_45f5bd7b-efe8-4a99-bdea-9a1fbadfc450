# 设计文档

## 概述

本设计文档描述了如何实现 ShardingSphere JDBC 配置化启动和定时任务服务动态加载功能。通过引入配置驱动的组件加载机制，系统能够根据配置文件动态决定是否启用 ShardingSphere 和各个定时任务服务。

## 架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    CloudKey Application                     │
├─────────────────────────────────────────────────────────────┤
│  Configuration Layer                                        │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ Config Reader   │  │ Conditional Bean Registration   │   │
│  │ - Properties    │  │ - @ConditionalOnProperty        │   │
│  │ - Validation    │  │ - @Configuration                │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Data Access Layer                                          │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ ShardingSphere  │  │ Standard DataSource             │   │
│  │ Configuration   │  │ Configuration                   │   │
│  │ (Conditional)   │  │ (Fallback)                      │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Scheduling Layer                                           │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ Audit Log       │  │ Business Coord Sign API Log    │   │
│  │ Schedule        │  │ Schedule                        │   │
│  │ (Conditional)   │  │ (Conditional)                   │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 组件和接口

### 1. 配置管理组件

#### ConfigurationProperties

```java
@ConfigurationProperties(prefix = "cloudkey")
public class CloudKeyConfigProperties {
    private ShardingSphereConfig shardingsphere = new ShardingSphereConfig();
    private ScheduleConfig schedule = new ScheduleConfig();

    public static class ShardingSphereConfig {
        private boolean enabled = true; // 默认启用
    }

    public static class ScheduleConfig {
        private AuditAppLogConfig auditAppLog = new AuditAppLogConfig();
        private AuditAppLogTruncateConfig auditAppLogTruncate = new AuditAppLogTruncateConfig();
        private BusinessCoordSignApiLogConfig businessCoordSignApiLog = new BusinessCoordSignApiLogConfig();
        private BusinessCoordSignApiLogTruncateConfig businessCoordSignApiLogTruncate = new BusinessCoordSignApiLogTruncateConfig();

        public static class AuditAppLogConfig {
            private boolean enabled = true; // 依赖 ShardingSphere 启用
        }

        public static class AuditAppLogTruncateConfig {
            private boolean enabled = true; // 依赖 ShardingSphere 启用
        }

        public static class BusinessCoordSignApiLogConfig {
            private boolean enabled = true; // 依赖 ShardingSphere 启用
        }

        public static class BusinessCoordSignApiLogTruncateConfig {
            private boolean enabled = true; // 依赖 ShardingSphere 启用
        }
    }
}
```

### 2. 数据源配置组件

#### ShardingSphereAutoConfiguration

```java
@Configuration
@ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(CloudKeyConfigProperties.class)
public class ShardingSphereAutoConfiguration {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "shardingsphere.datasource")
    public DataSource shardingSphereDataSource() {
        // 使用 shardingsphere.datasource 配置创建 ShardingSphere 数据源
        return ShardingSphereDataSourceFactory.createDataSource(...);
    }
}
```

#### StandardDataSourceConfiguration

```java
@Configuration
@ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "false")
public class StandardDataSourceConfiguration {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource standardDataSource() {
        // 使用 spring.datasource 配置创建标准数据源
        return DataSourceBuilder.create().build();
    }
}
```

### 3. 定时任务配置组件

#### ScheduleAutoConfiguration

```java
@Configuration
@EnableConfigurationProperties(CloudKeyConfigProperties.class)
public class ScheduleAutoConfiguration {

    // ShardingSphere 依赖的定时任务
    @Bean
    @ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnProperty(name = "cloudkey.schedule.auditAppLog.enabled", havingValue = "true", matchIfMissing = true)
    public AuditAppLogScheduleService auditAppLogScheduleService() {
        return new AuditAppLogScheduleService();
    }

    @Bean
    @ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnProperty(name = "cloudkey.schedule.auditAppLogTruncate.enabled", havingValue = "true", matchIfMissing = true)
    public AuditAppLogTruncateScheduleService auditAppLogTruncateScheduleService() {
        return new AuditAppLogTruncateScheduleService();
    }

    @Bean
    @ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnProperty(name = "cloudkey.schedule.businessCoordSignApiLog.enabled", havingValue = "true", matchIfMissing = true)
    public BusinessCoordSignApiLogScheduleService businessCoordSignApiLogScheduleService() {
        return new BusinessCoordSignApiLogScheduleService();
    }

    @Bean
    @ConditionalOnProperty(name = "cloudkey.shardingsphere.enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnProperty(name = "cloudkey.schedule.businessCoordSignApiLogTruncate.enabled", havingValue = "true", matchIfMissing = true)
    public BusinessCoordSignApiLogTruncateScheduleService businessCoordSignApiLogTruncateScheduleService() {
        return new BusinessCoordSignApiLogTruncateScheduleService();
    }

    // 其他不依赖 ShardingSphere 的定时任务...
}
```

## 数据模型

### 配置文件结构

```properties
# ShardingSphere 配置
cloudkey.shardingsphere.enabled=true

# 定时任务配置（这4个任务强依赖 ShardingSphere）
cloudkey.schedule.auditAppLog.enabled=true
cloudkey.schedule.auditAppLogTruncate.enabled=true
cloudkey.schedule.businessCoordSignApiLog.enabled=true
cloudkey.schedule.businessCoordSignApiLogTruncate.enabled=true

# 数据源配置 - 启用 ShardingSphere 时使用
shardingsphere.datasource.master.url=************************************
shardingsphere.datasource.master.username=root
shardingsphere.datasource.master.password=password

# 数据源配置 - 禁用 ShardingSphere 时使用
spring.datasource.url=************************************
spring.datasource.username=root
spring.datasource.password=password
```

### 配置对象模型

```java
public class CloudKeyConfig {
    private boolean shardingSphereEnabled;
    private Map<String, Boolean> scheduleEnabled;

    // 配置验证方法
    public void validate() throws ConfigurationException;

    // 配置合并方法
    public void merge(CloudKeyConfig other);
}
```

## 错误处理

### 配置错误处理策略

1. **配置缺失处理**

   - 使用默认值（所有功能默认启用）
   - 记录警告日志

2. **配置格式错误处理**

   - 记录错误日志
   - 使用默认值
   - 继续启动流程

3. **依赖缺失处理**
   - 当 ShardingSphere 被禁用但相关依赖缺失时，自动降级到标准数据源
   - 当定时任务被启用但相关服务不可用时，记录错误并跳过
   - 当 ShardingSphere 被禁用时，自动禁用依赖它的 4 个定时任务

### 异常类型定义

```java
public class ConfigurationException extends Exception {
    private String configKey;
    private String configValue;
    private String reason;
}

public class ComponentLoadException extends Exception {
    private String componentName;
    private Throwable cause;
}
```

## 测试策略

### 单元测试

1. **配置读取测试**

   - 测试各种配置组合
   - 测试默认值处理
   - 测试配置验证

2. **条件 Bean 注册测试**

   - 测试不同配置下的 Bean 创建
   - 测试 Bean 的优先级和覆盖

3. **数据源切换测试**
   - 测试 ShardingSphere 启用/禁用场景
   - 测试数据源连接和查询

### 集成测试

1. **完整启动测试**

   - 测试不同配置组合下的应用启动
   - 测试定时任务的实际执行

2. **配置热更新测试**
   - 测试配置文件修改后的重启行为
   - 测试配置一致性

### 性能测试

1. **启动时间测试**

   - 对比启用/禁用不同组件的启动时间
   - 测试配置读取的性能影响

2. **运行时性能测试**
   - 测试 ShardingSphere 与标准数据源的性能差异
   - 测试定时任务对系统性能的影响

## 部署考虑

### 配置管理

1. **环境隔离**

   - 开发环境：启用所有功能用于测试
   - 测试环境：根据测试需求选择性启用
   - 生产环境：根据实际需求配置

2. **配置版本控制**
   - 配置文件纳入版本控制
   - 提供配置模板和示例

### 监控和日志

1. **启动日志**

   - 记录所有配置的读取结果
   - 记录组件的加载状态

2. **运行时监控**
   - 监控定时任务的执行状态
   - 监控数据源的连接状态

### 回滚策略

1. **配置回滚**

   - 保留配置文件的备份
   - 提供快速回滚机制

2. **功能降级**
   - 当 ShardingSphere 出现问题时自动降级到标准数据源
   - 当定时任务出现问题时自动禁用相关任务

## 安全考虑

### 配置安全

1. **敏感信息保护**

   - 数据库密码等敏感信息继续使用加密存储
   - 配置文件访问权限控制

2. **配置验证**
   - 防止恶意配置导致系统异常
   - 配置项的合法性检查

### 运行时安全

1. **权限控制**

   - 定时任务的执行权限控制
   - 数据源访问权限验证

2. **审计日志**
   - 记录配置变更的审计日志
   - 记录组件启用/禁用的操作日志
