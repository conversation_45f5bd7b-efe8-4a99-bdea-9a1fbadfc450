package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 附件类型
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@AllArgsConstructor
@Getter
public enum AttachmentTypeEnum {

    BANK_TRANSFER_RECEIPT(1, "银行转账凭证"),
    HANDLER_ID_CARD_PHOTO(2, "经办人身份证头像"),
    ONSITE_PHOTO(3, "现场拍照"),
    PERSONAL_ID_CARD_PHOTO(4, "个人身份证头像"),
    EMPLOYEE_ID_CARD_PHOTO(5, "员工身份证头像"),
    SEAL_IMAGE(6, "印章图片"),
    VAT_APPLICATION_FORM(7, "增值税申请表"),
    ATTACHMENT_PHOTO_UPLOAD(8, "附件拍照上传"),
    ATTACHMENT_FILE_UPLOAD(9, "附件文件上传");

    private final Integer code;
    private final String description;

}
