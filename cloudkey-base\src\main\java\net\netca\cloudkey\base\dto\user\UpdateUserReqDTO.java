package net.netca.cloudkey.base.dto.user;

import lombok.Data;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * create by h<PERSON><PERSON><PERSON> on 2019-11-18
 */
@Data
public class UpdateUserReqDTO extends TimestampToken {

    private String cloudkeyProjectId;

    @NotNull(message = "user节点不能为空")
    @Valid
    private UserUpdateDTO user;
}
