package net.netca.test.cert;

import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import org.junit.Test;

import java.util.Objects;

/**
 * 证书解挂
 * @author: zys
 * @date: 2020/3/5 19:04
 */
public class TestUnSuspendCert extends TestCmpProtocol {

    @Test
    public void testUnSuspendCert() throws Exception {
        X509Certificate signCert = getNewSignCert();
        Objects.requireNonNull(signCert);
        suspendOrUnSuspendCert(signCert, BusinessTypeEnum.SUSPEND);
        // 解挂
        suspendOrUnSuspendCert(signCert, BusinessTypeEnum.UNSUSPEND);
    }

}
