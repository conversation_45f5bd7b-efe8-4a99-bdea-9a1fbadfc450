# 数据库驱动配置修复需求文档

## 介绍

当前系统在数据库驱动配置方面存在问题，导致使用 KingbaseES 数据库时出现 H2 驱动冲突错误。需要修复 ShardingSphere 和标准数据源的驱动配置逻辑，确保根据实际数据库类型正确加载对应的驱动程序。

## 需求

### 需求 1：修复 ShardingSphere 配置条件判断

**用户故事：** 作为系统管理员，我希望当明确设置 `cloudkey.shardingsphere.enabled=false` 时，ShardingSphere 配置完全不被加载，以便系统使用标准数据源配置。

#### 验收标准

1. 当 `cloudkey.shardingsphere.enabled=false` 时，系统不应加载 `ShardingSphereAutoConfiguration`
2. 当 `cloudkey.shardingsphere.enabled=true` 或未配置时，系统应加载 `ShardingSphereAutoConfiguration`
3. 配置条件判断应准确反映用户的配置意图

### 需求 2：动态数据库驱动类名配置

**用户故事：** 作为开发人员，我希望系统能够根据数据库 URL 自动识别并设置正确的驱动类名，而不是硬编码特定数据库的驱动。

#### 验收标准

1. 当数据库 URL 为 `jdbc:kingbase8://...` 时，系统应使用 `com.kingbase8.Driver`
2. 当数据库 URL 为 `jdbc:mysql://...` 时，系统应使用 `com.mysql.cj.jdbc.Driver`
3. 当数据库 URL 为 `jdbc:postgresql://...` 时，系统应使用 `org.postgresql.Driver`
4. 当数据库 URL 为 `jdbc:oracle://...` 时，系统应使用 `oracle.jdbc.OracleDriver`
5. 系统应支持其他常见数据库类型的驱动自动识别

### 需求 3：移除不必要的 H2 驱动强制加载

**用户故事：** 作为系统管理员，我希望系统只加载实际需要的数据库驱动，避免不必要的驱动冲突。

#### 验收标准

1. 当实际数据库不是 H2 时，系统不应强制加载 `org.h2.Driver`
2. 只有在测试环境或明确配置使用 H2 数据库时，才加载 H2 驱动
3. ShardingSphere 内部如果需要 H2 驱动，应通过配置或条件判断来决定是否加载

### 需求 4：创建标准数据源自动配置

**用户故事：** 作为系统管理员，我希望当禁用 ShardingSphere 时，系统能够自动配置标准的单数据源，支持各种数据库类型。

#### 验收标准

1. 当 `cloudkey.shardingsphere.enabled=false` 时，系统应创建标准数据源配置
2. 标准数据源应支持从 `spring.datasource.*` 配置读取连接信息
3. 如果 `spring.datasource.*` 配置不存在，应回退到 `shardingsphere.datasource.business.*` 配置
4. 标准数据源应根据 URL 自动设置正确的驱动类名

### 需求 5：增强数据库类型识别和驱动映射

**用户故事：** 作为开发人员，我希望系统能够准确识别各种数据库类型，并提供完整的驱动类名映射。

#### 验收标准

1. 系统应能识别 KingbaseES、MySQL、PostgreSQL、Oracle、H2、SQL Server 等常见数据库
2. 每种数据库类型应有对应的驱动类名映射
3. 当遇到未知数据库类型时，系统应提供清晰的错误信息和建议
4. 驱动映射应支持扩展，便于添加新的数据库类型支持

### 需求 6：改进错误处理和日志记录

**用户故事：** 作为系统管理员，我希望当数据库配置出现问题时，能够获得清晰的错误信息和解决建议。

#### 验收标准

1. 当驱动类不匹配时，错误信息应明确指出期望的驱动和实际使用的驱动
2. 当数据库连接失败时，应提供具体的配置检查建议
3. 系统启动时应记录实际使用的数据库类型和驱动信息
4. 配置验证失败时，应提供具体的配置修复指导