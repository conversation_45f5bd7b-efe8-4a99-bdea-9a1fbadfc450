package net.netca.cloudkey.base.dto.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class CertLastAuditAppLogExcelDTO {

    private String uid;
    private String userName;
    private String projectUid;
    private String userType;
    private String operatorName;
    private String description;
    private String success;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

}
