# 实施计划

- [x] 1. 更新配置属性类支持优化的配置结构


  - 修改 CloudKeyConfigProperties 类使用 cloudkey.shardingsphere.enabled 配置项
  - 确保 4 个特定定时任务的配置结构明确依赖关系
  - 更新配置验证逻辑以支持数据源配置的独立存在
  - _需求: 1.1, 1.2, 3.1, 3.5, 3.6_

- [x] 2. 优化 ShardingSphere 条件化配置实现








  - 修改 ShardingSphereAutoConfiguration 使用 cloudkey.shardingsphere.enabled 配置项
  - 实现 shardingsphere.datasource 配置前缀的数据源创建
  - 修改 StandardDataSourceConfiguration 使用 spring.datasource 配置前缀
  - 确保两种数据源配置可以独立存在
  - _需求: 1.1, 1.2, 1.4, 1.5, 4.2_

- [x] 3. 修改现有定时任务服务以支持条件化加载

- [x] 3.1 修改 AuditAppLogScheduleService 支持条件化加载

  - 移除 @Service 注解，改为通过配置类注册
  - 保持原有的定时任务逻辑不变
  - 添加日志记录组件加载状态
  - _需求: 2.1, 2.2, 5.3_

- [x] 3.2 修改 AuditAppLogTruncateScheduleService 支持条件化加载

  - 移除 @Service 注解，改为通过配置类注册
  - 保持原有的定时任务逻辑不变
  - 添加日志记录组件加载状态
  - _需求: 2.3, 2.4, 5.3_

- [x] 3.3 修改 BusinessCoordSignApiLogScheduleService 支持条件化加载

  - 移除 @Service 注解，改为通过配置类注册
  - 保持原有的定时任务逻辑不变
  - 添加日志记录组件加载状态
  - _需求: 2.5, 2.6, 5.3_

- [x] 3.4 修改 BusinessCoordSignApiLogTruncateScheduleService 支持条件化加载

  - 移除 @Service 注解，改为通过配置类注册
  - 保持原有的定时任务逻辑不变
  - 添加日志记录组件加载状态
  - _需求: 2.7, 2.8, 5.3_

- [x] 4. 优化定时任务自动配置类实现双重条件依赖








  - 修改 ScheduleAutoConfiguration 配置类
  - 为 4 个特定定时任务添加 ShardingSphere 启用条件依赖
  - 实现双重条件注解：@ConditionalOnProperty 组合使用
  - 确保 ShardingSphere 禁用时这 4 个任务自动禁用
  - _需求: 2.1-2.8, 2.9, 3.2, 4.3_

- [x] 5. 更新配置文件支持优化的配置项和数据源配置

  - 修改 cloudkey-config.properties 使用 cloudkey.shardingsphere.enabled 配置项
  - 添加 shardingsphere.datasource 和 spring.datasource 配置示例
  - 更新配置注释说明 4 个定时任务的 ShardingSphere 依赖关系
  - 提供两种数据源配置的完整示例
  - _需求: 3.1, 3.3, 3.5, 3.6, 4.1_

- [ ] 6. 优化配置读取和验证机制支持数据源配置验证

  - 更新配置读取工具类支持 cloudkey.shardingsphere.enabled 配置项
  - 实现数据源配置的存在性验证逻辑
  - 添加当一种数据源配置缺失时的友好错误提示
  - 实现配置依赖关系的验证（定时任务对 ShardingSphere 的依赖）
  - _需求: 3.4, 3.5, 3.6, 4.4, 5.5_

- [ ] 7. 优化日志和监控功能支持数据源切换和依赖关系日志

  - 在 ShardingSphere 配置类中添加数据源选择的详细日志
  - 在定时任务配置类中添加 ShardingSphere 依赖状态的日志
  - 记录 4 个特定定时任务因 ShardingSphere 禁用而跳过的日志
  - 实现数据源配置验证失败的详细错误日志
  - 添加配置依赖关系检查的监控日志
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 8. 编写优化功能的单元测试

  - 为优化的配置属性类编写单元测试
  - 为双重条件的定时任务加载编写测试
  - 测试数据源配置的独立存在场景
  - 测试 ShardingSphere 禁用时 4 个定时任务的自动禁用
  - 测试各种配置组合和边界情况
  - _需求: 所有需求的测试覆盖_

- [ ] 9. 编写优化功能的集成测试

  - 测试 ShardingSphere 启用/禁用场景下的完整应用启动流程
  - 测试两种数据源配置的切换行为
  - 测试 4 个定时任务的 ShardingSphere 依赖行为
  - 测试数据源配置独立存在时的系统行为
  - 验证优化后的向后兼容性和默认行为
  - _需求: 4.1, 4.3, 3.3, 1.4, 1.5_

- [ ] 10. 更新文档和部署指南支持优化功能
  - 更新系统配置文档说明新的配置项和依赖关系
  - 创建从旧配置到新配置的迁移指南
  - 提供 ShardingSphere 启用/禁用的不同环境配置示例
  - 编写数据源配置和定时任务依赖的故障排查指南
  - _需求: 所有需求的文档支持_
