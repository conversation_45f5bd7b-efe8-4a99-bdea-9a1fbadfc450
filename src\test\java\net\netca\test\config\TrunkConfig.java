package net.netca.test.config;

/**
 * <AUTHOR>
 * @date 2020-11-02 16:29
 */
public class TrunkConfig implements Config {
    public static String caCommCertSha256 = "53e8bd21afaebfdfb6de56d9423b8bf398adf420abace78e29714e9a0ab7ee4b";

    /**
     * RSA个人双证书模板
     */
    public static String certTempleId = "T202001080001";

    public static String BASE_URL = "http://192.168.200.102:8084/raserver/cmp/";

    public static String raCommCertStr = "MIIB7DCCAZOgAwIBAgIUaOHjv3cKtuZ8mF79Ypf8QH9UTt4wCgYIKoEcz1UBg3UwVTELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEgMB4GA1UEAwwXTkVUQ0FfVGVzdDIwMTkgU00yIENBMDEwHhcNMjAwMjA0MTIzMDIwWhcNMzAwMjA0MTIzMDIwWjA2MQswCQYDVQQGEwJDTjEMMAoGA1UECgwDMTIzMRkwFwYDVQQDDBB0ZXN0X3JhX2NvbV9jZXJ0MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE94VXDtRI4oHSl1Hi8RCYspaDTuzg4HanrB85a47sTsLeIJh18OVj7oldPD68VdmkLb9cujOrZgo5rKRoVX/cHaNgMF4wHQYDVR0OBBYEFIbyPEB+0WBqfhQs8DV9EwylS6CgMA4GA1UdDwEB/wQEAwIGwDAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFNBBHunn3uwo364nD8m2qyrPEZ08MAoGCCqBHM9VAYN1A0cAMEQCIAXUnSVjgmxc4MCoVJ9CJlwM7phPxvADJqdlhiNRSGKQAiAJzW+NOv9xPDg+tNOSDYrP+g7HS0/51pnrplbEp9BmtQ==";

    @Override
    public String getCertTempleId() {
        return certTempleId;
    }

    @Override
    public String getBaseUrl() {
        return BASE_URL;
    }

    @Override
    public String getCaCommCertSha256() {
        return caCommCertSha256;
    }

    @Override
    public String getRaCommCert() {
        return raCommCertStr;
    }
}
