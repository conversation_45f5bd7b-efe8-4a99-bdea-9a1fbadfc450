package net.netca.test.constant;


import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 资料查证：
 * https://www.alvestrand.no/objectid/top.html
 * https://www.ietf.org/rfc/rfc3039.txt?number=3039
 * https://oidref.com/
 */
public enum CertSubjectAttributeTypeEnum {
    C("2.5.4.6", "C", "PrintableString"),
    ST("2.5.4.8", "ST", "UTF8String"),
    S("2.5.4.8", "S", "UTF8String"),
    L("2.5.4.7", "L", "UTF8String"),
    O("2.5.4.10", "O", "UTF8String"),
    OU("2.5.4.11", "OU", "UTF8String"),
    CN("2.5.4.3", "CN", "UTF8String"),
    EMAIL("1.2.840.113549.1.9.1", "EMAIL", "IA5String"),
    E("1.2.840.113549.1.9.1", "E", "IA5String"),
    DC("0.9.2342.19200300.100.1.25", "DC", "IA5String"),
    UID("0.9.2342.19200300.100.1.1", "UID", "UTF8String"),
    SERIAL_NUMBER("2.5.4.5", "SERIALNUMBER", "UTF8String"),
    STREET("2.5.4.9", "street", "DirectoryString"),
    SURNAME("*******", "surname", "DirectoryString"),
    POSTAL_CODE("********", "PostalCode", "UTF8String"),
    GIVEN_NAME("*******2", "givenName", "DirectoryString"),
    INITIALS("*******3", "initials", "DirectoryString"),
    GENERATION_QUALIFIER("*******4", "generation", "DirectoryString"),
    UNIQUE_IDENTIFIER("*******5", "UniqueIdentifier", "UTF8String"),
    DN_QUALIFIER("*******6", "DN", "PrintableString"),
    PSEUDONYM("********", "Pseudonym", "DirectoryString"),
    POSTAL_ADDRESS("********", "PostalAddress", "PostalAddress"),
    BUSINESS_CATEGORY("********", "BusinessCategory", "DirectoryString"),
    TELEPHONE_NUMBER("********", "TelephoneNumber", "PrintableString"),
    NAME("*******1", "Name", "DirectoryString"),
    ORGANIZATION_IDENTIFIER("********", "organizationIdentifier", "DirectoryString"),
    DESCRIPTION("********", "description", "DirectoryString"),
    UNSTRUCTURED_ADDRESS("1.2.840.113549.1.9.8", "unstructuredAddress", "DirectoryString"),
    UNSTRUCTURED_NAME("1.2.840.113549.1.9.2", "unstructuredName", "IA5String"),
    NAME_AT_BIRTH("********.3.14", "NameAtBirth", "UTF8String"),
    COUNTRY_OF_CITIZENSHIP("*******.*******.4", "CountryOfCitizenship", "PrintableString"),
    COUNTRY_OF_RESIDENCE("*******.*******.5", "CountryOfResidence", "PrintableString"),
    GENDER("*******.*******.3", "gender", "PrintableString"),
    PLACE_OF_BIRTH("*******.*******.2", "PlaceOfBirth", "DirectoryString"),
    DATE_OF_BIRTH("*******.*******.1", "DateOfBirth", "GeneralizedTime"),
    /**
     * EV TLS jurisdictionLocality.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_LOCALITY("*******.4.1.311.********", "JurisdictionLocality", "PrintableString"),
    /**
     * EV TLS jurisdictionState.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_STATE("*******.4.1.311.********", "JurisdictionState", "PrintableString"),
    /**
     * EV TLS jurisdictionCountry.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_COUNTRY("*******.4.1.311.********", "JurisdictionCountry", "PrintableString"),

    /**
     * title ATTRIBUTE ::= {
     * SUBTYPE OF name
     * WITH SYNTAX DirectoryString {ub-title}
     * ID id-at-title
     * }
     */
    TITLE("2.5.4.12", "title", "DirectoryString"),

    /**
     * uri ATTRIBUTE ::= {
     * WITH SYNTAX URI
     * EQUALITY MATCHING RULE uriMatch
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"uri"}
     * ID id-at-uri }
     */
    URI("2.5.4.83", "uri", "DirectoryString"),

    /**
     * urn ATTRIBUTE ::= {
     * SUBTYPE OF uri
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"urn"}
     * ID id-at-urn }
     */
    URN("2.5.4.86", "urn", "DirectoryString"),

    /**
     * telexNumber ATTRIBUTE ::= {
     * WITH SYNTAX TelexNumber
     * LDAP-SYNTAX telexNr.&id
     * LDAP-NAME {"telexNumber"}
     * ID id-at-telexNumber }
     * TelexNumber ::= SEQUENCE {
     * telexNumber PrintableString(SIZE (1..ub-telex-number)),
     * countryCode PrintableString(SIZE (1..ub-country-code)),
     * answerback PrintableString(SIZE (1..ub-answerback)),
     * ... }
     * ub-telex-number INTEGER ::= 14
     * ub-country-code INTEGER ::= 4
     * ub-answerback INTEGER ::= 8
     */
//    TELEX_NUMBER("2.5.4.21", "telexNumber", "TelexNumber"),

    /**
     * url ATTRIBUTE ::= {
     * SUBTYPE OF uri
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"url"}
     * ID id-at-url
     * }
     */
    URL("2.16.840.1.113730.1.2", "url", "DirectoryString");


    public static Map<String, String> getAttributeType2Oid() {
        CertSubjectAttributeTypeEnum[] values = values();
        Map<String, String> map = new LinkedHashMap<>(values.length);
        for (CertSubjectAttributeTypeEnum value : values) {
            map.put(value.attributeType.toUpperCase(), value.OID);
        }
        return map;
    }

    public static Map<String, String> getOid2AttributeType() {
        CertSubjectAttributeTypeEnum[] values = values();
        Map<String, String> map = new HashMap<>(values.length);
        for (CertSubjectAttributeTypeEnum value : values) {
            if ("E".equals(value.attributeType) || "S".equals(value.attributeType)) {
                continue;
            }
            map.put(value.OID, value.attributeType);
        }
        return map;
    }

    public static Map<String, String> getAttributeOid2EncodeType() {
        CertSubjectAttributeTypeEnum[] values = values();
        Map<String, String> map = new HashMap<>(values.length);
        for (CertSubjectAttributeTypeEnum value : values) {
            if ("E".equals(value.attributeType) || "S".equals(value.attributeType)) {
                continue;
            }
            map.put(value.OID, value.encodeType);
        }
        return map;
    }

    CertSubjectAttributeTypeEnum(String OID, String attributeType, String encodeType) {
        this.attributeType = attributeType;
        this.OID = OID;
        this.encodeType = encodeType;
    }

    public String getAttributeType() {
        return attributeType;
    }

    public String getEncodeType() {
        return encodeType;
    }

    public String getOID() {
        return OID;
    }

    private String encodeType;

    private String attributeType;

    private String OID;
}
