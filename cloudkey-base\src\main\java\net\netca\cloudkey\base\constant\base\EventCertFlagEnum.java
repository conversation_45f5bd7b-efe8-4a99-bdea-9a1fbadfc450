package net.netca.cloudkey.base.constant.base;

/**
 * 事件证书项目标志
 */
public enum EventCertFlagEnum implements IBaseEnum {

    //
    NORMAL_PROJECT(0, "一般项目"),
    EVENT_CERT_PROJECT(1, "事件证书项目"),
    ;

    private final Integer code;
    private final String description;

    private EventCertFlagEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
