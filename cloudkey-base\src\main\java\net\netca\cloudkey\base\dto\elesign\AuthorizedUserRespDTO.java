package net.netca.cloudkey.base.dto.elesign;

import lombok.Data;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import static net.netca.cloudkey.base.constant.UserTypeConstant.BELONG_AUTHORIZED_USER;

@Secret
@Data
public class AuthorizedUserRespDTO {

    private Integer id;

    private String name;

    private Integer userType = BELONG_AUTHORIZED_USER.getCode();

    private Integer authorizeStatus;
    @SecurityDataField
    private String phone;

}
