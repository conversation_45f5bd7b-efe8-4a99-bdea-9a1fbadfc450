package net.netca.cloudkey.base.dto.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.netca.cloudkey.base.dto.abstractclass.ICert;
import net.netca.cloudkey.base.dto.abstractclass.IUser;
import net.netca.cloudkey.base.dto.cert.Cert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 绑定分割密钥 - 验证短信验证码
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BindSeparatingKeyReqDTO extends TimestampToken implements IUser {

    @NotBlank(message = "userUid参数不能为空")
    private String userUid;

    @NotBlank(message = "name参数不能为空")
    private String name;

    @NotBlank(message = "phoneValidCode参数不能为空")
    private String phoneValidCode;

    @NotBlank(message = "masterKeypairId参数不能为空")
    private String masterKeypairId;

    @NotBlank(message = "userPin参数不能为空")
    private String userPin;

    @NotNull(message = "cert参数不能为空")
    private Cert cert;
}
