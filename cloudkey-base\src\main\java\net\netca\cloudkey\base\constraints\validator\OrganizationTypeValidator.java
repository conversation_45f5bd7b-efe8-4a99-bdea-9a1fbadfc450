package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.OrganizationType;
import net.netca.cloudkey.base.constraints.check.CheckType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class OrganizationTypeValidator implements ConstraintValidator<OrganizationType, Integer> {


    @Override
    public void initialize(OrganizationType constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (Objects.nonNull(value)) {
            isValid = CheckType.ORGANIZATIONTYPE.check(value + "");
        }
        return isValid;
    }
}
