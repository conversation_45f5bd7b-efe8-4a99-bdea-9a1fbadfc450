package net.netca.cloudkey.base.dto.config.process;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @Description: 流程配置查询DTO
 * @Author: dyr
 * @CreateDate: 2020/8/11 17:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessSearchReqDTO {

    /**
     * 流程类型
     */
    private Integer[] processType;

    /**
     * 操作顺序
     */
    private String[] operationSequence;

    /**
     * 流程对应平台
     */
    private Integer[] processPlatforms;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date modifyStartTime;

    /**
     * 最新一次修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date modifyEndTime;

    public String appendOperationSequence() {
        return StringUtils.join(this.operationSequence, ",");
    }


}
