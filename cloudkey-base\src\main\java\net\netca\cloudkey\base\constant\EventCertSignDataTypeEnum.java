package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 待签数据类型
 *
 * <AUTHOR>
 * @date 2022/12/15 10:36
 */
@Getter
@AllArgsConstructor
public enum EventCertSignDataTypeEnum implements IBaseEnum {
    //
    DIGEST(1, "digest"),
    ORIGINAL(2, "original"),
    ;

    private final Integer code;
    private final String description;

}
