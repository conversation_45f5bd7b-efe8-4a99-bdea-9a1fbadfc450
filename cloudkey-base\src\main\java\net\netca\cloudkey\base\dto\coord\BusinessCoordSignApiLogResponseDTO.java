package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class BusinessCoordSignApiLogResponseDTO {
    @ApiModelProperty(value = "日志主键ID")
    private Long id;

    @ApiModelProperty(value = "第三方系统认证id")
    private String authId;

    @ApiModelProperty(value = "IP 地址 + 端口号")
    private String clientIpAddrWithPort;

    @ApiModelProperty(value = "接口类型")
    private Integer logType;

    @ApiModelProperty(value = "任务ID")
    @JsonSerialize(using = StringSerializer.class)
    private Long taskId;

    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    @ApiModelProperty(value = "第三方系统编号")
    private String thirdSystemCode;

    @ApiModelProperty(value = "请求地址")
    private String reqUrl;

    @ApiModelProperty(value = "请求时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reqTime;

    @ApiModelProperty(value = "响应状态：0-成功，1-失败")
    private Integer respStatus;

    @ApiModelProperty(value = "请求耗时（单位：ms）")
    private Integer duration;

    @ApiModelProperty(value = "调用方向：0-作为服务端，1-作为客户端")
    private Integer direction;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
