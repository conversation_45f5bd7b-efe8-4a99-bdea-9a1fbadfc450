package net.netca.test.util;

import net.netca.sdk.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 有关配置文件操作的工具类
 *
 * <AUTHOR>
 * @date 2019-12-30 9:46
 */
public class PropertiesUtil {
    private PropertiesUtil() {

    }

    private static final String CONFIG_NAME = "/config.properties";

    private static Properties properties;

    private static boolean cacheFlag = true;

    static {
        properties = new Properties();
        reload();
    }

    private static void reload(){
        InputStream in = null;
        try {
            in = PropertiesUtil.class.getResourceAsStream(CONFIG_NAME);
            if (in != null) {
                properties.load(in);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 根据key获取对应的value
     *
     * @param key
     * @return
     */
    public static String getProperty(String key) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("key must not be null");
        }
        if (!cacheFlag){
            reload();
        }
        return properties.getProperty(key);
    }

    public static boolean isCache(){
        return cacheFlag;
    }


}
