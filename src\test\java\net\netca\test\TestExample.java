package net.netca.test;

/**
 * <AUTHOR>
 * @date 2020-07-02 17:01
 */
public class TestExample {
    public static class Parent {
        public void say() {
            test();
        }

        protected void test() {
            System.out.println("parent test");
        }
    }

    public static class Son extends Parent {
        @Override
        protected void test() {
            System.out.println("son test");
        }
    }

    public static void main(String[] args) {
        new Son().say();
    }




}
