package net.netca.cloudkey.base.dto.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 17:54 2020/12/7
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class ApproveVO {

    private Integer id;
    private Integer userId;
    private Integer certId;
    private String userUid;
    private String userName;
    private String signCertSn;
    private String encCertSn;
    private Integer businessType;
    private Integer approveLevel;
    private Integer approvePresentStatus;
    private Integer presentOperatorId;
    private Integer presentApproveProgress;
    private Integer approveStatus;
    private Integer canApprove; //0不可审核 1可审核

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmt_create;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmt_modified;
}
