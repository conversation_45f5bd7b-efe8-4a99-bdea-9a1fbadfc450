package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 被授权人检验等级 0 不检验 1检验是否为相同职业
 * <AUTHOR>
 */

public enum AuthorizeCheckLevelEnum implements IBaseEnum {

    //
    NO_CHECK(0, "不做检验"),
    OCCUPATION(1, "检验是否为相同职业")
    ;

    private Integer code;
    private String description;

    AuthorizeCheckLevelEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

}
