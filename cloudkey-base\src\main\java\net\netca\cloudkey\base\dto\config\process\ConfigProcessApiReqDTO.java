package net.netca.cloudkey.base.dto.config.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.constraints.NotNull;

/**
 * @Description: 获取流程配置信息API接口请求DTO
 * @Author: dyr
 * @CreateDate: 2020/8/17 17:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessApiReqDTO extends TimestampToken {

    /**
     * 平台code
     */
    @NotNull(message = "平台id不能为空")
    private Integer platform;

}
