package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.GenderValidator;
import net.netca.cloudkey.base.constraints.validator.OrganizationTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对机构类型的校验注解
 * @see OrganizationTypeValidator
 */
@Documented
@Constraint(validatedBy = GenderValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface Gender {

    String message() default "性别值非法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
