package net.netca.sdk.codec.cmp;


import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

/**
 * {@link P10CertReqMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/9 17:58
 */
public class P10CertReqMessageCodec extends CmpMessageCodec {

    private P10CertReqMessageCodec() {

    }

    private static P10CertReqMessageEncoder encoder;


    private static P10CertReqMessageDecoder decoder;

    public static P10CertReqMessageCodec createEncoder(CmpMessageConfigManagement configManagement) {
        encoder = new P10CertReqMessageEncoder(configManagement);
        return new P10CertReqMessageCodec();
    }

    public static P10CertReqMessageCodec createDecoder(CmpMessageConfigManagement configManagement) {
        decoder = new P10CertReqMessageDecoder(configManagement);
        return new P10CertReqMessageCodec();
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }

    final static class P10CertReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {

        public P10CertReqMessageDecoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage decodedMsg) throws CmpDecodeException {
            checkDecodeArgs(context, decodedMsg);
            if (context instanceof SingleCertReqContext) {

                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                if (singleCertReqContext.getCmpReqMessage() instanceof P10CertReqMessage) {
                    try {
                        dealWithP10CertReqOrKeyUpdateRespPKIMessage(singleCertReqContext, decodedMsg);
                        singleCertReqContext.setDecoded(true);
                    } catch (Exception e) {
                        throw new CmpDecodeException(e.getMessage(), e);
                    }
                }
            }
        }
    }


    final static class P10CertReqMessageEncoder extends CmpMessageToAnyObjectEncoder {

        P10CertReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }


        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws CmpEncodeException {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof P10CertReqMessage) {

                P10CertReqMessage p10CertReqMessage = (P10CertReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                try {
                    singleCertReqContext.setEncodeResult(generateRegisteredReqDataByP10(p10CertReqMessage));
                }catch (Exception e){
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }

        /**
         * 通过 {@link CmpMessage} 产生 cmp 的证书注册 请求数据 PKIMessage
         *
         * @param p10CertReqMessage
         * @return
         * @throws Exception
         */
        private PKIMessage generateRegisteredReqDataByP10(P10CertReqMessage p10CertReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstanceBuilder(p10CertReqMessage)
                    .requestExtensions(p10CertReqMessage.getRequestExtensions())
                    .customFreeText(p10CertReqMessage.getCustomFreeText())
                    .build();
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            PKIBody pkiBody = pkiMessageGenerator.generateP10crPKIBody(p10CertReqMessage.getP10Base64());
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }


    }

}
