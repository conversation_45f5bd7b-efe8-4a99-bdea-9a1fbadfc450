package net.netca.cloudkey.base.dto.operator;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @descriptions:
 * @date: 2019/1/24
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperatorSearchReqDTO {

    private String operatorName;

    private String certSn;

    private Integer type;

    private Integer status;

    private Integer districtId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date modifyStartTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date modifyEndTime;

}
