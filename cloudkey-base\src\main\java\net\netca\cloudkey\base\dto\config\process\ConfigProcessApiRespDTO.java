package net.netca.cloudkey.base.dto.config.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 流程配置API返回DTO
 * @Author: dyr
 * @CreateDate: 2020/8/18 9:35
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessApiRespDTO {

    private Integer platform;

    private List<ConfigProcessBaseDTO> process;

}
