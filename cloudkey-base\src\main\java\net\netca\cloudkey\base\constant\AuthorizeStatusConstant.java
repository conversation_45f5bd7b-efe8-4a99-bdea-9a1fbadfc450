package net.netca.cloudkey.base.constant;

import com.google.common.base.Preconditions;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;

/**
 * @descriptions:
 * 授权记录状态，以及后续允许操作
 * @see AuthorizeOperationConstant
 *
 * 授权过期状态，在业务处理过程中判断以及变更
 * 也就是 触发的已授权状态，会变更至 授权过期
 *
 * @date: 2019/4/23
 * @author: cxy
 */
public enum AuthorizeStatusConstant implements IBaseEnum {
    /**
     * 初始录入信息
     * 确认授权： 录入（未授权） -> 已授权
     * 删除授权： 录入（未授权） -> 删除授权
     */
    AUTHORIZE_LOGIN(0, "录入", Arrays.asList(
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_COFIRM,
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_DELETE)),

    /**
     * 正常授权状态
     * 删除授权： 已授权 -> 删除授权
     * 冻结授权： 已授权 -> 冻结授权
     * 授权解锁： 已授权 -> 已授权  ,更换pin
     */
    AUTHORIZE_CONFIRM(1, "已授权", Arrays.asList(
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_DELETE,
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_FREEZE,
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_UNLOCK)),

    /**
     * 解冻    ： 冻结授权  -> 已授权 (可能变更至授权过期)
     * 删除授权： 冻结授权 -> 删除授权
     * 同意授权： 冻结授权(取消授权，用作兼容, 更换pin) -> 已授权 (可能变更至授权过期)
     */
    AUTHORIZE_FREEZE(2,"冻结授权", Arrays.asList(
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_ACTIVE,
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_COFIRM,
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_DELETE)),

    /**
     * 授权过期
     * 该记录可查询可索引，可重新授权
     * 删除授权： 授权过期 -> 删除授权
     * 重新授权： 授权过期 -> 已授权，不改pin
     */
    AUTHORIZE_EXPIRE(3, "授权过期", Collections.singletonList(
            AuthorizeOperationConstant.AUTHORIZE_OPERATION_DELETE)),

    /**
     * 逻辑删除
     * 不可查询
     */
    AUTHORIZE_DELETE(4,"删除授权", Collections.emptyList());

    private Integer code;
    private String description;
    private List<AuthorizeOperationConstant> allowOperations;

    AuthorizeStatusConstant(Integer code, String description, List<AuthorizeOperationConstant> allowOperations) {
        this.code = code;
        this.description = description;
        this.allowOperations = allowOperations;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public List<AuthorizeOperationConstant> getAllowOperations() {
        return this.allowOperations;
    }

    public static AuthorizeStatusConstant getAuthorizeStatusConstant(Integer code) {
        Preconditions.checkNotNull(code, "code is null");
        EnumSet<AuthorizeStatusConstant> authorizeStatusConstants = EnumSet.allOf(AuthorizeStatusConstant.class);
        for (AuthorizeStatusConstant authorizeStatusConstant : authorizeStatusConstants) {
            if (authorizeStatusConstant.getCode().equals(code)) {
                return authorizeStatusConstant;
            }
        }
        throw new CloudKeyRuntimeException("error code ...");
    }


}
