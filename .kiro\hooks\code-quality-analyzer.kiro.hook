{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors Java source code files for changes and provides automated code quality analysis including code smells, design patterns, and best practices suggestions", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.java", "**/src/main/java/**/*.java", "**/src/test/java/**/*.java"]}, "then": {"type": "askAgent", "prompt": "Analyze the provided Java code for potential improvements. Provide specific, actionable, and **pragmatic** suggestions that enhance maintainability, performance, and security. **Focus on practical solutions and avoid over-engineering.**\n\nYour analysis should cover these key areas:\n\n1.  **Code Refinement & Best Practices**\n    * **Lombok Integration**: Identify opportunities to simplify the code and reduce boilerplate by using Lombok annotations (e.g., `@Data`, `@Value`, `@Builder`, `@Slf4j`).\n    * **Code Smells**: Pinpoint issues like long methods, large classes, duplicate code, and overly complex conditional logic.\n    * **Java Best Practices**: Verify adherence to conventions like proper exception handling, use of `try-with-resources`, null safety, clear naming, and **SOLID** principles.\n\n2.  **Architecture & Design**\n    * **Design Patterns**: Suggest appropriate design patterns only where they provide a clear benefit and simplify the structure.\n    * **Maven Multi-module Structure**: Ensure module dependencies are logical and that the code complies with the intended layered architecture.\n\n3.  **Spring Boot & Security**\n    * **Framework Usage**: Check for correct use of Spring Boot annotations (`@Service`, `@Component`, etc.), dependency injection best practices, and configuration management.\n    * **Performance**: Identify potential performance bottlenecks and suggest optimizations.\n    * **Security**: Look for common vulnerabilities, paying special attention to the system's context as a **PKI** application.\n\nAll suggested changes must maintain the existing functionality."}}