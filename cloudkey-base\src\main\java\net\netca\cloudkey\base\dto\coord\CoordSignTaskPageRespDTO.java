package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoordSignTaskPageRespDTO {

    @ApiModelProperty(value = "任务id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    /**
     * 第三方业务流水号
     */
    @ApiModelProperty(value = "第三方业务流水号")
    private String thirdOrderId;

    /**
     * 任务标题
     */
    @ApiModelProperty(value = "任务标题")
    private String title;

    /**
     * 任务截止时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "任务截止时间")
    private LocalDateTime expireTime;

    /**
     * 任务状态：0-未签名，1-进行中，2-已拒绝，3-已过期，4-已撤回，5-已完成
     */
    @ApiModelProperty(value = "任务状态：0-未签名，1-进行中，2-已拒绝，3-已过期，4-已撤回，5-已完成")
    private Integer taskStatus;

    /**
     * 环节数量
     */
    @ApiModelProperty(value = "环节数量")
    private Integer segmentNum;

    /**
     * 任务状态描述
     */
    @ApiModelProperty(value = "任务状态描述")
    private String taskStatusDesc;

    @ApiModelProperty(value = "推送结果")
    private Integer pushResult;

    @ApiModelProperty(value = "推送结果描述")
    private String pushResultMsg;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后推送时间")
    private LocalDateTime pushTime;

    /**
     * 当前处理环节ID
     */
    @ApiModelProperty(value = "当前处理环节ID")
    private Integer curSegmentId;

    /**
     * 当前处理环节名称
     */
    @ApiModelProperty(value = "当前处理环节名称")
    private String curSegmentName;

    /**
     * 签名类型：0-P7签名，1-电子签章
     */
    @ApiModelProperty(value = "签名类型：0-P7签名，1-电子签章")
    private Integer signType;

    /**
     * 患者标识
     */
    private String patientId;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 发起者UID
     */
    private String starterUid;

    /**
     * 发起者名称
     */
    private String starterName;

    /**
     * 若taskStatus=5，且signType=0则返回签名值
     */
    private String signature;

    /**
     * 若taskStatus=5，且signType=1则返回已签章文件base64
     */
    private String fileBase64;

    /**
     * 若taskStatus=5，则返回存证结果ID
     */
    private String itemIndex;

    /**
     * 任务创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 任务发起时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 签名任务环节信息
     */
    private List<CoordSignSegmentDto> segments;
}

