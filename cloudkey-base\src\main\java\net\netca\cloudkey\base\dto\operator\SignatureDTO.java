package net.netca.cloudkey.base.dto.operator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;

/**
 * @descriptions:
 * @date: 2019/3/4
 * @author: SaromChars
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class SignatureDTO implements ISignature {

    private transient OperatorSignature operatorSignature;

    @Override
    public OperatorSignature getOperatorSignature() {
        return this.operatorSignature;
    }
}
