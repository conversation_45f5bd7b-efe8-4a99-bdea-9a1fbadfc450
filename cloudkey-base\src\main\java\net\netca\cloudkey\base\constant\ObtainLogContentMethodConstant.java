package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum ObtainLogContentMethodConstant implements IBaseEnum {
	EmptyParameter(-1, "不获取日志内容"),
	MethodFirstArgument(0, "将被拦截方法的第一个参数作为日志内容"), 
	MethodAllArguments(1, "将被拦截方法的全部参数作为日志内容"), 
	HttpRequestParameter(2, "将HTTP请求中的参数作为日志内容");
	
	private Integer code;
	private String description;
	
	private ObtainLogContentMethodConstant(Integer code, String description) {
		this.code = code;
		this.description = description;
	}
	@Override
	public Integer getCode() {
		return code;
	}
	@Override
	public String getDescription() {
		return description;
	}
}
