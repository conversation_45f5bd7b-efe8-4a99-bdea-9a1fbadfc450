package net.netca.test.asn1.format;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.UTF8String;
import net.netca.pki.encoding.asn1.Unknown;
import net.netca.pki.encoding.asn1.pki.*;
import net.netca.test.constant.GeneralNameEnum;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class GeneralNameFormat {
	
	private GeneralNameEnum type;
	
	private Object value;
	
	public GeneralNameEnum getType() {
		return type;
	}

	public void setType(GeneralNameEnum type) {
		this.type = type;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}
	
	public GeneralName generateGeneralName() throws PkiException {
		return (GeneralName) type.generalValue(value);
	}
	
	public static GeneralNameFormat deserialize(GeneralName generalName) throws PkiException {
		GeneralNameFormat generalNameFormat = new GeneralNameFormat();
		int type = generalName.getType();
		switch(type){
			case GeneralName.OTHERNAME_TYPE:
				OtherName otherName = generalName.getOtherName();
				generalNameFormat.setType(GeneralNameEnum.otherName);
				String otherNameType = otherName.getType();

				String value = null;
				try {
					UTF8String utf8String = (UTF8String) otherName.getValue();
					value = utf8String.getString();
				}catch (ClassCastException e){
					Unknown unknown = (Unknown)otherName.getValue();
					value = new String(unknown.getEncode(), StandardCharsets.UTF_8);
				}
				OtherNameFormat otherNameFormat = new OtherNameFormat();
				otherNameFormat.setTypeId(otherNameType);
				otherNameFormat.setValue(value);
				generalNameFormat.setValue(otherNameFormat);
				break;
			case GeneralName.RFC822NAME_TYPE:
				String rfc822Name = generalName.getRFC822Name();
				generalNameFormat.setType(GeneralNameEnum.rfc822Name);
				generalNameFormat.setValue(rfc822Name);
				break;
			case GeneralName.DNSNAME_TYPE:
				String dnsName = generalName.getDNSName();
				generalNameFormat.setType(GeneralNameEnum.dNSName);
				generalNameFormat.setValue(dnsName);
				break;
			case GeneralName.DIRECTORYNAME_TYPE:
				X500Name x500Name = generalName.getDirectoryName();
				
				RDNSequenceFormat rdn = new RDNSequenceFormat();
				List<RelativeDistinguishedNameFormat> relativeDistinguishedNames = new ArrayList<>();
				int x500NameSize = x500Name.size();
				for(int x500NameSizeIndex=0;x500NameSizeIndex<x500NameSize;x500NameSizeIndex++){
					RelativeDistinguishedName relativeDistinguishedName = x500Name.get(x500NameSizeIndex);
					RelativeDistinguishedNameFormat relativeDistinguishedNameFormat = new RelativeDistinguishedNameFormat();
					
					List<AttributeTypeValueFormat> attributeTypeValueFormats = new ArrayList<>();
					int attrSize = relativeDistinguishedName.size();
					for(int attrSizeIndex=0;attrSizeIndex<attrSize;attrSizeIndex++){
						AttributeTypeAndValue attributeTypeAndValue = relativeDistinguishedName.get(attrSizeIndex);
						AttributeTypeValueFormat attributeTypeValueFormat = AttributeTypeValueFormat.deserialize(attributeTypeAndValue);
						attributeTypeValueFormats.add(attributeTypeValueFormat);
					}
					
					relativeDistinguishedNameFormat.setAttributeTypeValueFormat(attributeTypeValueFormats);
					relativeDistinguishedNames.add(relativeDistinguishedNameFormat);
				}
				
				rdn.setRelativeDistinguishedNames(relativeDistinguishedNames);
				
				generalNameFormat.setType(GeneralNameEnum.directoryName);
				generalNameFormat.setValue(rdn);
				break;
			case GeneralName.URI_TYPE:
				String url = generalName.getURI();
				generalNameFormat.setType(GeneralNameEnum.uniformResourceIdentifier);
				generalNameFormat.setValue(url);
				break;
			case GeneralName.IPADDRESS_TYPE:
				byte[] ipAddress = generalName.getIPAddress();
				String ip = Hex.encode(false, ipAddress);
				generalNameFormat.setType(GeneralNameEnum.iPAddress);
				generalNameFormat.setValue(ip);
				break;
			case GeneralName.REGISTEREDID_TYPE:
				String registerdId = generalName.getRegisteredID();
				generalNameFormat.setType(GeneralNameEnum.registeredID);
				generalNameFormat.setValue(registerdId);
				break;
			
		}
		
		return generalNameFormat;
	}
	
	
}
