package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.TelPhone;
import net.netca.cloudkey.base.constraints.check.CheckType;
import net.netca.cloudkey.base.util.CustomStringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class TelPhoneValidator implements ConstraintValidator<TelPhone, String> {

    @Override
    public void initialize(TelPhone constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (!CustomStringUtils.isBlank(value)) {
            isValid = CheckType.TELNUM.check(value);
        }
        return isValid;
    }
}
