package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(description = "签名任务列表")
public class CoSignTaskDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 发起人姓名
     */
    @ApiModelProperty(value = "发起人姓名")
    private String starterName;

    /**
     * 发起人工号
     */
    @ApiModelProperty(value = "发起人工号")
    private String starterUid;
    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 签名任务状态
     */
    @ApiModelProperty(value = "任务状态：0-未签名，1-进行中,2-已拒绝,3-已过期,5-已完成")
    private Integer taskStatus;
    /**
     * 签名任务标题
     */
    @ApiModelProperty(value = "签名任务标题")
    private String title;

}
