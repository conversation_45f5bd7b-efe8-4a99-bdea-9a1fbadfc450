package net.netca.cloudkey.base.dto.certalertpolicy;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/6 10:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CertAlertPolicyRespDTO {

    /**
     * 策略ID
     */
    private Integer id;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 通知间隔时间（单位：天）
     */
    private Integer notifyInterval;

    /**
     * 证书过期报警天数
     */
    private Integer certAlertDays;

    /**
     * 通知时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm")
    private LocalTime notifyTime;

    /**
     * 通知号码
     */
    private String notifyNumber;

    /**
     * 是否启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private Integer districtId;

    private String districtName;

    /**
     * 短信平台
     */
    private String smsPlatform;

}
