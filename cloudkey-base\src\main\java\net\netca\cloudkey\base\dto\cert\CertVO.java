package net.netca.cloudkey.base.dto.cert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * @descriptions:
 * @date: 2019/1/31
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class CertVO {

    private int id;

    private int userId;

    private String userUid;

    private String userUidAlias;

    private String userName;

    @SecurityDataField
    private String phone;

    private String signCertSn;

    private String encCertSn;

    private int type;

    private int locked;

    private int status;

    private String memo;

    private String cloudkeyProjectId;

    private String picData;

    private Integer gmStampStatus;

    private int buttonType;// 前端证书按钮风格（续期，注销）：1正常，2初审按钮，0没有

    private String hasBindDevice;

    private String hasBindSeparatingKey;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityEnd;

    /**
     * 已预约状态，0未预约，1已预约
     *
     * @see net.netca.cloudkey.base.constant.CertReserveTaskStatusEnum
     */
    private Integer reserveStatus;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date reserveRequestTime;

    private Integer reserveRequestType;

    // 预约注销原因(暂时只有注销才有 所以命名带 revoke)
    private Integer reserveRevokeReason;

    private String reserveTaskMemo;

    private String reserveTaskOtherInfo;

    /**
     * 院区ID
     */
    private Integer districtId;

}
