package net.netca.sdk.codec.cmp;

import lombok.Builder;
import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.pki.CertificationRequest;
import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.SignatureAlgorithmEnum;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.Extension;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.Validity;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.sneakythrows.ThrowingFunction;
import net.netca.sdk.util.*;
import net.netca.sdk.util.json.JsonUtils;
import org.bouncycastle.asn1.*;
import org.bouncycastle.asn1.cmp.*;
import org.bouncycastle.asn1.crmf.*;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Supplier;

/**
 * CMP的 {@link PKIMessage} 生成器
 *
 * @author: zys
 */
public class PKIMessageGenerator {

    private CmpMessageConfigManagement configManagement;

    /**
     * 第三方通讯证书签名所用的算法
     *
     * @see SignatureAlgorithmEnum
     */
    private SignatureAlgorithmEnum serverCommCertSignAlgoEnum;


    PKIMessageGenerator(CmpMessageConfigManagement configManagement) {
        this.configManagement = configManagement;
        this.serverCommCertSignAlgoEnum = SignatureAlgorithmEnum.getByName(configManagement.getThirdPartyServerCommCertSignAlgoName());
    }

    public void destroy() {
        // good to gc
        this.configManagement = null;
        this.serverCommCertSignAlgoEnum = null;
    }


    /**
     * 创建CMP协议头部的参数
     */
    @Builder
    static class PKIHeaderParams {
        /**
         * 发送者
         */
        String senderStr;

        /**
         * 接受者
         */
        String recipientStr;

        /**
         * 协议版本 传空值默认： {@link PKIHeader#CMP_2000}
         */
        Integer pvno;

        /**
         * 是否设置 TransactionID
         * （TransactionID 是可选项，若设置了，request 将携带 TransactionID， 且 response 必须带有相同的 TransactionID）
         */
        boolean isSetTransactionID;

        /**
         * 是否设置 senderNonce，用于防止重放攻击
         */
        boolean isSetSenderNonce;

        /**
         * The protectionAlg field specifies the algorithm used to protect the message.
         */
        boolean unknownProtectionAlg;

        /**
         * 是否推送至 LDAP
         */
        boolean isPublishLdap;

        /**
         * 业务扩展   由于P10内无法存放额外的业务扩展，所以需要将业务扩展放到 CMP Header的 generalInfo 中
         */
        List<Extension> requestExtensions;


        /**
         * {@link CustomFreeText}
         */
        CustomFreeText customFreeText;

        public static PKIHeaderParams newInstance(CmpMessage cmpMessage) {
            return PKIHeaderParams.builder()
                    .senderStr(cmpMessage.getSenderStr())
                    .recipientStr(cmpMessage.getRecipientStr())
                    .pvno(cmpMessage.getPvno())
                    .isSetSenderNonce(cmpMessage.isSetSenderNonce())
                    .isSetTransactionID(cmpMessage.isSetTransactionID())
                    .unknownProtectionAlg(cmpMessage.isUnknownProtectionAlg())
                    .isPublishLdap(cmpMessage.isPublishLdap())
                    .build();
        }

        public static PKIHeaderParamsBuilder newInstanceBuilder(CmpMessage cmpMessage) {
            return PKIMessageGenerator.PKIHeaderParams.builder()
                    .senderStr(cmpMessage.getSenderStr())
                    .recipientStr(cmpMessage.getRecipientStr())
                    .pvno(cmpMessage.getPvno())
                    .isSetSenderNonce(cmpMessage.isSetSenderNonce())
                    .isSetTransactionID(cmpMessage.isSetTransactionID())
                    .unknownProtectionAlg(cmpMessage.isUnknownProtectionAlg())
                    .isPublishLdap(cmpMessage.isPublishLdap());
        }
    }

    @Builder
    static class CertReqMessagesParams {

        /**
         * 主题项
         */
        SubjectInfo subject;

        /**
         * 公钥数据
         */
        byte[] publicKey;

        /**
         * 私钥数据
         */
        byte[] privateKey;

        /**
         * 证书序列号
         */
        String certSn;

        /**
         * 颁发者
         */
        String issuer;

        /**
         * 证书请求Id
         */
        Long certRequestId;

        /**
         * 证书有效期 {@link Validity}
         */
        Validity validity;

        /**
         * 证书扩展项  {@link List<Extension> }
         */
        List<Extension> extensionList;
    }


    /**
     * 创建CMP协议头部
     * 根据 {@link PKIHeaderParams} 生成 {@link PKIHeader}
     *
     * @param pkiHeaderParams {@link PKIHeaderParams}
     * @return {@link  PKIHeader}
     */
    public PKIHeader generatePKIHeader(PKIHeaderParams pkiHeaderParams) {
        Assert.hasText(pkiHeaderParams.senderStr, "pkiHeaderParams.senderStr is empty");
        Assert.hasText(pkiHeaderParams.recipientStr, "pkiHeaderParams.recipientStr is empty");
        final GeneralName sender = new GeneralName(new X500Name(pkiHeaderParams.senderStr));
        Objects.requireNonNull(sender);
        final GeneralName recipient = new GeneralName(new X500Name(pkiHeaderParams.recipientStr));
        Objects.requireNonNull(recipient);

        PKIHeaderBuilder pkiHeaderBuilder;
        if (pkiHeaderParams.pvno != null) {
            pkiHeaderBuilder = new PKIHeaderBuilder(pkiHeaderParams.pvno, sender, recipient);
        } else {
            pkiHeaderBuilder = new PKIHeaderBuilder(PKIHeader.CMP_2000, sender, recipient);
        }
        Objects.requireNonNull(pkiHeaderBuilder);

        pkiHeaderBuilder.setMessageTime(new DERGeneralizedTime(new Date()));
        if (pkiHeaderParams.isSetTransactionID) {
            byte[] transactionId = CryptoUtils.generateRandomLongStream(10).toString().getBytes(StandardCharsets.UTF_8);
            Objects.requireNonNull(transactionId);
            pkiHeaderBuilder.setTransactionID(new DEROctetString(transactionId));
        }
        if (pkiHeaderParams.unknownProtectionAlg) {
            pkiHeaderBuilder.setProtectionAlg(new AlgorithmIdentifier(new ASN1ObjectIdentifier("*******")));
        } else {
            pkiHeaderBuilder.setProtectionAlg(new AlgorithmIdentifier(new ASN1ObjectIdentifier(serverCommCertSignAlgoEnum.getCode())));
        }
        if (pkiHeaderParams.isSetSenderNonce) {
            byte[] senderNonce = new byte[16];
            CryptoUtils.generateRandomBytes(senderNonce);
            pkiHeaderBuilder.setSenderNonce(new DEROctetString(senderNonce));
        }
        if (!pkiHeaderParams.isPublishLdap) {
            ASN1Encodable[] array = {new ASN1Integer(0), new DERSequence(new ASN1Integer(0))};
            InfoTypeAndValue infoTypeAndValue = new InfoTypeAndValue(CMPObjectIdentifiers.regCtrl_pkiPublicationInfo,
                    PKIPublicationInfo.getInstance(new DERSequence(array)));
            pkiHeaderBuilder.setGeneralInfo(infoTypeAndValue);
        }

        if (pkiHeaderParams.requestExtensions != null && !pkiHeaderParams.requestExtensions.isEmpty()) {
            InfoTypeAndValue[] infoTypeAndValues = pkiHeaderParams.requestExtensions.stream()
                    .map(ThrowingFunction.unchecked(
                            extension -> new InfoTypeAndValue(new ASN1ObjectIdentifier(extension.getOid()),
                                    ASN1ObjectIdentifier.fromByteArray(new net.netca.pki.encoding.asn1.pki.Extension(extension.getOid(),
                                            extension.isCritical(),
                                            extension.getContent()).getASN1Object().encode()))))
                    .toArray(InfoTypeAndValue[]::new);
            pkiHeaderBuilder.setGeneralInfo(infoTypeAndValues);
        }

        if (pkiHeaderParams.customFreeText != null) {
            pkiHeaderBuilder.setFreeText(new PKIFreeText(new String[]{JsonUtils.toJson(pkiHeaderParams.customFreeText)}));
        }
        return pkiHeaderBuilder.build();
    }

    /**
     * 生成p10cr的{@link PKIBody}
     *
     * @param p10Str base64的p10
     * @return {@link PKIBody}
     * @throws PkiException
     */
    public PKIBody generateP10crPKIBody(String p10Str) throws PkiException {
        Objects.requireNonNull(p10Str, "p10Str is allowed to be null");
        org.bouncycastle.asn1.pkcs.CertificationRequest certificationRequest = org.bouncycastle.asn1.pkcs.CertificationRequest.getInstance(new CertificationRequest(p10Str).derEncode());
        return new PKIBody(PKIBody.TYPE_P10_CERT_REQ, certificationRequest);
    }


    /**
     * 生成kur的{@link PKIBody}
     *
     * @param certReqMessagesParams {@link CertReqMessagesParams}
     * @return {@link PKIBody}
     * @throws Exception e
     */
    public PKIBody generateKurPKIBody(CertReqMessagesParams certReqMessagesParams) throws Exception {
        CertReqMessages certReqMessages = generateCertReqMessages(certReqMessagesParams);
        return new PKIBody(PKIBody.TYPE_KEY_UPDATE_REQ, certReqMessages);
    }


    /**
     * 生成krr的 {@link PKIBody}
     *
     * @param certReqMessagesParams {@link CertReqMessagesParams}
     * @return {@link PKIBody}
     * @throws Exception e
     */
    public PKIBody generateKrrPKIBody(CertReqMessagesParams certReqMessagesParams) throws Exception {
        CertReqMessages certReqMessages = generateCertReqMessages(certReqMessagesParams);
        return new PKIBody(PKIBody.TYPE_KEY_RECOVERY_REQ, certReqMessages);
    }


    /**
     * 生成 rr 的{@link PKIBody}
     *
     * @param issuerStr 颁发者
     * @param certSn    证书序列号
     * @param reason    证书注销|挂失|解挂的原因
     * @return {@link PKIBody}
     * @throws IOException e
     */
    public PKIBody generateRRPkiBody(String issuerStr, String certSn, Integer reason) throws IOException {
        X500Name issuer = new X500Name(issuerStr);
        CertTemplate certTemplate =
                new CertTemplateBuilder()
                        .setIssuer(issuer)
                        .setSerialNumber(new ASN1Integer(new BigInteger(certSn, 16)))
                        .build();
        ASN1Enumerated asn1Enumerated = new ASN1Enumerated(reason);
        ExtensionsGenerator x509ExtensionsGenerator = new ExtensionsGenerator();
        x509ExtensionsGenerator.addExtension(org.bouncycastle.asn1.x509.Extension.reasonCode, false, asn1Enumerated.getEncoded());
        ASN1EncodableVector asn1EncodableVector = new ASN1EncodableVector();
        asn1EncodableVector.add(certTemplate);
        asn1EncodableVector.add(x509ExtensionsGenerator.generate());

        RevDetails revDetails = RevDetails.getInstance(new DERSequence(asn1EncodableVector));
        RevReqContent revReqContent = new RevReqContent(revDetails);
        return new PKIBody(PKIBody.TYPE_REVOCATION_REQ, revReqContent);
    }


    /**
     * 使用系统间通讯证书对 {@link PKIMessage} 签名
     *
     * @param pkiHeader {@link PKIHeader}
     * @param pkiBody   {@link PKIBody}
     * @return {@link PKIMessage}
     * @throws Exception e
     */
    public PKIMessage signPKIMessage(PKIHeader pkiHeader, PKIBody pkiBody) throws Exception {
        Objects.requireNonNull(pkiHeader, "pkiHeader isn't allowed to be null");
        Objects.requireNonNull(pkiBody, "pkiBody isn't allowed to be null");
        PrivateKeyInfo thirdPartyServerCommCertPrivateKeyInfo = configManagement.getThirdPartyServerCommCertPrivateKeyInfo();
        byte[] eeSignature = CryptoUtils.p1SignWithOid(thirdPartyServerCommCertPrivateKeyInfo, getProtection(pkiHeader, pkiBody), serverCommCertSignAlgoEnum.getCode());
        return new PKIMessage(pkiHeader, pkiBody, new DERBitString(eeSignature), cmpCertSupplier().get());
    }

    /**
     * 构造证书确认的 {@link PKIBody}
     *
     * @param certRequestId 证书请求 Id
     * @return {@link PKIBody}
     */
    public PKIBody generateCertConfReq(Long certRequestId) {
        CertStatus certStatus = new CertStatus(new byte[]{}, BigInteger.valueOf(certRequestId));
        DERSequence content = new DERSequence(certStatus);
        CertConfirmContent certConfirmContent = CertConfirmContent.getInstance(content);
        return new PKIBody(PKIBody.TYPE_CERT_CONFIRM, certConfirmContent);
    }


    /**
     * 构造证书轮询的 {@link PKIBody}
     *
     * @param certRequestId 证书请求 Id
     * @return {@link PKIBody}
     */
    public PKIBody generatePollReq(Long certRequestId) {
        PollReqContent pollReqContent = new PollReqContent(new ASN1Integer(BigInteger.valueOf(certRequestId)));
        return new PKIBody(PKIBody.TYPE_POLL_REQ, pollReqContent);
    }

    private Supplier<CMPCertificate[]> cmpCertSupplier() {
        List<CMPCertificate> cmpCertificates = new ArrayList<>();
        X509Certificate[] certsNeedToCarry = configManagement.getCertsNeedToCarry();
        for (X509Certificate certificate : certsNeedToCarry) {
            cmpCertificates.add(new CMPCertificate(Certificate.getInstance(certificate.derEncode())));
        }
        return () -> cmpCertificates.toArray(new CMPCertificate[0]);
    }

    /**
     * 根据条件生成 {@link CertTemplate}
     *
     * @param subject       主题项 {@link SubjectInfo}
     * @param validity      有效期 {@link Validity}
     * @param publicKey     公钥
     * @param extensionList 证书扩展 {@link List<Extension>}
     * @return {@link CertTemplate}
     */
    private CertTemplate generateCertTemplate(SubjectInfo subject, Validity validity, byte[] publicKey, List<Extension> extensionList) throws PkiException {
        CertTemplateBuilder builder = new CertTemplateBuilder();
        if (subject != null) {
            builder.setSubject(X500Name.getInstance(PKCS10Generator.generateX500Name(subject).getASN1Object().encode()));
        }
        Date notBeforeDate = Optional.ofNullable(validity).map(Validity::getStartTime).orElse(null);
        Date notAfterDate = Optional.ofNullable(validity).map(Validity::getEndTime).orElse(null);
        if (notBeforeDate != null && notAfterDate != null) {
            ASN1TaggedObject notBefore = new DERTaggedObject(0, new Time(notBeforeDate));
            ASN1TaggedObject notAfter = new DERTaggedObject(1, new Time(notAfterDate));
            builder.setValidity(OptionalValidity.getInstance(new DERSequence(new ASN1Encodable[]{notBefore, notAfter})));
        }
        SubjectPublicKeyInfo publicKeyInfo = Optional.ofNullable(publicKey)
                .map(SubjectPublicKeyInfo::getInstance)
                .orElse(null);
        builder.setPublicKey(publicKeyInfo);

        if (extensionList != null && !extensionList.isEmpty()) {
            ExtensionsGenerator extensionsGenerator = new ExtensionsGenerator();
            extensionList.forEach(extension -> extensionsGenerator.addExtension(new ASN1ObjectIdentifier(extension.getOid()), extension.isCritical(), extension.getContent()));
            builder.setExtensions(extensionsGenerator.generate());
        }
        return builder.build();
    }

    /**
     * @param issuer       颁发者
     * @param serialNumber 序列号
     * @return {@link Controls}
     * @throws Exception e
     */
    private Controls generateControls(String issuer, String serialNumber) throws Exception {
        assert !StringUtils.isBlank(issuer);

        GeneralName issuerName = new GeneralName(new X500Name(issuer));

        ASN1Integer certSN = null;
        if (serialNumber != null) {
            certSN = new ASN1Integer(CryptoUtils.hexDecode(serialNumber));
        }

        Controls controls = null;
        if (certSN != null) {
            ASN1EncodableVector vector = new ASN1EncodableVector();
            vector.add(issuerName);
            vector.add(certSN);
            DERSequence sequence = new DERSequence(vector);

            ASN1ObjectIdentifier oid = new ASN1ObjectIdentifier("1.3.6.1.5.5.7.5.1.5");
            AttributeTypeAndValue atv = new AttributeTypeAndValue(oid, sequence);

            controls = new Controls(atv);
        }
        return controls;
    }


    /**
     * 生成 POP 选用的是 ProofOfPossession#signature
     * 本来构造 POP（拥有证据），是使用用户证书的公钥和使用用户的私钥进行签名的
     *
     * @param subject 主题项 {@link SubjectInfo}
     * @return {@link ProofOfPossession}
     * @throws IOException e
     */
    private ProofOfPossession generatePOP(SubjectInfo subject, byte[] publicKey, byte[] privateKey) throws IOException, PkiException {
        Objects.requireNonNull(publicKey);
        Objects.requireNonNull(privateKey);
        POPOSigningKeyInput poposkInput;
        GeneralName subjectName = null;
        if (subject != null) {
            net.netca.pki.encoding.asn1.pki.X500Name x500Name = PKCS10Generator.generateX500Name(subject);
            subjectName = new GeneralName(X500Name.getInstance(x500Name.getASN1Object().encode()));
        }
        SubjectPublicKeyInfo subjectPublicKeyInfo = Optional.of(publicKey)
                .map(SubjectPublicKeyInfo::getInstance)
                .orElseThrow(NullPointerException::new);
        poposkInput = new POPOSigningKeyInput(subjectName, subjectPublicKeyInfo);
        byte[] tbs;
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            ASN1OutputStream derOutput = ASN1OutputStream.create(output);
            derOutput.writeObject(poposkInput);
            derOutput.close();
            tbs = output.toByteArray();
        }

        PrivateKeyInfo privateKeyInfo = PrivateKeyInfo.decode(privateKey);
        byte[] signed = CryptoUtils.p1SignWithOid(privateKeyInfo, tbs, serverCommCertSignAlgoEnum.getCode());

        AlgorithmIdentifier alg = new AlgorithmIdentifier(new ASN1ObjectIdentifier(serverCommCertSignAlgoEnum.getCode()));
        DERBitString signature = new DERBitString(signed);
        POPOSigningKey poposk = new POPOSigningKey(poposkInput, alg, signature);

        return new ProofOfPossession(poposk);
    }

    /**
     * 获取签名值
     *
     * @param pkiHeader {@link PKIHeader}
     * @param pkiBody   {@link PKIBody}
     * @return byte[]
     * @throws Exception e
     */
    private byte[] getProtection(PKIHeader pkiHeader, PKIBody pkiBody) throws Exception {
        byte[] res;
        ASN1EncodableVector v = new ASN1EncodableVector();
        v.add(pkiHeader);
        v.add(pkiBody);
        ASN1Encodable protectedPart = new DERSequence(v);
        try (ByteArrayOutputStream bao = new ByteArrayOutputStream()) {
            ASN1OutputStream out = ASN1OutputStream.create(bao);
            out.writeObject(protectedPart);
            res = bao.toByteArray();
        }
        return res;
    }

    private CertReqMessages generateCertReqMessages(CertReqMessagesParams certReqMessagesParams) throws Exception {
        CertTemplate certTemplate = generateCertTemplate(certReqMessagesParams.subject, certReqMessagesParams.validity,
                certReqMessagesParams.publicKey, certReqMessagesParams.extensionList);
        Controls controls = generateControls(certReqMessagesParams.issuer, certReqMessagesParams.certSn);
        CertRequest certRequest = new CertRequest(new ASN1Integer(BigInteger.valueOf(certReqMessagesParams.certRequestId)), certTemplate, controls);
        ProofOfPossession proofOfPossession = null;
        if (certReqMessagesParams.privateKey != null) {
            proofOfPossession = generatePOP(certReqMessagesParams.subject, certReqMessagesParams.publicKey, certReqMessagesParams.privateKey);
        }
        CertReqMsg certReqMsg = new CertReqMsg(certRequest, proofOfPossession, null);
        return new CertReqMessages(certReqMsg);
    }
}
