package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * created by zaj on 2019/1/22
 */
public enum FingerprintRegisterConstant implements IBaseEnum {


    registered(0,"已注册指纹"),
    unregistered(1,"未注册指纹")
    ;

    FingerprintRegisterConstant(Integer code,String description){
        this.code = code;
        this.description =description;
    }

    private Integer code;
    private String description;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
