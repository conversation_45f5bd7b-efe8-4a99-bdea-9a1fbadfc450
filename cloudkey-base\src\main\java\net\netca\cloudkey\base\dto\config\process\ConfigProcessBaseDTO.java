package net.netca.cloudkey.base.dto.config.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: API返回基础DTO
 * @Author: dyr
 * @CreateDate: 2020/8/18 9:18
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessBaseDTO {

    /**
     * 流程类型
     */
    private Integer processType;

    /**
     * 操作顺序
     */
    private Integer[] operationSequence;

}
