package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.util.EnumUtil;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * 证书状态
 *
 * <AUTHOR>
 * @since 2018年10月17日12:51:46
 */
public enum CertStatusEnum implements IBaseEnum {

    CERT_STATUS_NORMAL(0,"正常"),
    CERT_STATUS_CANCELATION(1,"注销"),
    CERT_STATUS_LOSS(2,"挂失"),
    CERT_STATUS_RENEWAL(3,"已续期"),
    CERT_STATUS_MODIFIED(4,"已变更"),
    CERT_STATUS_UPDATE(5,"已更新(密钥)"),
    CERT_STATUS_FREEZE(100,"已冻结"),
    CERT_STATUS_RESERVE(200,"已预约"),
    ;

    private Integer code;
    private String description;

    private CertStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CertStatusEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = linkedHashMap.get("value");
                break;
            }
        }
        return obj.toString();
    }

}
