package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @ProjectName: cloudkey
 * @Package: net.netca.cloudkey.base.constant
 */
public enum CertDownloadMemoEnum implements IBaseEnum {

    CERT_DOWNLOAD_SUCCESS(0, "成功"),

    CERTAPPLY_DOWNLOAD_FAIL(1, "新申请证书：更新失败，"),

    CERTRENEW_DOWNLOAD_FAIL(2, "续期证书：更新失败，"),

    CERTREVOKE_DOWNLOAD_FAIL(3, "注销证书：更新失败，"),
    ;


    private Integer code;
    private String description;

    CertDownloadMemoEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

}
