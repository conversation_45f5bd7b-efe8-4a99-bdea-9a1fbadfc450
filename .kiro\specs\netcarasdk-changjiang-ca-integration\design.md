# 通过 netcarasdk 对接长江 CA 平台设计文档

## 1. 概述

本设计文档描述了如何通过 netcarasdk 实现与长江 CA 平台的对接，扩展现有的证书生命周期管理系统。通过实现新的 `ICertificateLifecycleManager` 具体实现类，支持基于 CMP 协议的证书申请、查询、下载、注销等操作。

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[业务调用层] --> B[CertificateLifecycleManagerFactory]
    B --> C[ICertificateLifecycleManager]
    C --> D[DefaultCertificateLifecycleManager]
    C --> E[CJCACertificateLifecycleManager]

    B --> F[ConfigKeyValueCacheUtil]
    F --> G[配置数据库]

    D --> H[BPMS业务平台]
    E --> I[netcarasdk]
    I --> J[长江CA平台]

    E --> K[CmpMessageConfigManagement]
    E --> L[CmpRespResultAdapter]
    
    style E fill:#e1f5fe
    style I fill:#f3e5f5
    style J fill:#fff3e0
```

### 2.2 模块分层

1. **接口层 (cloudkey-base)**
   - `ICertificateLifecycleManager` - 核心业务接口
   - `CertificateLifecycleManagerFactory` - 工厂类

2. **实现层 (cloudkeyserver)**
   - `DefaultCertificateLifecycleManager` - 默认BPMS实现
   - `CJCACertificateLifecycleManager` - 长江CA实现
   - `AbstractCertificateLifecycleManager` - 抽象基类

3. **适配层**
   - `CmpRespResultAdapter` - CMP响应结果适配器
   - `CJCAConfigManager` - 长江CA配置管理器

4. **配置层**
   - 数据库配置项定义和读取逻辑
   - 基于ConfigKeyValueCacheUtil的配置管理
   - 证书模板配置管理

## 3. 组件和接口

### 3.1 核心实现类

#### CJCACertificateLifecycleManager

```java
@Slf4j
@Component("cjcaCertificateLifecycleManager")
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
public class CJCACertificateLifecycleManager extends AbstractCertificateLifecycleManager {

    @Autowired
    private CJCAConfigManager configManager;
    
    @Autowired
    private CmpRespResultAdapter responseAdapter;
    
    // 实现抽象方法
    @Override
    protected NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest,
                                                  String opSignature,
                                                  BusinessUser businessUser,
                                                  ConfigProject configProject,
                                                  String url) throws Exception;
    
    @Override
    protected NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception;
    
    @Override
    protected NetcaBpmsResponse doDownloadCertificate(String requestId, String systemId) throws Exception;
    
    @Override
    protected NetcaBpmsResponse doRevokeCertificate(BusinessCertAttribute businessCertAttribute,
                                                   ConfigProject configProject,
                                                   AuthorityOperator authorityOperator) throws Exception;
    
    @Override
    protected String doDecryptAdministratorPin(String encryptedPin, String systemId);
    
    // ==================== 证书信息获取和解析扩展点 ====================
    
    /**
     * 从CA平台获取证书信息的扩展点
     * 统一使用NetcaBpmsResponse作为返回类型，提供类型安全性
     */
    @Override
    protected NetcaBpmsResponse getCertificateInfoFromPlatform(NetcaBpmsResponse bpmsResponse) throws Exception;
    
    /**
     * 解析CA平台响应数据的扩展点
     * 统一使用NetcaBpmsResponse作为参数类型，提供类型安全性
     */
    @Override
    protected Map<String, Map<String, Object>> parseCertificateInfo(NetcaBpmsResponse platformResponse) throws Exception;
    
    /**
     * 获取CA平台类型
     */
    @Override
    protected String getPlatformType();
}
```

#### CJCAConfigManager

```java
@Component
@Slf4j
public class CJCAConfigManager {

    @Autowired
    private ConfigKeyValueCacheUtil configKeyValueCacheUtil;

    // 配置项常量 - 所有配置都从数据库读取
    private static final String CJCA_BASE_URL = "CLOUDKEY.CJCA.BASE.URL";
    private static final String CJCA_CA_COMM_CERT_SHA256 = "CLOUDKEY.CJCA.CA.COMM.CERT.SHA256";
    private static final String CJCA_RA_COMM_CERT = "CLOUDKEY.CJCA.RA.COMM.CERT";
    private static final String CJCA_THIRD_PARTY_COMM_CERT = "CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT";
    private static final String CJCA_THIRD_PARTY_COMM_CERT_KEY = "CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY";
    private static final String CJCA_SIGN_ALGO_NAME = "CLOUDKEY.CJCA.SIGN.ALGO.NAME";
    private static final String CJCA_DEFAULT_TEMPLATE_ID = "CLOUDKEY.CJCA.DEFAULT.TEMPLATE.ID";
    private static final String CJCA_CONNECTION_TIMEOUT = "CLOUDKEY.CJCA.CONNECTION.TIMEOUT";
    private static final String CJCA_READ_TIMEOUT = "CLOUDKEY.CJCA.READ.TIMEOUT";
    private static final String CJCA_RETRY_MAX_ATTEMPTS = "CLOUDKEY.CJCA.RETRY.MAX.ATTEMPTS";
    private static final String CJCA_RETRY_DELAY = "CLOUDKEY.CJCA.RETRY.DELAY";

    // 配置获取方法 - 从数据库动态读取
    public String getBaseUrl() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_BASE_URL);
    }

    public String getCaCommCertSha256() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_CA_COMM_CERT_SHA256);
    }

    public X509Certificate getRaCommCert() throws Exception {
        String certBase64 = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_RA_COMM_CERT);
        return new X509Certificate(certBase64);
    }

    public X509Certificate getThirdPartyCommCert() throws Exception {
        String certBase64 = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_THIRD_PARTY_COMM_CERT);
        return new X509Certificate(certBase64);
    }

    public PrivateKeyInfo getThirdPartyCommCertKey() throws Exception {
        String keyBase64 = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_THIRD_PARTY_COMM_CERT_KEY);
        byte[] keyBlob = Base64.getDecoder().decode(keyBase64);
        return PrivateKeyInfo.decode(keyBlob);
    }

    public String getSignAlgoName() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_SIGN_ALGO_NAME);
    }

    public String getDefaultTemplateId() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_DEFAULT_TEMPLATE_ID);
    }

    public int getConnectionTimeout() {
        String timeout = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_CONNECTION_TIMEOUT);
        return CommonUtil.isStringEmpty(timeout) ? 30000 : Integer.parseInt(timeout);
    }

    public int getReadTimeout() {
        String timeout = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_READ_TIMEOUT);
        return CommonUtil.isStringEmpty(timeout) ? 60000 : Integer.parseInt(timeout);
    }

    public int getRetryMaxAttempts() {
        String attempts = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_RETRY_MAX_ATTEMPTS);
        return CommonUtil.isStringEmpty(attempts) ? 3 : Integer.parseInt(attempts);
    }

    public int getRetryDelay() {
        String delay = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_RETRY_DELAY);
        return CommonUtil.isStringEmpty(delay) ? 1000 : Integer.parseInt(delay);
    }

    // 配置验证方法
    public void validateConfiguration() throws CJCAConfigException {
        List<String> missingConfigs = new ArrayList<>();

        if (CommonUtil.isStringEmpty(getBaseUrl())) {
            missingConfigs.add(CJCA_BASE_URL);
        }
        if (CommonUtil.isStringEmpty(getCaCommCertSha256())) {
            missingConfigs.add(CJCA_CA_COMM_CERT_SHA256);
        }
        // ... 验证其他必需配置项

        if (!missingConfigs.isEmpty()) {
            throw new CJCAConfigException("CJCA_CONFIG_MISSING",
                "缺少必需的配置项: " + String.join(", ", missingConfigs));
        }
    }

    // CMP配置管理对象创建
    public CmpMessageConfigManagement createCmpMessageConfigManagement() throws Exception {
        validateConfiguration();

        return CmpMessageConfigManagement.builder()
                .communicationCert(getRaCommCert())
                .thirdPartyServerCommCert(getThirdPartyCommCert())
                .thirdPartyServerCommCertPrivateKeyInfo(getThirdPartyCommCertKey())
                .thirdPartyServerCommCertSignAlgoName(getSignAlgoName())
                .build();
    }
}
```

### 3.2 响应适配器

#### CmpRespResultAdapter

```java
@Component
@Slf4j
public class CmpRespResultAdapter {
    
    /**
     * 将CMP响应结果转换为NetcaBpmsResponse
     */
    public NetcaBpmsResponse adaptCmpRespResult(CmpRespResult cmpRespResult, String operation);
    
    /**
     * 适配证书申请响应
     */
    public NetcaBpmsResponse adaptCertificateApplicationResponse(CmpRespResult cmpRespResult);
    
    /**
     * 适配证书状态查询响应
     */
    public NetcaBpmsResponse adaptCertificateStatusResponse(CmpRespResult cmpRespResult);
    
    /**
     * 适配证书下载响应
     */
    public NetcaBpmsResponse adaptCertificateDownloadResponse(CmpRespResult cmpRespResult);
    
    /**
     * 适配证书注销响应
     */
    public NetcaBpmsResponse adaptCertificateRevocationResponse(CmpRespResult cmpRespResult);
    
    /**
     * 处理CMP错误状态
     */
    private NetcaBpmsResponse handleCmpError(CmpRespResult cmpRespResult, String operation);
}
```

### 3.3 工厂类扩展

#### CertificateLifecycleManagerFactory 扩展

```java
// 在现有工厂类中添加长江CA支持
private static final String CJCA_IMPLEMENTATION = "cjca";
private static final String CJCA_BEAN_NAME = "cjcaCertificateLifecycleManager";

private ICertificateLifecycleManager createCertificateLifecycleManagerImplementation(String type) {
    switch (type) {
        case DEFAULT_IMPLEMENTATION:
        case BPMS_IMPLEMENTATION:
        case "":
            return getImplementationBean(DEFAULT_BEAN_NAME);
        case CJCA_IMPLEMENTATION:
            return getImplementationBean(CJCA_BEAN_NAME);
        default:
            log.warn("未知的证书生命周期管理器实现类型: {}，使用默认实现", type);
            return getDefaultImplementation();
    }
}
```

## 4. 数据模型

### 4.1 配置数据模型

#### ConfigKeyValueEnum 扩展

需要在 `ConfigKeyValueEnum` 中添加长江CA相关的配置项：

```java
// 长江CA平台配置项
CJCA_BASE_URL("CLOUDKEY.CJCA.BASE.URL", "长江CA平台基础URL"),
CJCA_CA_COMM_CERT_SHA256("CLOUDKEY.CJCA.CA.COMM.CERT.SHA256", "CA通讯证书SHA256指纹"),
CJCA_RA_COMM_CERT("CLOUDKEY.CJCA.RA.COMM.CERT", "RA通讯证书Base64编码"),
CJCA_THIRD_PARTY_COMM_CERT("CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT", "第三方通讯证书Base64编码"),
CJCA_THIRD_PARTY_COMM_CERT_KEY("CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY", "第三方通讯证书私钥Base64编码"),
CJCA_SIGN_ALGO_NAME("CLOUDKEY.CJCA.SIGN.ALGO.NAME", "签名算法名称"),
CJCA_DEFAULT_TEMPLATE_ID("CLOUDKEY.CJCA.DEFAULT.TEMPLATE.ID", "默认证书模板ID"),
CJCA_CONNECTION_TIMEOUT("CLOUDKEY.CJCA.CONNECTION.TIMEOUT", "连接超时时间(毫秒)"),
CJCA_READ_TIMEOUT("CLOUDKEY.CJCA.READ.TIMEOUT", "读取超时时间(毫秒)"),
CJCA_RETRY_MAX_ATTEMPTS("CLOUDKEY.CJCA.RETRY.MAX.ATTEMPTS", "最大重试次数"),
CJCA_RETRY_DELAY("CLOUDKEY.CJCA.RETRY.DELAY", "重试间隔(毫秒)");
```

#### 长江CA配置项详细说明

| 配置项 | 描述 | 示例值 | 必填 |
|--------|------|--------|------|
| CLOUDKEY.CJCA.BASE.URL | 长江CA平台基础URL | http://192.168.200.102:8084/raserver/cmp/ | 是 |
| CLOUDKEY.CJCA.CA.COMM.CERT.SHA256 | CA通讯证书SHA256指纹 | 53e8bd21afaebfdfb6de56d9423b8bf398adf420abace78e29714e9a0ab7ee4b | 是 |
| CLOUDKEY.CJCA.RA.COMM.CERT | RA通讯证书(Base64) | MIIBxxx... | 是 |
| CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT | 第三方通讯证书(Base64) | MIIDxxx... | 是 |
| CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY | 第三方通讯证书私钥(Base64) | MIIEvxxx... | 是 |
| CLOUDKEY.CJCA.SIGN.ALGO.NAME | 签名算法名称 | SHA256WithRSA | 是 |
| CLOUDKEY.CJCA.DEFAULT.TEMPLATE.ID | 默认证书模板ID | T202112200001 | 是 |
| CLOUDKEY.CJCA.CONNECTION.TIMEOUT | 连接超时时间(毫秒) | 30000 | 否 |
| CLOUDKEY.CJCA.READ.TIMEOUT | 读取超时时间(毫秒) | 60000 | 否 |
| CLOUDKEY.CJCA.RETRY.MAX.ATTEMPTS | 最大重试次数 | 3 | 否 |
| CLOUDKEY.CJCA.RETRY.DELAY | 重试间隔(毫秒) | 1000 | 否 |

### 4.2 证书模板映射

#### 证书类型与模板ID映射

```java
public enum CJCATemplateEnum {
    PERSONAL_RSA("personal_rsa", "T202112200001", "个人RSA证书模板"),
    PERSONAL_SM2("personal_sm2", "T202112200002", "个人SM2证书模板"),
    EMPLOYEE_RSA("employee_rsa", "T202112200003", "员工RSA证书模板"),
    EMPLOYEE_SM2("employee_sm2", "T202112200004", "员工SM2证书模板"),
    ORGANIZATION_RSA("organization_rsa", "T202112200005", "组织RSA证书模板"),
    ORGANIZATION_SM2("organization_sm2", "T202112200006", "组织SM2证书模板");
    
    private final String type;
    private final String templateId;
    private final String description;
}
```

## 5. 错误处理

### 5.1 异常类定义

#### CJCAException

```java
public class CJCAException extends Exception {
    private final String errorCode;
    private final String errorMessage;
    private final Throwable cause;

    // 构造方法和getter方法
}

public class CJCAConfigException extends CJCAException {
    // 配置相关异常
}

public class CJCAConnectionException extends CJCAException {
    // 连接相关异常
}

public class CJCACmpProtocolException extends CJCAException {
    // CMP协议相关异常
}
```

### 5.2 错误码定义

```java
public enum CJCAErrorCode {
    CONFIG_MISSING("CJCA_001", "长江CA配置缺失"),
    CONFIG_INVALID("CJCA_002", "长江CA配置无效"),
    CONNECTION_FAILED("CJCA_003", "连接长江CA平台失败"),
    CMP_PROTOCOL_ERROR("CJCA_004", "CMP协议错误"),
    CERTIFICATE_NOT_FOUND("CJCA_005", "证书未找到"),
    TEMPLATE_NOT_FOUND("CJCA_006", "证书模板未找到"),
    SIGNATURE_VERIFICATION_FAILED("CJCA_007", "签名验证失败"),
    CERTIFICATE_PARSING_ERROR("CJCA_008", "证书解析错误");
    
    private final String code;
    private final String message;
}
```

## 6. 测试策略

### 6.1 单元测试

- **CJCACertificateLifecycleManagerTest** - 核心实现类测试
- **CJCAConfigManagerTest** - 配置管理器测试
- **CmpRespResultAdapterTest** - 响应适配器测试
- **CertificateLifecycleManagerFactoryTest** - 工厂类扩展测试

### 6.2 集成测试

- **长江CA平台连接测试** - 验证与实际CA平台的连接
- **证书申请流程测试** - 完整的证书申请流程验证
- **配置切换测试** - 验证不同实现之间的切换
- **异常处理测试** - 验证各种异常场景的处理

### 6.3 性能测试

- **并发申请测试** - 验证并发证书申请的性能
- **大批量操作测试** - 验证批量证书操作的性能
- **长时间运行测试** - 验证系统长时间运行的稳定性

## 7. 实现注意事项

### 7.1 向后兼容性

- 保持现有API接口不变
- 确保默认行为与原有系统一致
- 提供平滑的迁移路径

### 7.2 类型安全性改进

为了提高代码的类型安全性和可维护性，扩展点方法统一使用 `NetcaBpmsResponse` 类型：

#### 改进前的问题
- 扩展点方法使用 `Object` 作为返回类型，缺乏编译时类型检查
- 需要在运行时进行类型转换，容易出错
- IDE无法提供有效的代码补全和重构支持

#### 改进后的优势
- **编译时类型安全**：所有类型错误在编译阶段就能发现
- **IDE支持**：提供更好的代码补全、重构和错误检查
- **代码可读性**：明确的方法签名，提高代码可读性
- **维护性**：统一的类型定义，便于后续维护和扩展

#### 扩展点方法签名
```java
// 统一使用NetcaBpmsResponse作为返回类型
protected NetcaBpmsResponse getCertificateInfoFromPlatform(NetcaBpmsResponse bpmsResponse) throws Exception;

// 统一使用NetcaBpmsResponse作为参数类型
protected Map<String, Map<String, Object>> parseCertificateInfo(NetcaBpmsResponse platformResponse) throws Exception;
```

#### 实现要点
- **CJCA实现**：将CMP响应转换为标准的 `NetcaBpmsResponse` 格式
- **默认实现**：保持向后兼容，包装现有结果为 `NetcaBpmsResponse`
- **类型转换**：在具体实现类内部处理类型转换逻辑
- **错误处理**：提供统一的异常处理机制

### 7.3 配置管理

- **数据库配置存储**：所有配置项都存储在数据库中，通过ConfigKeyValueCacheUtil读取
- **配置热更新**：支持运行时配置变更，无需重启应用
- **配置验证机制**：在创建CMP配置对象时验证必需配置项的完整性
- **配置缓存机制**：利用ConfigKeyValueCacheUtil的缓存机制提高配置读取性能
- **配置变更日志**：记录详细的配置变更日志，便于问题追踪

### 7.4 安全考虑

- 证书和私钥的安全存储
- 通信过程的加密保护
- 敏感信息的脱敏处理

### 7.5 监控和日志

- 关键操作的详细日志记录
- 性能指标的监控
- 异常情况的告警机制

## 8. 部署和配置

### 8.1 依赖管理

#### Maven依赖配置

```xml
<!-- 项目已包含netcarasdk依赖 -->
<dependency>
    <groupId>net.netca</groupId>
    <artifactId>netcarasdk</artifactId>
    <version>${netcarasdk.version}</version>
</dependency>
```

#### 条件装配

长江CA实现的装配基于以下条件：

```java
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
public class CJCACertificateLifecycleManager extends AbstractCertificateLifecycleManager {
    // 实现类始终注册，但通过工厂类根据数据库配置动态选择
}
```

**注意**：不使用 `@ConditionalOnProperty` 注解，而是通过 `CertificateLifecycleManagerFactory` 根据数据库中的配置项 `CLOUDKEY.CERTIFICATE.LIFECYCLE.MANAGER.IMPLEMENTATION.TYPE` 动态选择实现。这样可以支持运行时配置切换。

### 8.2 数据库配置管理

#### 配置项数据库存储

所有长江CA相关配置都通过 `ConfigKeyValueCacheUtil` 从数据库中读取，配置项存储在系统配置表中：

```sql
-- 证书生命周期管理器实现类型配置
INSERT INTO config_key_value (config_key, config_value, config_desc) VALUES
('CLOUDKEY.CERTIFICATE.LIFECYCLE.MANAGER.IMPLEMENTATION.TYPE', 'CJCA', '证书生命周期管理器实现类型');

-- 长江CA平台基础配置
INSERT INTO config_key_value (config_key, config_value, config_desc) VALUES
('CLOUDKEY.CJCA.BASE.URL', 'https://ca.cjca.com:8084/raserver/cmp/', '长江CA平台基础URL'),
('CLOUDKEY.CJCA.CA.COMM.CERT.SHA256', '53e8bd21afaebfdfb6de56d9423b8bf398adf420abace78e29714e9a0ab7ee4b', 'CA通讯证书SHA256指纹'),
('CLOUDKEY.CJCA.RA.COMM.CERT', 'MIIBxxx...', 'RA通讯证书Base64编码'),
('CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT', 'MIIDxxx...', '第三方通讯证书Base64编码'),
('CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY', 'MIIEvxxx...', '第三方通讯证书私钥Base64编码'),
('CLOUDKEY.CJCA.SIGN.ALGO.NAME', 'SHA256WithRSA', '签名算法名称'),
('CLOUDKEY.CJCA.DEFAULT.TEMPLATE.ID', 'T202112200001', '默认证书模板ID');

-- 超时和重试配置
INSERT INTO config_key_value (config_key, config_value, config_desc) VALUES
('CLOUDKEY.CJCA.CONNECTION.TIMEOUT', '30000', '连接超时时间(毫秒)'),
('CLOUDKEY.CJCA.READ.TIMEOUT', '60000', '读取超时时间(毫秒)'),
('CLOUDKEY.CJCA.RETRY.MAX.ATTEMPTS', '3', '最大重试次数'),
('CLOUDKEY.CJCA.RETRY.DELAY', '1000', '重试间隔(毫秒)');
```

#### 配置管理界面

系统管理员可以通过后台管理界面动态修改这些配置项：

1. **配置切换**：修改 `CLOUDKEY.CERTIFICATE.LIFECYCLE.MANAGER.IMPLEMENTATION.TYPE` 在不同CA实现间切换
2. **连接配置**：修改URL、证书等连接相关配置
3. **性能调优**：调整超时时间、重试次数等性能参数
4. **模板管理**：配置不同类型证书的模板ID

#### 配置热更新机制

```java
// ConfigKeyValueCacheUtil 支持配置热更新
// 当数据库配置发生变化时，系统会自动检测并更新缓存
public class CJCAConfigManager {

    @Autowired
    private ConfigKeyValueCacheUtil configKeyValueCacheUtil;

    // 配置获取方法会自动从缓存中读取最新配置
    public String getBaseUrl() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_BASE_URL);
    }

    // 配置变更检测
    public boolean isConfigurationChanged() {
        // 通过ConfigKeyValueCacheUtil的缓存机制自动检测配置变更
        return configKeyValueCacheUtil.isConfigChanged(getAllConfigKeys());
    }
}
```

### 8.3 自动配置类

#### CJCAAutoConfiguration

```java
@Configuration
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
@Slf4j
public class CJCAAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public CJCAConfigManager cjcaConfigManager() {
        log.info("注册长江CA配置管理器");
        return new CJCAConfigManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public CmpRespResultAdapter cmpRespResultAdapter() {
        log.info("注册CMP响应结果适配器");
        return new CmpRespResultAdapter();
    }

    @Bean("cjcaCertificateLifecycleManager")
    @ConditionalOnMissingBean(name = "cjcaCertificateLifecycleManager")
    public ICertificateLifecycleManager cjcaCertificateLifecycleManager(
            CJCAConfigManager configManager,
            CmpRespResultAdapter responseAdapter) {
        log.info("注册长江CA证书生命周期管理器实现");
        return new CJCACertificateLifecycleManager(configManager, responseAdapter);
    }
}
```

**说明**：
- 移除了 `@ConditionalOnProperty` 注解，因为配置切换由工厂类根据数据库配置动态处理
- 所有Bean都会注册，但只有当数据库配置为 `CJCA` 时才会被工厂类选择使用
- 这样设计支持运行时配置热更新，无需重启应用

## 9. 实现细节

### 9.1 证书申请流程

#### CMP协议证书申请实现

```java
@Override
protected NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest,
                                              String opSignature,
                                              BusinessUser businessUser,
                                              ConfigProject configProject,
                                              String url) throws Exception {
    try {
        // 1. 构建CMP配置管理对象
        CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

        // 2. 生成证书请求ID
        long requestId = generateCertificateRequestId();

        // 3. 构建用户信息
        UserInfo userInfo = buildUserInfo(businessUser);

        // 4. 构建主题信息
        SubjectInfo subjectInfo = buildSubjectInfo(registerRequest, businessUser);

        // 5. 构建证书有效期
        Validity validity = buildValidity(configProject);

        // 6. 构建自定义扩展信息
        CustomFreeText customFreeText = CustomFreeText.builder()
                .validity(validity)
                .userInfo(userInfo)
                .subjectInfo(subjectInfo)
                .certReqId(requestId)
                .build();

        // 7. 构建P10证书申请消息
        P10CertReqMessage p10CertReqMessage = P10CertReqMessage.defaultMessage().toBuilder()
                .senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName())
                .p10Base64(registerRequest.getP10())
                .customFreeText(customFreeText)
                .certRequestId(requestId)
                .build();

        // 8. 创建编码器和上下文
        MessageEncoder<CmpMessage> encoder = P10CertReqMessageCodec.createEncoder(cmpConfig);
        SingleCertReqContext context = new SingleCertReqContext(p10CertReqMessage);

        // 9. 编码消息
        encoder.encode(context, p10CertReqMessage);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        byte[] reqData = reqPkiMessages.getEncoded();

        // 10. 发送HTTP请求
        String targetUrl = buildRequestUrl(configProject);
        CmpRespResult cmpRespResult = sendCmpRequest(targetUrl, reqData, cmpConfig);

        // 11. 适配响应结果
        return responseAdapter.adaptCertificateApplicationResponse(cmpRespResult);

    } catch (Exception e) {
        log.error("长江CA证书申请失败", e);
        throw new CJCAException("CJCA_APPLY_FAILED", "证书申请失败: " + e.getMessage(), e);
    }
}
```

### 9.2 证书状态查询实现

```java
@Override
protected NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception {
    try {
        // 1. 构建CMP配置管理对象
        CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

        // 2. 构建轮询请求消息
        PollReqMessage pollReqMessage = PollReqMessage.defaultMessage().toBuilder()
                .senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName())
                .certRequestId(Long.parseLong(requestId))
                .build();

        // 3. 创建编码器和上下文
        MessageEncoder<CmpMessage> encoder = PollReqMessageCodec.createEncoder(cmpConfig);
        SingleCertReqContext context = new SingleCertReqContext(pollReqMessage);

        // 4. 编码和发送请求
        encoder.encode(context, pollReqMessage);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        byte[] reqData = reqPkiMessages.getEncoded();

        String targetUrl = configManager.getBaseUrl() + configManager.getCaCommCertSha256();
        CmpRespResult cmpRespResult = sendCmpRequest(targetUrl, reqData, cmpConfig);

        // 5. 适配响应结果
        return responseAdapter.adaptCertificateStatusResponse(cmpRespResult);

    } catch (Exception e) {
        log.error("长江CA证书状态查询失败", e);
        throw new CJCAException("CJCA_QUERY_FAILED", "证书状态查询失败: " + e.getMessage(), e);
    }
}
```

### 9.3 HTTP请求发送

```java
private CmpRespResult sendCmpRequest(String url, byte[] reqData, CmpMessageConfigManagement cmpConfig)
        throws Exception {

    // 1. 构建HTTP请求
    Request request = new Request.Builder()
            .url(url)
            .post(RequestBody.create(MediaType.get("application/pkixcmp"), reqData))
            .build();

    // 2. 配置HTTP客户端
    OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(configManager.getConnectionTimeout(), TimeUnit.MILLISECONDS)
            .readTimeout(configManager.getReadTimeout(), TimeUnit.MILLISECONDS)
            .build();

    // 3. 发送请求并处理响应
    try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
            throw new CJCAConnectionException("CJCA_HTTP_ERROR",
                    "HTTP请求失败: " + response.code() + " " + response.message());
        }

        if (response.body() == null) {
            throw new CJCAConnectionException("CJCA_EMPTY_RESPONSE", "响应体为空");
        }

        byte[] resData = response.body().bytes();

        // 4. 解析CMP响应
        return parseCmpResponse(resData, cmpConfig);
    }
}

private CmpRespResult parseCmpResponse(byte[] resData, CmpMessageConfigManagement cmpConfig)
        throws Exception {

    // 1. 解析PKI消息
    PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(resData));

    // 2. 创建解码器
    MessageDecoder<PKIMessage> decoder = P10CertReqMessageCodec.createDecoder(cmpConfig);

    // 3. 创建上下文并解码
    SingleCertReqContext context = new SingleCertReqContext();
    decoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);

    // 4. 获取解码结果
    return context.getCmpRespResult();
}
```
