package net.netca.sdk.constants;

public enum IdentityTypeEnum implements IBaseEnum {

    IDCARD(1, "身份证"),
    MILITARYID(2, "军官证"),
    PASSPORT(3, "护照"),
    WORDCARD(4, "工作证"),
    HOMEVISITPERMIT(5, "回乡证"),
    DOMICILE(6, "户口本"),
    OTHERPERSONIDENTITYTYPE(7, "其他个人证件"),
    HKIDCARD(8, "香港身份证");

    private final Integer code;
    private final String description;

    private IdentityTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static boolean isValid(Integer code) {
        for (IdentityTypeEnum constant : values()) {
            if (constant.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

}
