package net.netca.sdk.constants;


public enum HashAlgorithmEnum implements IBaseEnum {
    SHA1(0, "SHA1"),
    SHA256(1, "SHA256"),
    SHA512(2, "SHA512"),
    SM3(3, "SM3");


    private HashAlgorithmEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static HashAlgorithmEnum getByCode(Integer code) {
        for (HashAlgorithmEnum examType : HashAlgorithmEnum.values()) {
            if (code.equals(examType.getCode())) {
                return examType;
            }
        }
        throw new RuntimeException(String.format("不支持code=%d的类型", code));
    }
}
