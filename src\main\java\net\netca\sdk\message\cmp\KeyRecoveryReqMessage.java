package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.CustomFreeText;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * 密钥恢复所需的信息
 * @author: zys
 * @date: 2020/3/13 18:30
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Getter
public class KeyRecoveryReqMessage extends CmpMessage {

    /**
     * 用户公钥
     */
    private byte[] publicKey;

    /**
     * 用户私钥
     */
    private byte[] privateKey;

    /**
     * 证书序列号，用于证书续期|密钥更新|密钥更新
     * 详见：RFC 4211 #6.5 OldCert ID Control(https://tools.ietf.org/html/rfc4211#section-6.5)
     *
     * @see org.bouncycastle.asn1.crmf.Controls
     */
    private String serialNumber;

    /**
     * 颁发者
     */
    private String issuer;

    /**
     * 自定义的扩展信息
     */
    private CustomFreeText customFreeText;


    public static KeyRecoveryReqMessage defaultMessage(){
        return KeyRecoveryReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.RECOVERY)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
