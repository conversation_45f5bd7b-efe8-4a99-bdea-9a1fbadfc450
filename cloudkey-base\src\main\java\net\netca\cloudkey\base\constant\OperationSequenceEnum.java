package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @Description: 定义操作类型
 * @Author: dyr
 * @CreateDate: 2020/8/12 11:19
 */
@Getter
@AllArgsConstructor
public enum OperationSequenceEnum implements IBaseEnum {
    //绑定设备与绑定指纹、人脸、设备信息同等
    BINDING_EQUIPMENT(1, "绑定设备"),
    //绑定签章与上传签章图片同等
    BINDING_SIGN_PICTURE(2, "绑定签章"),
    ;

    private final Integer code;
    private final String description;

}
