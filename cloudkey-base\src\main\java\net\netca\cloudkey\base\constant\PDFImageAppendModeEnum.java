package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import org.apache.pdfbox.pdmodel.PDPageContentStream;

import java.util.Arrays;
import java.util.Optional;

/**
 * @descriptions:
 * @date: 2020/2/19
 * @author: SaromChars
 */
public enum PDFImageAppendModeEnum {

    APPEND(0, "叠加上层", PDPageContentStream.AppendMode.APPEND),
    PREPEND(1, "插入底层", PDPageContentStream.AppendMode.PREPEND),
    OVERWRITE(2, "覆盖", PDPageContentStream.AppendMode.OVERWRITE);

    private Integer code;
    private String description;
    private PDPageContentStream.AppendMode appendMode;

    PDFImageAppendModeEnum(Integer code, String description, PDPageContentStream.AppendMode appendMode) {
        this.code = code;
        this.description = description;
        this.appendMode = appendMode;
    }

    public static PDFImageAppendModeEnum getPDFImageAppendModeEnum(Integer modeCode) {
        Optional<PDFImageAppendModeEnum> optionalPDFImageAppendModeEnum = Arrays.asList(PDFImageAppendModeEnum.values())
                .stream()
                .filter(pdfImageAppendModeEnum -> pdfImageAppendModeEnum.code.equals(modeCode))
                .findFirst();

        if (!optionalPDFImageAppendModeEnum.isPresent()) {
            throw new CloudKeyRuntimeException("无此图片叠加模式");
        }

        return optionalPDFImageAppendModeEnum.get();
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public PDPageContentStream.AppendMode getAppendMode() {
        return appendMode;
    }
}
