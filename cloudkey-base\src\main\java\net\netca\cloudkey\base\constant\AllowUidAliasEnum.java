package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 启用新旧uid共存功能常量类
 * <AUTHOR>
 */
public enum AllowUidAliasEnum implements IBaseEnum {

    //WARN!!!!
    // 使用小写描述图片类型
    DISABLE  ("false", "不启用"),
    ENABLE("true", "启用"),
    ;

    private final String code;
    private final String description;

    AllowUidAliasEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
