package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;


public enum ReturnUnlockPinType implements IBaseEnum {

    SMS(1, "通过短信下发"),
    INT_RESP(2, "通过接口响应返回"),
    SMS_INT_RESP(3, "短信和接口返回均有");

    ReturnUnlockPinType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }


}
