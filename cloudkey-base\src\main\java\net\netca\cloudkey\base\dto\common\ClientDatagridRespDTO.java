package net.netca.cloudkey.base.dto.common;

import lombok.Data;

import java.util.List;

/**
 * created by zaj on 2019/4/24
 */
@Data
public class ClientDatagridRespDTO {

    private long total;

    private int currentRowsSize;

    /**
     * V2.17.2
     * 3.1 查询已被授权信息接口新增返回证书用户的 projectUid uid name
     */
    private String projectUid;
    private String userUid;
    private String userName;

    private List<?> rows;

    public ClientDatagridRespDTO(long total, List<?> rows) {
        this.total = total;
        this.rows = rows;
        this.currentRowsSize = (rows == null) ? 0 : rows.size();
    }
}
