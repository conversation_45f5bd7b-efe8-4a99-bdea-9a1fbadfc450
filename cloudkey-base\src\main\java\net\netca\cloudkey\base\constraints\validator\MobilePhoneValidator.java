package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.MobilePhone;
import net.netca.cloudkey.base.constraints.check.CheckType;
import net.netca.cloudkey.base.util.CustomStringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class MobilePhoneValidator implements ConstraintValidator<MobilePhone, String> {

    @Override
    public void initialize(MobilePhone constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (!CustomStringUtils.isBlank(value)) {
            isValid = CheckType.MOBILE.check(value);
        }
        return isValid;
    }
}
