package net.netca.cloudkey.base.dto.businessuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * @Description:
 * @Author: dyr
 * @CreateDate: 2020/6/24 14:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Secret
public class BusinessUserDTO {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 机构名称
     */
    private String organizationName;

    /**
     * 用户
     */
    private Integer id;

    /**
     * 用户唯一标识
     */
    private String uid;
    /**
     * 用户唯一标识别名
     */
    private String uidAlias;

    /**
     * 用户名
     */
    private String name;

    /**
     * 手机号码
     */
    @SecurityDataField
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    @SecurityDataField
    private String officialResidence;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 性别
     */
    private Integer gender;

    private String genderDescription;

    /**
     * 电子邮件
     */
    @SecurityDataField
    private String email;

    /**
     * 证件类型
     */
    private Integer identityType;

    private String identityTypeDescription;

    /**
     * 证件号码
     */
    @SecurityDataField
    private String identity;

    /**
     * 部门
     */
    private String department;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 申请状态
     */
    private Integer makeCertStatus;

    private String makeCertStatusDescription;

    private Integer certUserType;

    private Integer makeCertCount;

    /**
     * 院区ID
     */
    private Integer districtId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 有无签章
     */
    private String picTypeDescription;

    /**
     * 证书有无绑定
     */
    private String certBindingStatusDesc;
}
