package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.constant.base.IEnumFilter;

public enum NoticeRequestTypeEnum implements IBaseEnum, IEnumFilter {

    /**
     * 0表示新申请密钥产生，包含证书初始密码
     */
    REGISTER_KEYPAIR_GENERATE(0, "新申请密钥产生"),

    /**
     * 1表示新申请证书签发
     */
    REGISTER_CERT_GENERATE(1, "新申请证书签发"),

    /**
     * 2表示证书解锁，包含证书解锁后的密码
     */
    CERT_UNLOCK(2, "证书解锁"),

    /**
     * 3表示授权录入，待证书用户确认
     */
    AUTHORIZED_APPLY(3, "授权录入"),

    /**
     * 4表示授权完成，对证书用户的通知
     */
    AUTHORIZED_FINISH_CERTUSER(4, "授权完成（证书用户）"),

    /**
     * 5表示授权完成，对被授权用户的通知，包含被授权用户的初始使用密码
     */
    AUTHORIZED_FINISH_AUTHUSER(5, "授权完成（被授权用户）"),

    /**
     * 6表示产生验证码
     */
    VALIDCODE_GENERATE(6, "产生验证码"),

    /**
     * 7表示授权修改，对被授权用户的通知，包含被授权用户使用次数和有效期
     */
    AUTHORIZED_MODIFY_AUTHUSER(7, "授权修改"),

    /**
     * 证书续期
     */
    CERT_RENEWAL(8, "证书续期", true),

    /**
     * 证书注销
     */
    CERT_REVOKE(9, "证书注销", true),

    /**
     * 证书过期预警
     */
    CERT_EXPIRY_WARNING(10, "证书过期预警"),

    /**
     * APP 设备绑定
     */
    BINDING_APP_DEVICE(11, "客户端设备绑定"),

    /**
     * 协同签名任务签署通知
     */
    COORD_SIGN_TASK_TOOD(12, "协同签名任务签署通知"),

    /**
     * 协同签名任务用户拒绝签署
     */
    COORD_SIGN_TASK_REJECT(13, "协同签名任务用户拒绝签署"),

    /**
     * 协同签名任务完成
     */
    COORD_SIGN_TASK_COMPLETE(14, "协同签名任务完成"),
    ;

    private String description;
    private int code;
    private boolean filter;

    NoticeRequestTypeEnum(int code, String description) {
        this.description = description;
        this.code = code;
    }

    NoticeRequestTypeEnum(int code, String description, boolean filter) {
        this.description = description;
        this.code = code;
        this.filter = filter;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public boolean filter() {
        return filter;
    }
}
