package net.netca.cloudkey.base.dto.authuser;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @descriptions:
 * @date: 2019/4/23
 * @author: cxy
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorizedUserCondition {

    //用户名称
    private String userName;

    //授权用户名称
    private String authorizeUserName;

    //用户uid
    private String userUid;

    //授权用户uid
    private String authorizeUserUid;

    //证书序列号
    private String certSN;

    //授权状态
    private Integer authorizeStatus;

    private List<String> projIds;

    private Boolean hasBindDevice;

    private Boolean hasBindSeparatingKey;

}
