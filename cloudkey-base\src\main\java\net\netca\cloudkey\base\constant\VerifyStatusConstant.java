package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * created by zaj on 2019/1/23
 */
public enum VerifyStatusConstant implements IBaseEnum {


    success(0,"操作验证-成功"),
    unverify(1,"操作验证-未开始"),
    fail(-1,"操作验证-失败")
    ;

    VerifyStatusConstant(Integer code, String description){
        this.code = code;
        this.description =description;
    }

    private Integer code;
    private String description;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
