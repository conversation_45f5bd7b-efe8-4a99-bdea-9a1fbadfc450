package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 任务环节推送接口
 */
@Getter
@AllArgsConstructor
public enum CoSignSegmentPushResultEnum implements IBaseEnum {

    /**
     * 未签名
     */
    NOT_PUSH(0, "未推送"),
    /**
     * 进行中
     */
    PUSHED(1, "已推送"),
    /**
     * 已拒绝
     */
    PUSH_FAIL(2, "推送失败"),
;

    private final Integer code;
    private final String description;



}
