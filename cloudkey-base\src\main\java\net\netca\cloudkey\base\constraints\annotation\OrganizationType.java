package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.OrganizationTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对机构类型的校验注解
 * @see OrganizationTypeValidator
 */
@Documented
@Constraint(validatedBy = OrganizationTypeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface OrganizationType {

    String message() default "机构类型值非法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
