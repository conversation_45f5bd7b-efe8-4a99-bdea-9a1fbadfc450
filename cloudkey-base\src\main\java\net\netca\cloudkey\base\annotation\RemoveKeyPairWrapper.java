package net.netca.cloudkey.base.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @descriptions:
 * 声明方法执行完应
 * 释放keyPairWrapper
 * @date: 2019/8/28
 * @author: SaromChars
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RemoveKeyPairWrapper {
}
