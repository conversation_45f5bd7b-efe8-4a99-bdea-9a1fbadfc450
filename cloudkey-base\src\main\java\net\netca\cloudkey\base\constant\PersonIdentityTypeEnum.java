package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.EnumUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;

public enum PersonIdentityTypeEnum implements IBaseEnum {

    IDCARD(1, "身份证"),
    MILITARYID(2, "军官证"),
    PASSPORT(3, "护照"),
    WORDCARD(4, "工作证"),
    HOMEVISITPERMIT(5, "回乡证"),
    DOMICILE(6, "户口本"),
    OTHERPERSONIDENTITYTYPE(7, "其他证件"),
    HKIDCARD(8, "香港身份证");

    private Integer code;
    private String description;

    private PersonIdentityTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static String getDescription(int code) {
        String obj = null;
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(PersonIdentityTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = (String) linkedHashMap.get("value");
                break;
            }
        }
        return obj;
    }

}
