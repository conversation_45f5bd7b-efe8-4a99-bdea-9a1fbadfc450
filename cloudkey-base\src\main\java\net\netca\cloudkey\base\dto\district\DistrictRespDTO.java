package net.netca.cloudkey.base.dto.district;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/28 19:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DistrictRespDTO {

    private Integer id;

    private String code;

    private String name;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
