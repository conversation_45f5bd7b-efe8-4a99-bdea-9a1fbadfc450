package net.netca.cloudkey.base.dto.operator;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AuthorityOperatorVO {

    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 证件类型
     */
    private Integer identityType;
    /**
     * 证件号码
     */
    private String identity;
    private String name;
    private String email;
    private String phone;
    private String address;
    private String memo;
    /**
     * 管理员类型
     */
    private Integer type;

    private List<String> projectIds;

    private List<Integer> districtIds;

    private String certSn;
    private String certThumbprint;
    private Integer certThumbprintAglo;
    /**
     * 签名证书
     */
    private String signCert;
    private Date validityStart;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityEnd;

    private Integer status;
    @TableField("gmt_create")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @TableField("gmt_modified")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private Integer right; // 审核权力

}
