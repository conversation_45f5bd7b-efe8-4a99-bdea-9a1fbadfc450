package net.netca.sdk.codec;


import net.netca.sdk.entity.RequestContext;

/**
 * @author: zys
 * @date: 2020/3/2 18:51
 */
public interface MessageEncoder<T> {
    /**
     * 编码
     *
     * @param context 上下文
     * @param message 需要编码的消息
     * @throws Exception
     */
    void encode(RequestContext context, T message) throws Exception;

//    /**
//     * 校验 message 是否符合 encoder的要求
//     *
//     * @param message
//     * @return
//     */
//    boolean isValidMessage(T message);
}
