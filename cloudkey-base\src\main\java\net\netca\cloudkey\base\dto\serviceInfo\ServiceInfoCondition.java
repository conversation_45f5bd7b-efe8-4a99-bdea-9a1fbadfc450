package net.netca.cloudkey.base.dto.serviceInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description  
 * @Date 11:30 2020/7/30
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceInfoCondition {

    //服务名称
    private String name;

    //服务地址
    private String content;

    //优先展示
    private Integer priorFlag;

}
