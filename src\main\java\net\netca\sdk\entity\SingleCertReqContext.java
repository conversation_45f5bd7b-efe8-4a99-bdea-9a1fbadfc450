package net.netca.sdk.entity;

import net.netca.sdk.message.cmp.CmpMessage;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

/**
 * 证书请求上下文
 * @author: zys
 *
 */
public class SingleCertReqContext implements RequestContext {

    /**
     * 业务的消息体
     */
    private CmpMessage cmpReqMessage;

    /**
     * 消息体编码的结果
     */
    private PKIMessage encodeResult;


    /**
     * CMP响应解析的结果
     */
    private CmpRespResult cmpRespResult;

    private boolean decoded;


    public SingleCertReqContext(CmpMessage cmpReqMessage) {
        this.cmpReqMessage = cmpReqMessage;
        this.cmpRespResult = new CmpRespResult();
        decoded = false;
    }

    public PKIMessages getPKIMessages() {
        if (encodeResult != null) {
            return new PKIMessages(encodeResult);
        }
        return null;
    }

    /**
     *
     * @return
     */
    public boolean isEncoded() {
        return encodeResult != null;
    }


    /**
     *
     * @return
     */
    public boolean isDecoded() {
        return decoded;
    }


    public CmpRespResult getCmpRespResult() {
        return cmpRespResult;
    }

    public void setEncodeResult(PKIMessage encodeResult) {
        this.encodeResult = encodeResult;
    }

    public PKIMessage getEncodeResult() {
        return encodeResult;
    }

    public void setCmpRespResult(CmpRespResult cmpRespResult) {
        this.cmpRespResult = cmpRespResult;
    }

    public CmpMessage getCmpReqMessage() {
        return cmpReqMessage;
    }

    public void setDecoded(boolean decoded) {
        this.decoded = decoded;
    }

}
