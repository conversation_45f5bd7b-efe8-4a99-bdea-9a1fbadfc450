package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum OperationTypeConstant implements IBaseEnum {
	Login(0, "系统用户登录"),
	SystemOperation(1, "系统用户登录系统后的操作的统称"),
	Logout(2, "系统用户退出登录"),
	audit(3,"审计操作");

	private final Integer code;
	private final String description;
	
	private OperationTypeConstant(Integer code, String description) {
		this.code = code;
		this.description = description;
	}
	
	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

}
