package net.netca.test.asn1;


import net.netca.pki.encoding.asn1.*;
import net.netca.test.asn1.pojo.TNEntry;
import net.netca.test.asn1.pojo.TelephoneNumberRange;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-23 11:56
 */
public class TnAuthorizationList {
    public static final String TN_AUTHORIZATION_LIST_OID = "1.3.6.1.5.5.7.1.26";

    public static SequenceType TelephoneNumberRangeType;
    public static TaggedType TelephoneNumberRangeTaggedType;
    public static TaggedType ServiceProviderCodeTaggedType;
    public static TaggedType TelephoneNumberTaggedType;
    public static ChoiceType TNEntryType;
    public static SequenceOfType TNAuthorizationListType;

    public final static boolean NEED_VALIDATE_TN_AUTH_LIST;

    private List<TNEntry> TnAuthList;

    public TnAuthorizationList(List<TNEntry> tnAuthList) {
        TnAuthList = tnAuthList;
    }

    static {
        // 默认开启校验，显式声明为“false”才取消校验
        String tnAuthListValidate = System.getProperty("net.netca.esimca.tnauthlist.validate");
        NEED_VALIDATE_TN_AUTH_LIST = !"false".equalsIgnoreCase(tnAuthListValidate);

        try {
            preCompile();
        } catch (ASN1Exception e) {
            e.printStackTrace();
        }
    }


    private static void preCompile() throws ASN1Exception {
        IA5StringType TelephoneNumberType = IA5StringType.getInstance();
        IA5StringType ServiceProviderCodeType = IA5StringType.getInstance();

        TelephoneNumberRangeType = new SequenceType();
        TelephoneNumberRangeType.add(TelephoneNumberRange.NAME_START, TelephoneNumberType);
        TelephoneNumberRangeType.add(TelephoneNumberRange.NAME_COUNT, IntegerType.getInstance());

        int tagClass = ASN1Object.Context;
        ServiceProviderCodeTaggedType = new TaggedType(tagClass, 0, false, ServiceProviderCodeType);
        TelephoneNumberRangeTaggedType = new TaggedType(tagClass, 1, false, TelephoneNumberRangeType);
        TelephoneNumberTaggedType = new TaggedType(tagClass, 2, false, TelephoneNumberType);

        TNEntryType = new ChoiceType();
        TNEntryType.add(TNEntry.NAME_SPC, ServiceProviderCodeTaggedType);
        TNEntryType.add(TNEntry.NAME_RANGE, TelephoneNumberRangeTaggedType);
        TNEntryType.add(TNEntry.NAME_ONE, TelephoneNumberTaggedType);

        TNAuthorizationListType = new SequenceOfType(TNEntryType, 1);
    }

}
