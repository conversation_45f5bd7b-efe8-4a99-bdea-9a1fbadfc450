package net.netca.cloudkey.base.dto.sms;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

@Data
@NoArgsConstructor
@Secret
public class SmsLogSearchDTO {

    @SecurityDataField
    private String phone;

    private String smsPlatform;

    private Integer noticeRequestType;

    private Integer isRequestSuccess;

    private String responseCode;

    private String uid;

    private String authorizedUserUid;

    private String projectId;

    private String username;

    private Date requestTimeStart;

    private Date requestTimeEnd;

}
