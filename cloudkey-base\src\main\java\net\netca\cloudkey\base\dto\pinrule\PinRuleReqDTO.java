package net.netca.cloudkey.base.dto.pinrule;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

@Setter
@Getter
@ToString
public class PinRuleReqDTO implements ISignature {

    private Integer id;

    @NotBlank(message = "口令规则名称不能为空")
    @Size(max = 128, message = "口令规则名称长度不能大于128个字符")
    private String name;

    @NotBlank(message = "口令校验正则表达式不能为空")
    @Size(max = 512, message = "口令校验正则表达式不能大于512个字符")
    private String regex;

    @NotBlank(message = "口令规则要求提示语不能为空")
    @Size(max = 512, message = "口令规则要求提示语不能大于512个字符")
    private String message;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private transient OperatorSignature operatorSignature;

}
