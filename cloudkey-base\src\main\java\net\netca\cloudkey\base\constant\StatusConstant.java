package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.constant.base.IEnumFilter;


public enum StatusConstant implements  IBaseEnum , IEnumFilter {

    NORMAL(0, "正常") {
        @Override
        public boolean filter() {
            return false;
        }
    }, FREEZE(1, "冻结") {
        @Override
        public boolean filter() {
            return false;
        }
    }, DELETE(2, "删除") {
        @Override
        public boolean filter() {
            return false;
        }
    }, INVALID(3, "失效") {
        @Override
        public boolean filter() {
            return false;
        }
    };

    public static boolean isNormal(Integer con) {
        return NORMAL.getCode().equals(con);
    }

    private StatusConstant(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
