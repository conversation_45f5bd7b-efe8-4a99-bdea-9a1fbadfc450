package net.netca.sdk.codec.cmp.lambda;

import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.message.cmp.CmpMessage;
import org.bouncycastle.asn1.cmp.PKIMessage;

import java.util.function.BiConsumer;
import java.util.function.BiPredicate;

/**
 * <AUTHOR>
 * @date 2020-04-07 16:10
 */
public class CmpMessageCodec implements MessageEncoder<CmpMessage>, MessageDecoder<PKIMessage> {
    private CmpMessageEncodeHandler encodeHandler;

    private CmpMessageDecodeHandler decodeHandler;



    public CmpMessageCodec(CmpMessageEncodeHandler encodeHandler, CmpMessageDecodeHandler decodeHandler) {
        this.encodeHandler = encodeHandler;
        this.decodeHandler = decodeHandler;
    }

    public static CmpMessageCodec createEncoder(CmpMessageEncodeHandler encodeHandler){
        return new CmpMessageCodec(encodeHandler, null);
    }

    public static CmpMessageCodec createDecoder(CmpMessageDecodeHandler decodeHandler){
        return new CmpMessageCodec(null, decodeHandler);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws Exception {
        if (encodeHandler == null){
            throw new NullPointerException("CmpMessageCodec is not a encoder");
        }
        if (!(context instanceof CmpRequestContext)){
            throw new IllegalArgumentException("context is not instanceof CmpProtocolContext");
        }
        encodeHandler.accept((CmpRequestContext) context, message);
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws Exception {
        if (decodeHandler == null){
            throw new NullPointerException("CmpMessageCodec is not a decoder");
        }
        decodeHandler.accept((CmpRequestContext) context, message);
    }


    public static final class CmpMessageEncodeHandler implements BiConsumer<CmpRequestContext, CmpMessage> {
        private BiPredicate<CmpRequestContext, CmpMessage> predicate;

        private BiConsumer<CmpRequestContext, CmpMessage> encoder;

        public CmpMessageEncodeHandler(BiPredicate<CmpRequestContext, CmpMessage> predicate, BiConsumer<CmpRequestContext, CmpMessage> encoder) {
            this.predicate = predicate;
            this.encoder = encoder;
        }

        @Override
        public void accept(CmpRequestContext context, CmpMessage message) {
            context.getCmpMessageConfigManagement().verifyEncodeValid();
            if (predicate.test(context, message)) {
                encoder.accept(context, message);
            }
        }
    }

    public static final class CmpMessageDecodeHandler implements BiConsumer<CmpRequestContext, PKIMessage> {
        private BiPredicate<CmpRequestContext, PKIMessage> predicate;

        private BiConsumer<CmpRequestContext, PKIMessage> decoder;

        public CmpMessageDecodeHandler(BiPredicate<CmpRequestContext, PKIMessage> predicate, BiConsumer<CmpRequestContext, PKIMessage> decoder) {
            this.predicate = predicate;
            this.decoder = decoder;
        }

        @Override
        public void accept(CmpRequestContext context, PKIMessage message) {
            context.getCmpMessageConfigManagement().verifyDecodeValid();
            if (predicate.test(context, message)) {
                decoder.accept(context, message);
            }
        }
    }

}
