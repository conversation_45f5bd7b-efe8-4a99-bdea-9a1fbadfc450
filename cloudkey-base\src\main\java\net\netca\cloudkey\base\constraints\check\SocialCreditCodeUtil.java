package net.netca.cloudkey.base.constraints.check;

import net.netca.cloudkey.base.javaconfig.RegExpConfig;

import java.util.regex.Pattern;

/***
 * http://blog.sina.com.cn/s/blog_540316260102x352.html
 * 统一社会信任号检查类
 */

public class SocialCreditCodeUtil {
	private static Pattern regex;

	public static boolean check(String code) {
		if(null == regex){
			regex = Pattern.compile(RegExpConfig.socialCredit);
		}
		if ((code.length() != 18) || !(regex.matcher(code).matches())) { 
			return false; 
		} else {
			String ancode;// 统一社会信用代码的每一个值
			int ancodevalue;// 统一社会信用代码每一个值的权重
			int total = 0; 
			int[] weightedfactors = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};// 加权因子
			String str = "0123456789ABCDEFGHJKLMNPQRTUWXY";
			// 不用I、O、S、V、Z
			for (int i = 0; i < code.length() - 1; i++){
				ancode = code.substring(i, i + 1); 
				ancodevalue = str.indexOf(ancode); 
				total = total + ancodevalue * weightedfactors[i];
				// 权重与加权因子相乘之和
			}
			int logiccheckcode = 31 - total % 31;
			if (logiccheckcode == 31){
				logiccheckcode = 0;
			}
			String Str = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,T,U,W,X,Y";
			String[] Array_Str = Str.split(",");
			String checkcode = code.substring(17, 18);
			if (!Array_Str[logiccheckcode].equals(checkcode)) { 
				return false; 
			}
			return true;
		}
		
	}
}
