package net.netca.test.cert;


import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.Validity;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.KeyUpdateReqMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.UpdateKeyReqMessage;
import net.netca.sdk.util.CryptoUtils;
import net.netca.test.util.DateUtil;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

/**
 * @author: zys
 * @date: 2020/3/5 19:01
 */
public class TestUpdateKey extends TestCmpProtocol {


    private X509Certificate oldSignCert;


    /**
     * 仅更新密钥对
     */
    @Test
    public void testOnlyUpdateKeyPair() throws Exception {
        KeyUpdateReqMessage onlyUpdateKeyPairMessage = getOnlyUpdateKeyPairMessage();
        MultipleCmpReqContext context = commonProcess(onlyUpdateKeyPairMessage);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            Assert.assertNotNull(newSignCert);
            Assert.assertFalse(Arrays.equals(oldSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue()));
        }
    }


    /**
     * 仅更新主题项和密钥对
     *
     * @throws Exception
     */
    @Test
    public void testOnlyUpdateSubjectAndKeyPair() throws Exception {
        UpdateKeyReqMessage onlyUpdateSubjectMessage = getOnlyUpdateSubjectAndKeyPairMessage();
        MultipleCmpReqContext context = commonProcess(onlyUpdateSubjectMessage);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            System.out.println("old subject：" + oldSignCert.getSubject().getLdapName());
            System.out.println("new subject：" + newSignCert.getSubject().getLdapName());
            Assert.assertNotEquals(oldSignCert.getSubject().getLdapName(), newSignCert.getSubject().getLdapName());
        }
    }

    @Test
    public void testUpdateKey() throws Exception {
        UpdateKeyReqMessage message = getMessage();
        MultipleCmpReqContext context = commonProcess(message);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            Assert.assertFalse(Arrays.equals(oldSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue()));
            Assert.assertEquals(message.getSubject(), newSignCert.getSubject().getLdapName());
            Assert.assertTrue(compare(message.getValidity().getEndTime(), newSignCert.getNotAfter()));
        }
    }

    /**
     * 仅更新密钥对
     */
    @Test
    public void testOnlyUpdateKeyPairWithNestedMessage() throws Exception {
        KeyUpdateReqMessage onlyUpdateKeyPairMessage = getOnlyUpdateKeyPairMessage();
        NestedMessage nestedMessage = getNestedMessage(onlyUpdateKeyPairMessage, BusinessTypeEnum.UPDATE_KEY);
        MultipleCmpReqContext context = commonProcess(nestedMessage);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            Assert.assertNotNull(newSignCert);
            Assert.assertFalse(Arrays.equals(oldSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue()));
        }
    }

    /**
     * 仅更新主题项和密钥对
     *
     * @throws Exception
     */
    @Test
    public void testOnlyUpdateSubjectWithNestedMessage() throws Exception {
        KeyUpdateReqMessage onlyUpdateSubjectMessage = getOnlyUpdateSubjectAndKeyPairMessage();
        NestedMessage nestedMessage = getNestedMessage(onlyUpdateSubjectMessage, BusinessTypeEnum.UPDATE_KEY);
        MultipleCmpReqContext context = commonProcess(nestedMessage);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            Assert.assertNotNull(newSignCert);
            Assert.assertEquals(oldSignCert.getSubject().getLdapName(), newSignCert.getSubject().getLdapName());
        }
    }


    @Test
    public void testUpdateKeyWithNestedMessage() throws Exception {
        UpdateKeyReqMessage message = getMessage();
        NestedMessage nestedMessage = getNestedMessage(message, BusinessTypeEnum.UPDATE_KEY);
        MultipleCmpReqContext context = commonProcess(nestedMessage);
        for (CmpRespResult cmpRespResult : context.getCmpRespResults()) {
            Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
            byte[] signCertDer = cmpRespResult.getSignCertDer();
            Assert.assertNotNull(signCertDer);
            X509Certificate newSignCert = new X509Certificate(signCertDer);
            Assert.assertFalse(Arrays.equals(oldSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue(), newSignCert.getSubjectPublicKeyInfo().getSubjectPublicKey().getValue()));
            Assert.assertEquals(message.getSubject(), newSignCert.getSubject().getLdapName());
            Assert.assertTrue(compare(message.getValidity().getEndTime(), newSignCert.getNotAfter()));
        }

    }


    private MultipleCmpReqContext commonProcess(CmpMessage keyUpdateReqMessage) throws Exception {
        MultipleCmpReqContext context = new MultipleCmpReqContext();
        List<CmpMessage> keyUpdateReqMessageList = new ArrayList<>();
        keyUpdateReqMessageList.add(keyUpdateReqMessage);
        multipleMessageEncoder.encode(context, keyUpdateReqMessageList);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        multipleMessageDecoder.decode(context, respPkiMessages);
        return context;
    }


    private UpdateKeyReqMessage getOnlyUpdateSubjectAndKeyPairMessage() throws Exception {
        oldSignCert = getNewSignCert();
        Assert.assertNotNull(oldSignCert);
        String serialNumber = Hex.encode(false, oldSignCert.getSerialNumber());
        String issuer = oldSignCert.getIssuer().getLdapName();
        final long certRequestId = CryptoUtils.generateRandomLong();
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        SubjectInfo subjectInfo = SubjectInfo.builder().c("CN").cn("test").l("test L").build();
        String p10Str = generateRSAP10();
        // p10 用于代替 POP 验证
        CustomFreeText customFreeText = CustomFreeText.builder()
                .p10(p10Str)
                .build();
        return UpdateKeyReqMessage.defaultMessage().toBuilder()
                .businessTypeEnum(BusinessTypeEnum.UPDATE_KEY)
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .issuer(issuer)
                .serialNumber(serialNumber)
                .certRequestId(certRequestId)
                .subject(subjectInfo)
                .publicKey(mockKeyPair.getPublic().getEncoded())
                .customFreeText(customFreeText)
                .build();
    }

    private UpdateKeyReqMessage getOnlyUpdateKeyPairMessage() throws Exception {
        oldSignCert = getNewSignCert();
        Assert.assertNotNull(oldSignCert);
        String serialNumber = Hex.encode(false, oldSignCert.getSerialNumber());
        String issuer = oldSignCert.getIssuer().getLdapName();
        String p10Str = generateRSAP10();
        final long certRequestId = CryptoUtils.generateRandomLong();
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        // 构建密钥更新 CMP 请求所需的参数，只需证书序列号、certRequestId（必有），有效期、主题、密钥对可选
        // p10 用于代替 POP 验证
        CustomFreeText customFreeText = CustomFreeText.builder()
                .p10(p10Str)
                .build();
        return UpdateKeyReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .issuer(issuer)
                .serialNumber(serialNumber)
                .certRequestId(certRequestId)
                .publicKey(mockKeyPair.getPublic().getEncoded())
                .customFreeText(customFreeText)
                .build();
    }

    /**
     * 主题项、有效期、密钥对都进行更新
     *
     * @return
     */
    private UpdateKeyReqMessage getMessage() throws Exception {
        oldSignCert = getNewSignCert();
        Assert.assertNotNull(oldSignCert);
        String serialNumber = Hex.encode(false, oldSignCert.getSerialNumber());
        String issuer = oldSignCert.getIssuer().getLdapName();
        String p10Str = generateRSAP10();
        Date now = new Date();
        Validity validity = Validity.builder()
                .startTime(now)
                .endTime(DateUtil.dateAddYear(now, 2))
                .build();
        final long certRequestId = new Random().nextInt(1000000000);
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        SubjectInfo subjectInfo = SubjectInfo.builder().c("CN").cn("test").l("test L").build();
        // 构建密钥更新 CMP 请求所需的参数，只需证书序列号、certRequestId（必有），有效期、主题、密钥对可选
        CustomFreeText customFreeText = CustomFreeText.builder()
                .p10(p10Str)
                .build();
        return UpdateKeyReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .issuer(issuer)
                .serialNumber(serialNumber)
                .certRequestId(certRequestId)
                .subject(subjectInfo)
                .publicKey(mockKeyPair.getPublic().getEncoded())
                .validity(validity)
                .customFreeText(customFreeText)
                .build();
    }
}
