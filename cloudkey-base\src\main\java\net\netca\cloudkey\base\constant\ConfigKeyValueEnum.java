package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * @ProjectName: cloudkey
 * @Package: net.netca.cloudkey.base.constant
 * @ClassName: ConfigKeyValueEnum
 * @Description: 定义配置表的键名, 供查询使用
 * @Author: dyr
 * @CreateDate: 2020/5/9 14:55
 */
public enum ConfigKeyValueEnum implements IBaseEnum {

    // 加密卡主密钥哈希值16进制编码
    ENCRYPTIONCARD_SYS_MASTER_KEY_HASH("ENCRYPTIONCARD_SYS_MASTER_KEY_HASH", "加密卡主密钥哈希值16进制编码"),

    // 短信提示语配置
    SMS_SEND_PIN("SMS_SEND_PIN", "PIN码短信提示语"),
    SMS_SEND_VALIDCODE("SMS_SEND_VALIDCODE", "短信验证码短信提示语"),
    SMS_SEND_ISSUE_CERT("SMS_SEND_ISSUE_CERT", "证书签发成功通知短信提示语"),
    SMS_SEND_UNLOCK("SMS_SEND_UNLOCK", "解锁后的用户新口令短信提示语"),
    SMS_SEND_NOTICE_USER("SMS_SEND_NOTICE_USER", "已录入授权用户数量给授权人短信提示语"),
    SMS_SEND_AUTHORIZE("SMS_SEND_AUTHORIZE", "成功授权后提示证授权人的短信提示语"),
    SMS_SEND_AUTHORIZED_PIN("SMS_SEND_AUTHORIZED_PIN", "被授权口令短信提示语"),
    SMS_SEND_AUTHORIZED_MODIFY("SMS_SEND_AUTHORIZED_MODIFY", "授权修改通知被授权人的短信提示语"),
    SMS_SEND_CERT_EXPIRY_WARNING("SMS_SEND_CERT_EXPIRY_WARNING", "证书过期预警的短信提示语"),
    SMS_SEND_DEVICE_BINDING("SMS_SEND_DEVICE_BINDING", "设备绑定短信提示语"),
    SMS_SEND_COORD_SIGN_TASK_TODO("SMS_SEND_COORD_SIGN_TODO", "协同签名任务待签署通知短信示语"),
    SMS_SEND_COORD_SIGN_TASK_REJECT("SMS_SEND_COORD_SIGN_REJECT", "协同签名任务用户拒绝签署通知短信提示语"),
    SMS_SEND_COORD_SIGN_TASK_COMPLETE("SMS_SEND_COORD_SIGN_COMPLETE", "协同签名任务完成通知短信提示语"),

    // 业务平台接口配置
    BPMS_URL_PREFIX("CLOUDKEY.SYSTEM.BPMS.URL_PREFIX", "业务平台URL路径前缀"),
    BPMS_DEC_ADMIN_PIN_URL("CLOUDKEY.SYSTEM.BPMS.DEC_ADMIN_PIN_URL", "业务平台解密管理员pin码路径"),
    BPMS_REGISTER_CERT_URL("CLOUDKEY.SYSTEM.BPMS.REGISTER_CERT_URL", "业务平台新申请证书路径"),
    BPMS_REQUEST_SEARCH_URL("CLOUDKEY.SYSTEM.BPMS.REQUEST_SEARCH_URL", "业务平台查询业务状态路径"),
    BPMS_DOWNLOAD_CERT_URL("CLOUDKEY.SYSTEM.BPMS.DOWNLOAD_CERT_URL", "业务平台下载证书路径"),
    BPMS_REVOKE_CERT_URL("CLOUDKEY.SYSTEM.BPMS.REVOKE_CERT_URL", "业务平台注销证书路径"),
    BPMS_RENEWAL_CERT_URL("CLOUDKEY.SYSTEM.BPMS.RENEWAL_CERT_URL", "业务平台证书续期路径"),
    BPMS_UPDATE_CERT_URL("CLOUDKEY.SYSTEM.BPMS.UPDATE_CERT_URL", "业务平台证书密钥更新路径"),
    BPMS_REAPPLY_CERT_URL("CLOUDKEY.SYSTEM.BPMS.REAPPLY_CERT_URL", "业务平台证书密钥更新路径"),
    BPMS_SUBMIT_REQUEST_FILE_URL("CLOUDKEY.SYSTEM.BPMS.SUBMIT_REQUEST_FILE_URL", "业务平台上传附件路径"),

    // 业务平台事件证书接口配置
    BPMS_EVENTCERT_URL_PREFIX("CLOUDKEY.SYSTEM.BPMS.EVENTCERT.URL_PREFIX", "业务平台事件证书URL路径前缀"),
    BPMS_REGISTER_EVENT_CERT_URL("CLOUDKEY.SYSTEM.BPMS.REGISTER_EVENT_CERT_URL", "业务平台新申请事件证书路径"),
    // 微信配置
    WECHAT_ACCESS_TOKEN_URL("CLOUDKEY.SYSTEM.WECHAT.ACCESS_TOKEN_URL", "微信获取token路径"),
    WECHAT_APPID("CLOUDKEY.SYSTEM.WECHAT.APPID", "微信的appid号"),
    WECHAT_SECRET("CLOUDKEY.SYSTEM.WECHAT.SECRET", "微信的secret号"),
    WECHAT_GET_UNLIMITED_URL("CLOUDKEY.SYSTEM.WECHAT.GET_UNLIMITED_URL", "微信获取未限制路径"),
    WECHAT_QRCODE_NOT_HOST("CLOUDKEY.SYSTEM.WECHAT.QRCODE_NOT_HOST", "是否产生带host二维码，默认false"),
    WECHAT_QRCODE_HOST("CLOUDKEY.SYSTEM.WECHAT.QRCODE_HOST", "二维码指定的服务器安全域名，长度限定22个字符"),

    // 签章OCSP配置
    OCSP_URL("CLOUDKEY.SYSTEM.OCSP_URL", "OCSP路径"),

    // 系统配置
    MOBILE_PHONE_SIGN_ALGO("CLOUDKEY.SYSTEM.MOBILE_PHONE_SIGN_ALGO", "手机二维码签名算法"),
    MOBILE_QRCODE_PREFIX("CLOUDKEY.SYSTEM.MOBILE_QRCODE_PREFIX", "二维码前缀"),
    MOBILE_QRCODE_DOMAIN("CLOUDKEY.SYSTEM.MOBILE_QRCODE_DOMAIN", "手机扫码，二维码的服务器地址数据"),
    BASE64_STRICT_MODE_ENABLED("CLOUDKEY.SYSTEM.BASE64_STRICT_MODE_ENABLED", "是否启用Base64严格模式"),

    // 时间戳配置
    CLOUDKEY_SYSTEM_TIMESTAMP_URL("CLOUDKEY.SYSTEM.TIMESTAMP_URL", "时间戳地址"),
    CLOUDKEY_SYSTEM_TIMESTAMP_HASH_ALGO("CLOUDKEY.SYSTEM.TIMESTAMP_HASH_ALGO", "时间戳算法"),

    // 是否只允许一个用户登录
    CLOUDKEY_SYSTEM_SINGLE_SIGN_ON("CLOUDKEY.SYSTEM.SINGLE_SIGN_ON", "是否只允许一个用户登录"),

    // 是否允许通过接口录入授权信息
    CLOUDKEY_SYSTEM_PERMIT_INVOKE_AUTHORIZE("CLOUDKEY.SYSTEM.PERMIT_INVOKE_AUTHORIZE", "是否允许通过接口录入授权信息"),

    // 产生短信验证码接口，1通过短信下发，2通过接口返回，3以上均有
    CLOUDKEY_SYSTEM_GEN_VALIDCODE("CLOUDKEY.SYSTEM.GEN_VALIDCODE", "产生短信验证码接口，1通过短信下发，2通过接口返回，3以上均有"),

    // 是否需要验证图形验证码
    CLOUDKEY_SYSTEM_NEED_VERIFY_IMG_VALIDCODE("CLOUDKEY.SYSTEM.NEED_VERIFY_IMG_VALIDCODE", "是否需要验证图形验证码"),

    // 加密卡rsa密钥对长度
    CLOUDKEY_SYSTEM_RSA_KEYPAIR_LENGTH("CLOUDKEY.SYSTEM.RSA_KEYPAIR_LENGTH",
            "加密卡rsa密钥对长度，得安、三未都是加密卡1412，软设备1203，-1表示不检查"),

    // 加密卡sm2密钥对长度
    CLOUDKEY_SYSTEM_SM2_KEYPAIR_LENGTH("CLOUDKEY.SYSTEM.SM2_KEYPAIR_LENGTH",
            "加密卡sm2密钥对长度，得安加密卡292，三未加密卡104，软设备117，-1表示不检查"),

    // 业务平台用户扩展字段，工号唯一标识
    CLOUDKEY_UID_BPMS_USEREXTFIELDID("CLOUDKEY.UID_BPMS_USEREXTFIELDID", "业务平台用户唯一标识扩展id"),

    // 批量上传签章图片功能，文件编码
    CLOUDKEY_UPLOAD_ZIP_ENCODING("CLOUDKEY.UPLOAD_ZIP_ENCODING", "批量上传签章图片功能，文件编码"),

    // 重启tomcat相关配置
    CLOUDKEY_TOMCAT_SERVICE_NAME("CLOUDKEY.TOMCAT.SERVICE.NAME", "系统配置的tomcat service name"),
    CLOUDKEY_SYSTEM_ADDRESS("CLOUDKEY.SYSTEM.ADDRESS", "部署的服务器ip和tomcat端口，根据实际部署的ip进行配置，多个用英文逗号隔开，格式为ip:port"),

    // 三未加密卡配置
    SYSTEM_SANWEIOPERATOR("CLOUDKEY.SYSTEM.SANWEIOPERATOR", "三未加密卡操作员账号"),
    SYSTEM_SANWEIPASSWORD("CLOUDKEY.SYSTEM.SANWEIPASSWORD", "三未加密卡操作员账号对应的密码"),

    // 加密卡配置
    SJK1555_OPERATOR("CLOUDKEY.SYSTEM.SJK1555_OPERATOR", "加密卡操作员账号"),
    SJK1555_PASSWORD("CLOUDKEY.SYSTEM.SJK1555_PASSWORD", "加密卡操作员账号对应的密码"),

    // 系统配置
    USER_KEYPAIR_EXPIRE_TIME("CLOUDKEY.SYSTEM.USER_KEYPAIR_EXPIRE_TIME", "用户密钥对不被使用的失效时间"),
    DB_INFO_EXPIRE_TIME("CLOUDKEY.SYSTEM.DB_INFO_EXPIRE_TIME", "数据库缓存，添加到缓存后过了多久失效"),
    IS_KEYPAIR_CERT_CHAIN("CLOUDKEY.SYSTEM.IS_KEYPAIR_CERT_CHAIN", "访问外部接口是否验证证书链"),

    // 短信通用配置
    USER_VERIFY_EXPIRE_TIME("CLOUDKEY.SYSTEM.USER_VERIFY_EXPIRE_TIME", "用户信息校验失效时间 --二维码，短信验证码失效时间"),
    VALID_CODE_MIN_NEXT_CREATE_TIME("CLOUDKEY.SYSTEM.VALID_CODE_MIN_NEXT_CREATE_TIME", "下次产生验证码的最短时间间隔"),

    // 系统增值电信业务
    RECORD_INFO("CLOUDKEY.RECORD_INFO", "系统首页的备案信息"),

    // 过期前允许续期的时间间隔，若大于该间隔，不允许进行续期
    CLOUDKEY_SYSTEM_RENEWAL_CERT_INTERVAL("CLOUDKEY.SYSTEM.RENEWAL_CERT_INTERVAL", "过期前允许续期的时间间隔，单位天"),

    // 电子签名系统配置
    ELESIGN_SYSTEM_TIMESTAMP("ELESIGN.SYSTEM.TIMESTAMP", "电子签名系统与云密钥对接时间戳"),
    ELESIGN_SYSTEM_SIGNCERT("ELESIGN.SYSTEM.SIGNCERT", "电子签名系统与云密钥对接时间戳签名证书"),

    // 扩展功能是否开启配置
    ELESIGN_OPEN("CLOUDKEY.ELESIGN.OPEN", "是否开启电子签名系统扩展功能"),
    MQTT_OPEN("CLOUDKEY.MQTT.OPEN", "是否开启手机消息推送功能"),

    // ActiveMQ MQTT配置信息
    // MQTT-服务器连接地址，如果有多个，用逗号隔开
    MQ_MQTT_CLIENTURL("MQ.MQTT.CLIENTURL", "MQTT-客户端连接地址"),
    MQ_MQTT_SERVERURL("MQ.MQTT.SERVERURL", "MQTT-服务器连接地址"),

    MQ_MQTT_CLIENT_ID("MQ.MQTT.CLIENT.ID", "MQTT-连接服务器默认客户端ID"),
    MQ_MQTT_COMPLETION_TIMEOUT("MQ.MQTT.COMPLETION.TIMEOUT", "MQTT-连接服务器有效期"),

    // 消息质量 0-最多一次，即发即弃；1-最少一次，客户端最少接收到一次；2-只一次，每个消息都只被接收到一次
    MQ_MQTT_QOS("MQ.MQTT.QOS", "消息质量"),
    MQ_MQTT_RETAINED("MQ.MQTT.RETAINED", "是否保留最新消息"),
    MQ_MQTT_CONNECTION_TIMEOUT("MQ.MQTT.CONNECTION.TIMEOUT", "连接超时时间"),
    MQ_MQTT_KEEPALIVED_INTERVAL("MQ.MQTT.KEEPALIVED.INTERVAL", "设置心跳时间间隔"),
    MQ_MQTT_MAXINFLIGHT("MQ.MQTT.MAXINFLIGHT", "最大等待被推送的消息数量"),
    MQTT_TOPIC_PREFIX("CLOUDKEY.MQTT.TOPIC.PREFIX", "订阅主题前缀"),
    MQTT_MSG_SIGNATURE("CLOUDKEY.MQTT.MSG.SIGNATURE", "消息通知署名"),

    // mqtt账号密码开始
    MQ_MQTT_USERNAME("MQ.MQTT.USERNAME", "服务端账号"),
    MQ_MQTT_PASSWORD("MQ.MQTT.PASSWORD", "服务端密码"),
    MQTT_SUBSCRIBER_USERNAME("CLOUDKEY.MQTT.SUBSCRIBER.USERNAME", "MQTT-客户端账号"),
    MQTT_SUBSCRIBER_PASSWORD("CLOUDKEY.MQTT.SUBSCRIBER.PASSWORD", "MQTT-客户端密码"),

    // ipone配置开始
    APPLE_KEYSTORE_PATH("CLOUDKEY.SYSTEM.APPLE.KEYSTORE.PATH", "IPhone密钥对路径"),
    APPLE_KEYSTORE_PWD("CLOUDKEY.SYSTEM.APPLE.KEYSTORE.PWD", "IPhone密钥对账号"),
    APPLE_KEYSTORE_TYPE("CLOUDKEY.SYSTEM.APPLE.KEYSTORE.TYPE", "IPhone密钥对密码"),
    APPLE_SOUND("CLOUDKEY.SYSTEM.APPLE.SOUND", "IPhone提示音"),
    APPLE_GATEWAY_DESTINATION("CLOUDKEY.SYSTEM.APPLE.GATEWAY.DESTINATION", "IPhone网关url"),
    APPLE_GATEWAY_DESTINATION_PORT("CLOUDKEY.SYSTEM.APPLE.GATEWAY.DESTINATION.PORT", "IPhone网关端口"),
    APPLE_FEEDBACK_DESTINATION("CLOUDKEY.SYSTEM.APPLE.FEEDBACK.DESTINATION", "IPhone回溯url"),
    APPLE_FEEDBACK_DESTINATION_PORT("CLOUDKEY.SYSTEM.APPLE.FEEDBACK.DESTINATION.PORT", "IPhone回溯端口"),
    APPLE_TOPIC("CLOUDKEY.SYSTEM.APPLE.TOPIC", "IPhone主题类"),

    // 配置是否在签名后马上进行验签操作
    VERIFY_AFTER_SIGN("CLOUDKEY.SYSTEM.VERIFY_AFTER_SIGN", "是否在签名后进行验签操作后才返回"),

    // 功能/前端菜单展示配置
    SERVICE_CONFIG_OPEN("SERVICE_CONFIG.OPEN", "配置app初始化二维码功能是否开启"),
    SERVICE_MANAGE_OPEN("SERVICE_MANAGE.OPEN", "系统配置-服务管理的tab标签是否开启"),
    SERVICE_SELF_REGISTER_OPEN("SERVICE_SELF_REGISTER.OPEN", "自助申请服务是否开启"),
    APPROVE_MANAGE_OPEN("APPROVE_MANAGE_OPEN", "审核管理是否开启"),

    // 外部系统接口解锁，1通过短信下发，2通过接口返回（接口返回使用证书加密），3以上均有
    CLOUDKEY_SYSTEM_RETURN_UNLOCK_PIN("CLOUDKEY.SYSTEM.RETURN.UNLOCK_PIN", "系统配置-解锁是否返回加密的新PIN码"),

    // 上传签章图片接口是否开启
    UPLOAD_SEAL_PIC_OPEN("UPLOAD_SEAL_PIC.OPEN", "上传签章图片接口是否开启"),

    CLOUDKEY_SYSTEM_RETURN_REGISTER_PIN("CLOUDKEY.SYSTEM.RETURN.REGISTER_PIN", "系统配置-第三方接口注册用户是否返回加密的初始PIN码"),
    // 是否开启防重放攻击校验
    VALID_TIMESTAMP_TOKEN_OPEN("VALID_TIMESTAMP_TOKEN.OPEN", "是否开启防重放攻击校验"),

    CHECK_APPLICATION_ID("CHECK_APPLICATION_ID", "是否开启检验ApplicationId入参"),

    // 防重放 随机数有效时间值(这个时间内 随机数不能重复) 如果值为-1 表示不校验时间戳 随机数的有效时间为默认值(300秒)
    CHECK_REPLAY_NONCE_VALID_TIME("CHECK_REPLAY_NONCE_VALID_TIME", "防重放随机数有效时间"),

    // 时间戳校验允许的时间差，单位秒
    VALID_TIMESTAMP_INTERVAL("VALID_TIMESTAMP_INTERVAL", "时间戳校验允许的时间差，单位秒"),

    // 随机数校验允许重复的时间范围，单位秒
    VALID_TIMESTAMP_TOKEN_EXPIRE_TIME("VALID_TIMESTAMP_TOKEN_EXPIRE_TIME", "随机数校验允许重复的时间范围，单位秒"),

    // 腾讯云相关信息
    TECENT_CLOUD_SECRETID("TECENT_CLOUD_SECRETID", "腾讯云密钥secretId"),
    TECENT_CLOUD_SECRETKEY("TECENT_CLOUD_SECRETKEY", "腾讯云密钥secretKey"),
    TECENT_CLOUD_FACE_HOST("TECENT_CLOUD_FACE_HOST", "腾讯云-api 人脸识别模块的host"),
    TECENT_CLOUD_FACE_REGION("TECENT_CLOUD_FACE_REGION", "腾讯云-api 人脸识别模块的region"),
    TECENT_CLOUD_FACE_SERVICE("TECENT_CLOUD_FACE_SERVICE", "腾讯云-api 人脸识别模块的service"),
    TECENT_CLOUD_FACE_VERSION("TECENT_CLOUD_FACE_VERSION", "腾讯云-api 人脸识别模块的version"),
    TECENT_CLOUD_FACE_GETBIZTOKEN_ACTION("TECENT_CLOUD_FACE_GETBIZTOKEN_ACTION", "腾讯云-api 获取giztoken的action"),
    TECENT_CLOUD_FACE_GETRESULT_ACTION("TECENT_CLOUD_FACE_GETRESULT_ACTION", "腾讯云-api 获取人机校验结果的action"),

    // 下载证书定时任务循环执行时间，单位秒
    DOWNCERT_SCHEDULE_TIME("DOWNCERT_SCHEDULE_TIME", "下载证书定时任务循环执行时间，单位秒"),

    // 纯数字初始pin码长度
    NUMERIC_INITIAL_PIN_LENGTH("CLOUDKEY.SYSTEM.NUMERIC_INITIAL_PIN_LENGTH", "纯数字初始pin码长度"),

    // 系统logo图片URL
    LOGO_INFO("CLOUDKEY.SYSTEM.LOGO_INFO", "系统logo图片URL"),

    // 证书预约注销任务配置json
    RESERVE_REVOKE_TASK("CLOUDKEY.SYSTEM.RESERVE.REVOKE.TASK", "证书预约注销任务配置json"),

    CERT_DOWN_TASK("CLOUDKEY.SYSTEM.CERT.DOWN.TASK", "定时下载证书任务配置json"),

    // 短信控制台 是否启用
    SMS_CONSOLE("CLOUDKEY.SYSTEM.SMS.CONSOLE", "控制是否开启短信控制台"),

    // 是否开启新旧uid共存功能
    UID_ALIAS_OPEN("CLOUDKEY_SYSTEM_UID_ALIAS_OPEN", "是否开启新旧uid共存功能"),

    // 创建或修改证书用户时开不开其唯一性检测
    UNIQUE_PHONE("CLOUDKEY.SYSTEM.UNIQUE.PHONE", "手机号码唯一性检测"),

    UNIQUE_IDENTITY("CLOUDKEY.SYSTEM.UNIQUE.IDENTITY", "身份证件号码唯一性检测"),

    ARCHIVE_AUDIT_LOG_START_TIME("CLOUDKEY.SYSTEM.ARCHIVE.AUDIT.LOG.START.TIME", "归档日志库应用日志表开始启用时间"),
    // 是否调用签名验签服务
    IS_CALL_SVS("CLOUDKEY.SYSTEM.CALL_SVS", "是否调用签名验签服务(1为开启调用, 0为本地验证)"),
    // 签名验签服务器地址
    SVS_SERVER_URL("CLOUDKEY.SYSTEM.SVS_SERVER_URL", "签名验签服务器地址"),
    PARAMETER_ENCRYPT_KEY("CLOUDKEY.PARAMETER.ENCRYPT_KEY", "敏感参数加解密Key"),

    USER_CHECK_ENABLE("CLOUDKEY.USER.CHECK_ENABLE", "用户信息第三方检验开关"),

    EXIST_PIN_CHECK_ENABLE("CLOUDKEY.EXIST.PIN.CHECK_ENABLE", "强制用户密码失效开关"),

    DATA_LOG_ENABLE("CLOUDKEY.DATA.LOG_ENABLE", "是否记录接口返回值、签名及验签接口中签名原文与签名值"),

    APPLICATION_LOG_ENABLE("CLOUDKEY.APPLICATION.LOG_ENABLE", "是否禁用应用日志 true表示禁用应用日志，false表示启用应用日志"),

    INTER_CONNECTION_LOG_ENABLE("CLOUDKEY.INTER_CONNECTION.LOG_ENABLE", "是否禁用互联互通日志 true表示停止记录日志，false表示允许记录日志"),

    THIRD_SYSTEM_LOG_ENABLE("CLOUDKEY.THIRD_SYSTEM.LOG_ENABLE", "是否第三方系统日志 true表示停止记录日志，false表示允许记录日志"),

    TENCENT_FACE_MIN_SCORE("CLOUDKEY.TENCENT.FACE.MIN.SCORE", "腾讯云人脸认证通过阈值，调用腾讯云认证接口低于该值则认为不为同一人"),

    CLOUDKEY_APP_LOG_SYNC("CLOUDKEY_APP_LOG_SYNC", "是否采用同步方式记录日志，true 表示同步记录日志，false异步记录日志"),

    // 企业微信相关配置
    ZDFY_CORP_WECHAT_ID("ZDFY-CORP-WECHAT-ID", "中大山附一企业微信id"),
    ZDFY_CORP_WECHAT_AGENT_ID("ZDFY-CORP-WECHAT-AGENT-ID", "中大山附一企业微信自建应用id"),
    ZDFY_CORP_WECHAT_AGENT_SECRET("ZDFY-CORP-WECHAT-AGENT-SECRET", "中大山附一企业微信自建应用secret"),
    CORP_WECHAT_ACCESS_TOKEN_URL("CORP-WECHAT-ACCESS-TOKEN-URL", "企业微信登录获取accessToken的url"),
    CORP_WECHAT_GET_USER_INFO_URL("CORP-WECHAT-GET-USER-INFO-URL", "企业微信登录获取用户登录信息的url"),
    CORP_WECHAT_GET_CODE_REDIRECT_URI("CORP-WECHAT-GET-CODE-REDIRECT-URI", "企业微信获取code的回调地址"),
    CLOUDKEY_SYSTE_HOST_URL("CLOUDKEY.SYSTEM.HOST_URL", "云密钥主机地址，需要带上cloudkeyserver"),
    CLOUDKEY_SYSTEM_PAGE_URL("CLOUDKEY.SYSTEM.PAGE_URL", "云密钥企业微信跳转页面host，需要带上cloudkeyserver"),

    // 中大附一企业微信中台相关配置
    ZD_CORP_MID_APP_ID("ZD-CORP-MID-APP-ID", "中大附一企业微信中台appId"),
    ZD_CORP_MID_APP_SECRET("ZD-CORP-MID-APP-SECRET", "中大附一企业微信中台appSecret"),
    ZD_CORP_MID_RANDOM_KEY("ZD-CORP-MID-RANDOM-KEY", "中大附一企业微信中台randomKey"),
    ZD_CORP_MID_QRCODE_URL("ZD-CORP-MID-QRCODE-URL", "中大附一企业微信中台二维码地址"),
    ZD_CORP_MID_ACCESS_TOKEN_URL("ZD-CORP-MID-ACCESS-TOKEN-URL", "中大附一企业微信中台登录获取accessToken的url"),
    ZD_CORP_MID_GET_USER_INFO_URL("ZD-CORP-MID-GET-USER-INFO-URL", "中大附一企业微信中台登录获取用户登录信息的url"),
    USER_INFO_DESENSITIZATION("CLOUDKEY.SYSTEM.USER_INFO.DESENSITIZATION", "控制是否开启用户个人信息脱敏"),
    SECURITY_PROTECT_KEY("CLOUDKEY.SYSTEM.SECURITY_PROTECT_KEY", "数据安全保护密钥"),
    APP_LOG_RETAINED_MONTH("CLOUDKEY.SYSTEM.APP_LOG_RETAINED_MONTH", "应用日志保留月份"),
    CLOUDKEY_SYSTEM_NAME("CLOUDKEY.SYSTEM.NAME", "系统名称"),
    PIN_EXPIRATION_TIME("CLOUDKEY.SYSTEM.PIN.EXPIRATION.TIME", "口令过期时间(天)，-1表示不过期"),
    DEVICE_EXPIRATION_TIME("CLOUDKEY.SYSTEM.DEVICE.EXPIRATION.TIME", "设备过期时间(天)，-1表示不过期"),
    ATTACHED_DEVICE_COUNT("CLOUDKEY.SYSTEM.ATTACHED.DEVICE.COUNT", "附属设备个数"),
    ATTACHED_DEVICE_OPEN("CLOUDKEY.SYSTEM.ATTACHED.DEVICE.OPEN", "是否开启附属设备功能"),
    CLOUDKEY_EDSS_PARAM_REQUIRED_ENABLE("CLOUDKEY_EDSS_PARAM_REQUIRED_ENABLE", "电子存证存证参数非空检测开关"),
    CLOUDKEY_OLD_SIGNATURE_API_EDSS_STRATEGY_TYPE("CLOUDKEY_OLD_SIGNATURE_API_EDSS_STRATEGY_TYPE",
            "原签名签章接口数据同步到电子存证配置"),
    DELAY_PASSWORD_SMS_AFTER_DOWNLOAD_ENABLE("DELAY_PASSWORD_SMS_AFTER_DOWNLOAD_ENABLE", "延迟初始密码短信至证书下载后发送开关"),

    CLOUDKEY_SYSTEM_THEME("CLOUDKEY.SYSTEM.THEME", "系统颜色主题"),

    // 协同签名相关配置
    DEFAULT_TASK_EXPIRE_DAY("CLOUDKEY.COSIGN.DEFAULT_TASK_EXPIRE_DAY", "协同签名任务默认过期天数"),
    BATCH_SIGN_LIMIT_COUNT("CLOUDKEY.COSIGN.BATCH_SIGN_LIMIT_COUNT", "协同签名批量签名的数量限制"),
    COORD_SIGN_TASK_EXPIRE_ALERT_DAY("CLOUDKEY.COSIGN.TASK_EXPIRE_ALERT_DAY", "协同签名即将过期提醒的天数"),
    COORD_SIGN_DEFAULT_SIGN_OPINION("CLOUDKEY.COSIGN.DEFAULT_SIGN_OPINION", "协同签名默认签名意见"),
    COORD_SIGN_DEFAULT_REJECT_OPINION("CLOUDKEY.COSIGN.DEFAULT_REJECT_OPINION", "协同签名默认拒绝意见"),
    BUSINESS_COORD_SIGN_API_LOG_START_TIME("CLOUDKEY.SYSTEM.BUSINESS.COORD_SIGN_API.LOG.START.TIME",
            "协同签署日志表归档日志库开始启用时间"),
    CLOUDKEY_COSIGN_API_LOG_ENABLE("CLOUDKEY.COSIGN.API_LOG_ENABLE", "是否禁用协同签名接口日志"),
    CLOUDKEY_SIGNATURE_SEAL_SAVE_BD_WITH_EDSS("CLOUDKEY_SIGNATURE_SEAL_SAVE_BD_WITH_EDSS",
            "签名签章接口数据同步到存证后是否仍然保存到数据库开关"),

    // V2.41
    EXPORT_APP_LOG_PER_COUNT("EXPORT_APP_LOG_PER_COUNT", "应用日志导出每次查询的记录数"),
    EXPORT_APP_LOG_MAX_COUNT("EXPORT_APP_LOG_MAX_COUNT", "应用日志导出最大记录条数"),

    CLOUDKEY_COSIGN_TASK_EXPIRE_KEEP_DAY("CLOUDKEY.COSIGN.TASK_EXPIRE_KEEP_DAY", "协同签名任务过期保留天数"),

    CLOUDKEY_COSIGN_ENABLE_EXPIRE_DELETE("CLOUDKEY.COSIGN.ENABLE_EXPIRE_DELETE", "协同签名任务过期超过保留的天数时，是否需要逻辑删除"),
    CLOUDKEY_COORD_SIGN_SMS_NOTIFY_ENABLE("CLOUDKEY.COSIGN.SMS_NOTIFY_ENABLE", "是否启用协同签名任务短信通知"),

    // 证书生命周期管理器配置
    CERTIFICATE_LIFECYCLE_MANAGER_IMPLEMENTATION_TYPE("CLOUDKEY.CERTIFICATE.LIFECYCLE.MANAGER.IMPLEMENTATION.TYPE",
            "证书生命周期管理器实现类型"),

    // 长江CA平台配置项
    CJCA_BASE_URL("CLOUDKEY.CJCA.BASE.URL", "长江CA平台基础URL"),
    CJCA_CA_COMM_CERT_SHA256("CLOUDKEY.CJCA.CA.COMM.CERT.SHA256", "CA通讯证书SHA256指纹"),
    CJCA_RA_COMM_CERT("CLOUDKEY.CJCA.RA.COMM.CERT", "RA通讯证书Base64编码"),
    CJCA_THIRD_PARTY_COMM_CERT("CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT", "第三方通讯证书Base64编码"),
    CJCA_THIRD_PARTY_COMM_CERT_KEY("CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY", "第三方通讯证书私钥Base64编码"),
    CJCA_SIGN_ALGO_NAME("CLOUDKEY.CJCA.SIGN.ALGO.NAME", "签名算法名称"),
    CJCA_DEFAULT_TEMPLATE_ID("CLOUDKEY.CJCA.DEFAULT.TEMPLATE.ID", "默认证书模板ID"),
    CJCA_CONNECTION_TIMEOUT("CLOUDKEY.CJCA.CONNECTION.TIMEOUT", "连接超时时间(毫秒)"),
    CJCA_READ_TIMEOUT("CLOUDKEY.CJCA.READ.TIMEOUT", "读取超时时间(毫秒)"),
    CJCA_RETRY_MAX_ATTEMPTS("CLOUDKEY.CJCA.RETRY.MAX.ATTEMPTS", "最大重试次数"),
    CJCA_RETRY_DELAY("CLOUDKEY.CJCA.RETRY.DELAY", "重试间隔(毫秒)"),

    ;

    private String code;
    private String description;

    ConfigKeyValueEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

}
