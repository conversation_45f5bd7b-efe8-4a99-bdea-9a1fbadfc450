package net.netca.cloudkey.base.dto.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2023-06-05 17:21:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SignatureParseRespDTO {

    private String userCert;

    private String tsaToken;

    private String verifyErrorMessage;
}
