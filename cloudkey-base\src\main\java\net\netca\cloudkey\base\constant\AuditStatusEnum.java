package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @Description  审计状态常量 0待审计 ;1已审计；2无需审计
 * @Date 15:40 2020/5/21
 **/
public enum AuditStatusEnum implements IBaseEnum {

    WAIT_AUDIT(0, "待审计"),
    HAS_AUDIT(1, "已审计"),
    NOT_NEED_AUDIT(2, "无需审计");

    private Integer code;
    private String description;

    AuditStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }


}
