package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessCoordSignApiLogQueryDTO {

    @ApiModelProperty(value = "第三方系统认证ID")
    private String authId; // 第三方系统认证ID

    @ApiModelProperty(value = "接口日志类型")
    private Integer logType; // 日志类型

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    @ApiModelProperty(value = "调用方向：0-作为服务端，1-作为客户端")
    private Integer direction;

    @ApiModelProperty(value = "响应状态：0-成功，1-失败")
    private Long respStatus;

    @ApiModelProperty(value = "响应时间最小值范围")
    private Long durationMin;

    @ApiModelProperty(value = "响应时间最大值范围")
    private Long durationMax;

    // req_time 在这个范围内会被查询 [startTIme, endTime]
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期开始时间")
    private LocalDateTime reqTimeStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期开始时间")
    private LocalDateTime reqTimeEnd;


}
