package net.netca.cloudkey.base.dto.signature;

import lombok.Data;

/**
 * {
 *     "responseResult":{
 *         "status":0,
 *         "msg":"成功"
 *     },
 *     "contents":{
 *         "originalData":"原文签名数据",
 *         "signAlgo":4,
 *         "signature":"对原文数据的签名值"
 *     }
 * }
 * created by zaj on 2019/1/18
 */
@Data
public class SignatureResultRespDTO {

    /**
     * Base64不换行编码的hashValue
     */
    private String tbs;
    /**
     * 签名值
     */
    private String signature;

    /**
     *
     */
    private Integer cloudKeyUserId;
    /**
     * 用户类型
     */
    private Integer userType;

}
