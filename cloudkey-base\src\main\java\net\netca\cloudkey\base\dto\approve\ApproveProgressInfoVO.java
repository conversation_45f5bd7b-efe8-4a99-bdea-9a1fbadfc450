package net.netca.cloudkey.base.dto.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 17:54 2020/12/7
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class ApproveProgressInfoVO {

    private String time;
    private String operator;
    private String operate;
    private String Description;

}
