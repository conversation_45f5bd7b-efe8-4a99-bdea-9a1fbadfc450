package net.netca.sdk.sneakythrows;


import java.util.function.Function;

/**
 * net.netca.rasystem.rabase.sneakythrows 包内的接口是用于在 Lambda 表达式中优雅地抛出异常
 * 但是把双刃剑，需要谨慎使用
 * 理由：
 * try {
 *    sneakyThrow(new IOException());
 * } catch (IOException e) { // exception is never thrown in corresponding try block
 *    e.printStackTrace();
 * }
 *
 * <AUTHOR>
 * @date 2019-06-11 16:32
 */
@FunctionalInterface
public interface ThrowingFunction<T, R> {
    R apply(T t) throws Exception;

    @SuppressWarnings("unchecked")
    static <T extends Exception, R> R sneakyThrow(Exception t) throws T {
        throw (T) t;
    }

    static <T, R> Function<T, R> unchecked(ThrowingFunction<T, R> f) {
        return (t) -> {
            try {
                return f.apply(t);
            } catch (Exception ex) {
                return ThrowingFunction.sneakyThrow(ex);
            }
        };
    }
}
