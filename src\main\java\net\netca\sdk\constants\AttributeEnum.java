package net.netca.sdk.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AttributeEnum {
    CN("CN", "2.5.4.3", AttributeEncodeEnum.UTF8_STRING, 2),
    C("C", "2.5.4.6", AttributeEncodeEnum.PRINTABLE_STRING, 10),
    SERIAL_NUMBER("SERIALNUMBER", "2.5.4.5", AttributeEncodeEnum.UTF8_STRING, 9),
    L("L", "2.5.4.7", AttributeEncodeEnum.UTF8_STRING, 5),
    S("S", "2.5.4.8", AttributeEncodeEnum.UTF8_STRING, 6),
    ST("ST", "2.5.4.8", AttributeEncodeEnum.UTF8_STRING, 6),
    O("O", "2.5.4.10", AttributeEncodeEnum.UTF8_STRING, 4),
    OU("OU", "2.5.4.11", AttributeEncodeEnum.UTF8_STRING, 3),
    UID("UID", "0.9.2342.19200300.100.1.1", AttributeEncodeEnum.UTF8_STRING, 8),
    DC("DC", "0.9.2342.19200300.100.1.25", AttributeEncodeEnum.IA5_STRING, 7),
    EMAIL("EMAIL", "1.2.840.113549.1.9.1", AttributeEncodeEnum.IA5_STRING, 1),
    STREET("Street", "*******", AttributeEncodeEnum.DIRECTORY_STRING, 11),
    SURNAME("SURNAME", "*******", AttributeEncodeEnum.DIRECTORY_STRING, 12),
    GIVEN_NAME("GIVENNAME", "*******2", AttributeEncodeEnum.DIRECTORY_STRING, 13),
    INITIALS("INITIALS", "*******3", AttributeEncodeEnum.DIRECTORY_STRING, 14),
    GENERATION("GENERATION", "*******4", AttributeEncodeEnum.DIRECTORY_STRING, 15),
    UNSTRUCTURED_ADDRESS("unstructuredAddress", "1.2.840.113549.1.9.8", AttributeEncodeEnum.DIRECTORY_STRING, 16),
    UNSTRUCTURED_NAME("unstructuredName", "1.2.840.113549.1.9.2", AttributeEncodeEnum.IA5_STRING, 17),
    UNIQUE_IDENTIFIER("UniqueIdentifier", "*******5", AttributeEncodeEnum.UTF8_STRING, 18),
    DN_QUALIFIER("DN", "*******6", AttributeEncodeEnum.PRINTABLE_STRING, 19),
    PSEUDONYM("Pseudonym", "********", AttributeEncodeEnum.DIRECTORY_STRING, 20),
    POSTAL_ADDRESS("PostalAddress", "********", AttributeEncodeEnum.POSTAL_ADDRESS, 21),
    NAME_AT_BIRTH("NameAtBirth", "********.3.14", AttributeEncodeEnum.UTF8_STRING, 22),
    COUNTRY_OF_CITIZENSHIP("CountryOfCitizenship", "*******.*******.4", AttributeEncodeEnum.PRINTABLE_STRING, 23),
    COUNTRY_OF_RESIDENCE("CountryOfResidence", "*******.*******.5", AttributeEncodeEnum.PRINTABLE_STRING, 24),
    GENDER("Gender", "*******.*******.3", AttributeEncodeEnum.PRINTABLE_STRING, 25),
    PLACE_OF_BIRTH("PlaceOfBirth", "*******.*******.2", AttributeEncodeEnum.DIRECTORY_STRING, 26),
    DATE_OF_BIRTH("DateOfBirth", "*******.*******.1", AttributeEncodeEnum.GENERALIZED_TIME, 27),
    POSTAL_CODE("PostalCode", "********", AttributeEncodeEnum.UTF8_STRING, 28),
    BUSINESS_CATEGORY("BusinessCategory", "********", AttributeEncodeEnum.DIRECTORY_STRING, 29),
    TELEPHONE_NUMBER("TelephoneNumber", "********", AttributeEncodeEnum.PRINTABLE_STRING, 30),
    NAME("Name", "*******1", AttributeEncodeEnum.DIRECTORY_STRING, 31),
    /**
     * EV TLS jurisdictionLocality.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_LOCALITY("JurisdictionLocality", "*******.4.1.311.********", AttributeEncodeEnum.PRINTABLE_STRING, 32),
    /**
     * EV TLS jurisdictionState.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_STATE("JurisdictionState", "*******.4.1.311.********", AttributeEncodeEnum.PRINTABLE_STRING, 33),
    /**
     * EV TLS jurisdictionCountry.
     * https://cabforum.org/wp-content/uploads/EV-V1_5_2Libre.pdf
     */
    JURISDICTION_COUNTRY("JurisdictionCountry", "*******.4.1.311.********", AttributeEncodeEnum.PRINTABLE_STRING, 34),
    ORGANIZATION_IDENTIFIER("organizationIdentifier", "*******7", AttributeEncodeEnum.UTF8_STRING, 35),
    DESCRIPTION("description", "********", AttributeEncodeEnum.UTF8_STRING, 36),
    /**
     * title ATTRIBUTE ::= {
     * SUBTYPE OF name
     * WITH SYNTAX DirectoryString {ub-title}
     * ID id-at-title
     * }
     */
    TITLE("title", "********", AttributeEncodeEnum.DIRECTORY_STRING, 37),

    /**
     * uri ATTRIBUTE ::= {
     * WITH SYNTAX URI
     * EQUALITY MATCHING RULE uriMatch
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"uri"}
     * ID id-at-uri }
     */
    URI("uri","********",  AttributeEncodeEnum.DIRECTORY_STRING, 38),

    /**
     * urn ATTRIBUTE ::= {
     * SUBTYPE OF uri
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"urn"}
     * ID id-at-urn }
     */
    URN("urn","********",  AttributeEncodeEnum.DIRECTORY_STRING, 39),

    /**
     * telexNumber ATTRIBUTE ::= {
     * WITH SYNTAX TelexNumber
     * LDAP-SYNTAX telexNr.&id
     * LDAP-NAME {"telexNumber"}
     * ID id-at-telexNumber }
     * TelexNumber ::= SEQUENCE {
     * telexNumber PrintableString(SIZE (1..ub-telex-number)),
     * countryCode PrintableString(SIZE (1..ub-country-code)),
     * answerback PrintableString(SIZE (1..ub-answerback)),
     * ... }
     * ub-telex-number INTEGER ::= 14
     * ub-country-code INTEGER ::= 4
     * ub-answerback INTEGER ::= 8
     */
//    TELEX_NUMBER("2.5.4.21", "telexNumber", "TelexNumber"),

    /**
     * url ATTRIBUTE ::= {
     * SUBTYPE OF uri
     * LDAP-SYNTAX directoryString.&id
     * LDAP-NAME {"url"}
     * ID id-at-url
     * }
     */
    URL("url", "2.16.840.1.113730.1.2", AttributeEncodeEnum.DIRECTORY_STRING, 40)
    ;

    private final String type;
    private final String oid;
    private final AttributeEncodeEnum encode;
    private final int order;

    public static AttributeEnum getAttribute(String type) {
        for (AttributeEnum attribute : AttributeEnum.values()) {
            if (attribute.type.equalsIgnoreCase(type)) {
                return attribute;
            }
        }
        return null;
    }
}
