package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.OrganizationIdentityTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对机构证件类型的校验注解
 * @see OrganizationIdentityTypeValidator
 */
@Documented
@Constraint(validatedBy = OrganizationIdentityTypeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface OrganizationIdentityType {

    String message() default "证件类型值非法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
