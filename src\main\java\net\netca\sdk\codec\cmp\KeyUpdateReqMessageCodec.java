package net.netca.sdk.codec.cmp;


import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.cmp.*;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

import java.util.Objects;

/**
 * {@link KeyUpdateReqMessage} 的编解码生成器
 *
 * @author: zys
 */
public class KeyUpdateReqMessageCodec extends CmpMessageCodec {

    private KeyUpdateReqMessageEncoder encoder;


    private KeyUpdateReqMessageDecoder decoder;


    private KeyUpdateReqMessageCodec() {

    }

    private KeyUpdateReqMessageCodec(KeyUpdateReqMessageEncoder encoder) {
        this.encoder = encoder;
    }

    private KeyUpdateReqMessageCodec(KeyUpdateReqMessageDecoder decoder) {
        this.decoder = decoder;
    }

    /**
     * 创建 {@link KeyUpdateReqMessage} 的编码器
     *
     * @param configManagement {@link CmpMessageConfigManagement} 配置类
     * @return {@link MessageEncoder < CmpMessage >}
     */
    public static MessageEncoder<CmpMessage> createEncoder(CmpMessageConfigManagement configManagement) {
        return new KeyUpdateReqMessageCodec(new KeyUpdateReqMessageEncoder(configManagement));
    }

    /**
     * 创建 {@link KeyUpdateReqMessage} 的解码器
     *
     * @param configManagement {@link CmpMessageConfigManagement} 配置类
     * @return {@link MessageDecoder <PKIMessage>}
     */
    public static MessageDecoder<PKIMessage> createDecoder(CmpMessageConfigManagement configManagement) {
        return new KeyUpdateReqMessageCodec(new KeyUpdateReqMessageDecoder(configManagement));
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws Exception {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws Exception {
        encoder.encode(context, message);
    }

    final static class KeyUpdateReqMessageEncoder extends CmpMessageToAnyObjectEncoder {

        public KeyUpdateReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }


        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws Exception {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof KeyUpdateReqMessage) {
                KeyUpdateReqMessage keyUpdateReqMessage = (KeyUpdateReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                switch (keyUpdateReqMessage.getBusinessTypeEnum()) {
                    case RENEWAL:
                        singleCertReqContext.setEncodeResult(generateRenewalReqData((RenewReqMessage) keyUpdateReqMessage));
                        break;
                    case MODIFICATION:
                        singleCertReqContext.setEncodeResult(generateModifiedReqData((ModifiedCertReqMessage) keyUpdateReqMessage));
                        break;
                    case UPDATE_KEY:
                        singleCertReqContext.setEncodeResult(generateKeyUpdateReqData((UpdateKeyReqMessage) keyUpdateReqMessage));
                        break;
                    default:
                        throw new IllegalArgumentException("businessType is illegal !");
                }
            }
        }


        /**
         * 通过 {@link RenewReqMessage} 产生 cmp 证书续期的请求数据
         *
         * @param renewReqMessage {@link RenewReqMessage}
         * @return {@link PKIMessage}
         * @throws Exception e
         */
        private PKIMessage generateRenewalReqData(RenewReqMessage renewReqMessage) throws Exception {
            PKIHeader pkiHeader = getPkiHeader(renewReqMessage);
            PKIMessageGenerator.CertReqMessagesParams certReqMessagesParams = PKIMessageGenerator.CertReqMessagesParams.builder()
                    .certSn(renewReqMessage.getSerialNumber())
                    .issuer(renewReqMessage.getIssuer())
                    .certRequestId(renewReqMessage.getCertRequestId())
                    .validity(renewReqMessage.getValidity()).build();
            PKIBody pkiBody = pkiMessageGenerator.generateKurPKIBody(certReqMessagesParams);
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }


        /**
         * 通过 {@link ModifiedCertReqMessage} 产生 cmp 证书变更的请求数据
         *
         * @param modifiedCertReqMessage {@link ModifiedCertReqMessage}
         * @return {@link PKIMessage}
         * @throws Exception e
         */
        private PKIMessage generateModifiedReqData(ModifiedCertReqMessage modifiedCertReqMessage) throws Exception {
            PKIHeader pkiHeader = getPkiHeader(modifiedCertReqMessage);
            SubjectInfo subject = modifiedCertReqMessage.getSubject();
            Objects.requireNonNull(subject);
            PKIMessageGenerator.CertReqMessagesParams certReqMessagesParams = PKIMessageGenerator.CertReqMessagesParams.builder()
                    .certSn(modifiedCertReqMessage.getSerialNumber())
                    .subject(modifiedCertReqMessage.getSubject())
                    .issuer(modifiedCertReqMessage.getIssuer())
                    .certRequestId(modifiedCertReqMessage.getCertRequestId())
                    .validity(modifiedCertReqMessage.getValidity())
                    .build();
            PKIBody pkiBody = pkiMessageGenerator.generateKurPKIBody(certReqMessagesParams);
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }

        /**
         * 通过 {@link UpdateKeyReqMessage} 产生 cmp 密钥更新的请求数据
         *
         * @param updateKeyReqMessage {@link UpdateKeyReqMessage}
         * @return {@link PKIMessage}
         * @throws Exception e
         */
        public PKIMessage generateKeyUpdateReqData(UpdateKeyReqMessage updateKeyReqMessage) throws Exception {
            PKIHeader pkiHeader = getPkiHeader(updateKeyReqMessage);
            byte[] publicKey = updateKeyReqMessage.getPublicKey();
            Objects.requireNonNull(publicKey, "publicKey is not allowed to be null");
            PKIMessageGenerator.CertReqMessagesParams certReqMessagesParams = PKIMessageGenerator.CertReqMessagesParams.builder()
                    .subject(updateKeyReqMessage.getSubject())
                    .issuer(updateKeyReqMessage.getIssuer())
                    .validity(updateKeyReqMessage.getValidity())
                    .publicKey(updateKeyReqMessage.getPublicKey())
                    .privateKey(updateKeyReqMessage.getPrivateKey())
                    .certRequestId(updateKeyReqMessage.getCertRequestId())
                    .certSn(updateKeyReqMessage.getSerialNumber())
                    .extensionList(updateKeyReqMessage.getRequestExtensions())
                    .build();
            PKIBody pkiBody = pkiMessageGenerator.generateKurPKIBody(certReqMessagesParams);
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }


        private PKIHeader getPkiHeader(CmpMessage renewReqMessage) {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstance(renewReqMessage);
            return pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
        }

        private PKIHeader getPkiHeader(UpdateKeyReqMessage updateKeyReqMessage) {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams = PKIMessageGenerator.PKIHeaderParams.newInstanceBuilder(updateKeyReqMessage)
                    .customFreeText(updateKeyReqMessage.getCustomFreeText())
                    .build();
            return pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
        }

    }


    final static class KeyUpdateReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {

        public KeyUpdateReqMessageDecoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage decodedMsg) throws CmpDecodeException {
            checkDecodeArgs(context, decodedMsg);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                CmpMessage cmpReqMessage = singleCertReqContext.getCmpReqMessage();
                if ((cmpReqMessage instanceof KeyUpdateReqMessage || cmpReqMessage instanceof NestedMessage) &&
                        (BusinessTypeEnum.RENEWAL.equals(cmpReqMessage.getBusinessTypeEnum()) ||
                                BusinessTypeEnum.UPDATE_KEY.equals(cmpReqMessage.getBusinessTypeEnum()) ||
                                BusinessTypeEnum.MODIFICATION.equals(cmpReqMessage.getBusinessTypeEnum()))) {
                    try {
                        dealWithP10CertReqOrKeyUpdateRespPKIMessage(singleCertReqContext, decodedMsg);
                        singleCertReqContext.setDecoded(true);
                    } catch (Exception e) {
                        throw new CmpDecodeException(e.getMessage(), e);
                    }
                }
            }
        }

    }

}
