package net.netca.sdk.message.cmp;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * <AUTHOR>
 * @date 2019-06-28 13:01
 */
@SuperBuilder(toBuilder = true)
@Getter
public abstract class CmpMessage {


    /**
     * 业务类型（标识符）
     */
    private BusinessTypeEnum businessTypeEnum;

    /**
     * 发送者
     */
    private String senderStr;

    /**
     * 接受者
     */
    private String recipientStr;

    /**
     * @see PKIHeader
     * 协议版本
     * 默认值 {@link PKIHeader#CMP_2000}
     */
    private Integer pvno;

    /**
     * 是否设置 TransactionID (optional)
     */
    private boolean isSetTransactionID;


    /**
     * 是否设置 senderNonce(optional)
     */
    private boolean isSetSenderNonce;

    /**
     * The protectionAlg field specifies the algorithm used to protect the message. optional
     */
    private boolean unknownProtectionAlg;


    /**
     * CMP 证书续期|密钥更新|证书注销|证书挂失|证书解挂|轮询|确认 需要，必须保证同一CA下，是唯一的
     * 证书请求 Id
     */
    private Long certRequestId;


    /**
     * 是否推送 LDAP
     */
    private boolean isPublishLdap;

}
