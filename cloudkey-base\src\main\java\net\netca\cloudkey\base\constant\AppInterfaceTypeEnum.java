package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum AppInterfaceTypeEnum implements IBaseEnum {

    /**
     * 2000 - 3000 作为应用接口类型
     */
    BIND_FACE(2000, "绑定人脸数据", "/api/identity/facedata/bind"),
    BIND_DEVICE(2001, "绑定手机设备信息", "/api/identity/pin/bind"),
    BIND_FINGER(2002, "绑定手机设备指纹信息", "/api/identity/finger/bind"),
    BIND_FACE_DEVICE(2003, "绑定手机设备人脸信息", " /api/identity/face/bind"),

    MODIFY_USER_PIN(2004, "修改用户口令", "/api/base64/user/pin/change"),

    SELF_UNLOCK(2005, "自助解锁", "/api/selfunlock/certcontent/smsvalidcode/verify/2CKV3"),

    AUTH_APPLY(2006, "授权申请", "/authorizeduser/certcontent/authorized/apply/batch"),

    MODIFY_AUTH_VALIDITY(2007, "授权有效性修改", "/authorizeduser/authorized/validity/modify"),
    CHANGE_AUTH_STATUS(2008, "用户授权状态变更", "/authorizeduser/changestatus"),
    UPLOAD_SEAL_PIC(2009, "上传签章图片", "/seal/uploadsealpic"),


    SEAL_PDF(2010, "PDF同步签章", "/seal/pdfseal"),
    SEAL_PDF_WITH_OTHER_IMG(2011, "PDF签章-使用参数图片签章", "/seal/with/sealpic/1CKV13"),
    SEAL_PDF_VERIFY(2012, "PDF验证", "/seal/verify/1CKV13"),

    SELF_UNBIND_SEND_SMS_CODE(2013, "自助解绑-获取短信验证码", "/api/device/sms/code/2CKV16"),
    SELF_UNBIND(2014, "自助解绑-解绑设备", "/api/device/unbind/2CKV16"),

    SIGN_DATA(2015,"电子签名", "/pki/signdata"),

    PIN_LOGIN(2016,"登录认证-口令认证", "/user/certcontent/dologin"),
    PIN_SMS_LOGIN(2017,"登录认证-口令短信认证", "/user/certcontent/login/phonevalidcode"),

    AUTHORIZED_USER_PIN_LOGIN(2018,"登录认证-被授权用户口令认证", "authorizeduser/certcontent/pin/login"),

    AUTHORIZED_USER_PIN_SMS_LOGIN(2019,"登录认证-被授权用户口令短信认证", "authorizeduser/certcontent/smsvalidcode/login/verify"),
    QRCODE_PIN_LOGIN(2020,"扫码认证-口令认证", "api/base64/identity/pin/login"),
    QRCODE_FINGER_LOGIN(2021,"扫码认证-指纹认证", "api/base64/identity/finger/login"),
    QRCODE_FACE_LOGIN(2022,"扫码认证-刷脸认证", "api/certcontent/identity/facedata/login"),



    SEPARATING_KEY_BIND_SEND_SMS(2028, "绑定分割密钥-发送短信验证码", "api/identity/separatingKey/bind/smsvalidcode"),
    SEPARATING_KEY_BIND(2029, "绑定分割密钥-验证短信验证码", "api/identity/separatingKey/bind/"),
    SEPARATING_KEY_UNBIND_SEND_SMS(2030, "解绑分割密钥-发送短信验证码", "api/identity/separatingKey/unbind/smsvalidcode"),
    SEPARATING_KEY_UNBIND(2031, "解绑分割密钥-验证短信验证码", "api/identity/separatingKey/unbind/"),
    VERIFY_SIGNED_DATA_V2(2032, "验签接口V2", "/pki/verifysigndata/2CKV24"),
    SEPARATING_KEY_COSIGN_VERIFY(2033, "扫码认证-分割密钥认证", "api/identity/cosign/verify"),

    BIND_TENCENT_FACE(2034, "绑定人脸数据(腾讯云)", "/api/identity/facedata/tencent/bind"),

    TENCENT_FACE_LOGIN(2035, "人脸认证(腾讯云)", "/api/certcontent/identity/facedata/tencent/login"),


    SIGN_DATA_2CKV27(2036,"电子签名V2", "/pki/signdata/2CKV27"),

    SEAL_PDF_2CKV27(2037, "PDF同步签章V2", "/seal/pdfseal/2CKV27"),

    CORP_WECHAT_BIND(2040, "企业微信绑定", "/wx/corp/user/bind/2CKV29"),
    CORP_WECHAT_LOGIN(2041, "企业微信登录", "/wx/corp/user/unbind/2CKV29"),
    ;

    private Integer code;
    private String description;
    private String uri;

    AppInterfaceTypeEnum(Integer code, String description, String uri) {
        this.code = code;
        this.description = description;
        this.uri = uri;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public String getUri() {
        return uri;
    }
}
