package net.netca.cloudkey.base.dto.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;
import java.util.List;

@Data
@Secret
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserStatisticsVO {

    private String orgName;
    private Integer userId;
    private String userUid;
    private String userName;
    private String operatorName;
    private Integer status;
    private String department;
    private String occupation;
    private Integer identityType;
    private Integer districtId;
    @SecurityDataField
    private String identity;
    @SecurityDataField
    private String phone;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityStart;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityEnd;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastOperatedTime;
    @JsonIgnore
    private Integer certType;
    @JsonIgnore
    private byte[] signCert;
    @JsonIgnore
    private byte[] encCert;

    private String signCertSn;

}
