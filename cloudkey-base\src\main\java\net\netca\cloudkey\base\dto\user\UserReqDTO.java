package net.netca.cloudkey.base.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.constraints.annotation.*;
import net.netca.cloudkey.base.constraints.annotation.Email;
import net.netca.cloudkey.base.constraints.group.IGroupApiEmployeeUser;
import net.netca.cloudkey.base.constraints.group.IGroupUser2Self;
import net.netca.cloudkey.base.constraints.group.IGroupWebEmployeeUser;
import net.netca.cloudkey.base.constraints.group.IGroupWebPersonUser;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;
import net.netca.cloudkey.base.dto.abstractclass.IAreaReqDTO;
import net.netca.cloudkey.base.dto.abstractclass.IIdentityReqDTO;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Area
public class UserReqDTO implements ISignature, IAreaReqDTO {

    @NotBlank(message = "UID不能为空")
    @Size(min = 1, max = 128, message = "用户唯一标识的长度需要在1 - 128个字之间 ")
    private String uid;

    /**
     * 可选地允许修改新唯一标识
     */
    private String uidAlias;

    @NotBlank(message = "名称不能为空")
    private String name;

    @Range(min = 0, max = 2)
    @NotNull(message = "性别不能为空")
    @Gender
    private Integer gender;

    @NotBlank(message = "手机号码不能为空")
    @MobilePhone
    private String phone;

    @Email
    private String email;

    private String occupation;

    @NotBlank(groups = {IGroupWebEmployeeUser.class, IGroupWebPersonUser.class})
    private String cloudkeyProjectId;

    @NotBlank(message = "国家不能为空")
    @Size(max = 32, message = "国家代码长度不超过32个字符")
    private String countryName;

    @NotBlank(message = "省份不能为空")
    @Size(max = 64, message = "省份长度不超过64个字符")
    private String province;

    @Size(max = 64, message = "城市长度不超过64个字符")
    @NotBlank(message = "城市不能为空",groups = IGroupUser2Self.class)
    private String city;

    private String officialResidence;

    private String department;

    @NotNull(message = "关联机构不能为空", groups = {IGroupApiEmployeeUser.class, IGroupWebEmployeeUser.class})
    private Integer organizationId;

    @NotNull(message = "证件类型不能为空")
    private Integer identityType;

    @NotBlank(message = "证件号码不能为空")
    private String identity;

    private String sealPic;

    @Future(message = "certValidityEnd 必须为将来的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date certValidityEnd;

    private Integer districtId;

    /**
     * 用户拓展字段，可选
     */
    private List<UserExtFieldInfoDTO> userExtFieldInfos;

    private transient OperatorSignature operatorSignature;

}
