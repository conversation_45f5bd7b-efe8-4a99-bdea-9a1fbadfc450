package net.netca.cloudkey.base.dto.config.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 返回待修改对象DTO
 * @Author: dyr
 * @CreateDate: 2020/8/12 10:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessRespDTO {

    /**
     * 表的自增主键
     */
    private Integer id;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 操作顺序
     */
    private String[] operationSequence;

    /**
     * 流程对应平台
     */
    private String processPlatform;

}
