package net.netca.cloudkey.base.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Description 偏移方式的分页查询返回DTO，非常规的查询，每次都是根据上一次的最大或最小值进行查询。
 * @Date Created in 8:45 2024/9/20
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ShiftPageRespDTO extends DatagridResp{
    /**
     * 当前页最大的itemIndex
     */
    private Long maxItemIndex;
    /**
     * 当前页最小的itemIndex
     */
    private Long minItemIndex;

    public ShiftPageRespDTO(long total, List<?> rows, Long maxItemIndex, Long minItemIndex) {
        super(total, rows);
        this.maxItemIndex = maxItemIndex;
        this.minItemIndex = minItemIndex;
    }
}
