package net.netca.cloudkey.api.webservice;

import com.google.common.base.Preconditions;
import lombok.extern.apachecommons.CommonsLog;
import net.netca.cloudkey.base.constant.UserTypeConstant;
import net.netca.cloudkey.base.dto.common.ImgValidCodeRespDTO;
import net.netca.cloudkey.base.dto.superclass.UserToken;
import net.netca.cloudkey.base.dto.common.SmsValidCodeRespDTO;
import net.netca.cloudkey.base.dto.user.RegisterUserReqDTO;
import net.netca.cloudkey.base.exception.CloudKeyException;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.exception.CodecFailException;
import net.netca.cloudkey.base.manager.IUserKeypairInfoManager;
import net.netca.cloudkey.base.util.CodecUtil;
import net.netca.cloudkey.base.util.CommonUtil;
import net.netca.cloudkey.identity.authentication.LoginTypeBitValue;
import net.netca.cloudkey.identity.authentication.req.AuthorizedUserPINLoginBasicReq;
import net.netca.cloudkey.identity.authentication.req.AuthorizedUserPhoneValidCodeLoginReq;
import net.netca.cloudkey.identity.authentication.req.CorpWechatLoginReq;
import net.netca.cloudkey.identity.authentication.res.CorpWechatResult;
import net.netca.cloudkey.identity.authentication.res.DoLoginResult;
import net.netca.cloudkey.identity.authentication.res.QRCodeResult;
import net.netca.cloudkey.identity.authentication.res.UserTokenResult;
import net.netca.cloudkey.identity.constant.MobileValidCodeUsageTypeEnum;
import net.netca.cloudkey.identity.constant.PublicKeyRegisterType;
import net.netca.cloudkey.identity.dto.*;
import net.netca.cloudkey.identity.manager.UserScanQRCodeCache;
import net.netca.cloudkey.identity.po.UserMobilePublickey;
import net.netca.cloudkey.identity.service.ImgValidCodeService;
import net.netca.cloudkey.identity.webservice.IdentityAuthWebService;
import net.netca.cloudkey.identity.webservice.IdentityBindWebService;
import net.netca.cloudkey.identity.webservice.ValidCodeWebService;
import net.netca.cloudkey.pki.device.KeyPairManagerFactory;
import net.netca.cloudkey.pki.util.SignUtil;
import net.netca.pki.PkiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.security.interfaces.RSAPublicKey;

@Service
@CommonsLog
public class IdentityWebService {

    @Autowired
    private IdentityAuthWebService identityAuthWebService;

    @Autowired
    private ValidCodeWebService validCodeWebService;

    @Autowired
    private ImgValidCodeService imgValidCodeService;

    @Autowired
    private IdentityBindWebService identityBindWebService;

    /**################################身份认证相关####################################*/

    /**
     * 获取身份认证的二维码
     *
     * @param loginQRCodeReq
     * @return
     */
    public QRCodeResult genLoginQRCode(LoginQRCodeReqDTO loginQRCodeReq) throws CloudKeyException {
        return identityAuthWebService.genLoginQRCode(loginQRCodeReq);
    }

    public UserTokenResult qrCodeLoginByPinAndSeparatingKey(QRCodeSeparatingKeyLoginReqDTO qrCodeSeparatingKeyLoginReqDTO) throws CloudKeyException{
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.qrCodeLoginByPinAndSeparatingKey(qrCodeSeparatingKeyLoginReqDTO, userKeypairInfoManager);
    }

    public UserTokenResult loginByTencentFace(TencentFaceLoginReqDTO tencentFaceLoginReqDTO){
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.loginByTencentFace(tencentFaceLoginReqDTO, userKeypairInfoManager);
    }

    /**
     * 获取无状态身份认证的二维码
     *
     * @param loginQRCodeNoStatusReqDTO
     * @return
     */
    public QRCodeNoStatusRespDTO genLoginQRCodeNoStatus(LoginQRCodeNoStatusReqDTO loginQRCodeNoStatusReqDTO) {
        return identityAuthWebService.genLoginQRCodeNoStatus(loginQRCodeNoStatusReqDTO);
    }

    /**
     * 绑定认证二维码的状态
     *
     * @param bindLoginQRCodeStatusReqDTO
     * @return {@link LoginTypeBitValue}
     */
    public int bindLoginQRCodeStatus(BindLoginQRCodeStatusReqDTO bindLoginQRCodeStatusReqDTO) {
        return identityAuthWebService.bindLoginQRCodeStatus(bindLoginQRCodeStatusReqDTO);
    }

    public void loginPublicKey(Integer type, MobileData data) {
        MobileTbs mobileTbs = CommonUtil.parseObject(data.getTbs(), MobileTbs.class);
        // 获取二维码随机数
        String qrCodeIdentity = mobileTbs.getRaw();
        Preconditions.checkArgument(!CommonUtil.isStringEmpty(qrCodeIdentity), "raw is empty...");
        // 从随机数中获取用户登录的证书id
        String cacheKey = CodecUtil.getKeyFromQRCodeIdentity(qrCodeIdentity);
        Integer certId = CodecUtil.getCertIdFromCacheKey(cacheKey);
        Preconditions.checkNotNull(certId, "获取不到用户的证书信息");
        UserMobilePublickey userMobilePublickey = identityAuthWebService.loginPublicKey(qrCodeIdentity, cacheKey, certId);

        this.verify(userMobilePublickey.getAuthkeyPubKey(), data.getTbs(), data.getTbs_signature(), type, data.getTbs_signature_algo_oid());
    }


    /**
     * 证书用户登录
     * 废弃，为了兼容之前云密钥一期开发给客户端的接口
     * 原因1：接口定义不合理，没有尽到单一职责。
     * 原因2：需求变更，新增了证书被授权用户
     *
     * @param doLogin
     * @return
     */
    public DoLoginResult doLogin(DoLoginReqDTO doLogin, String pinSM3Hash) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.doLogin(pinSM3Hash, doLogin, userKeypairInfoManager);
    }

    /**
     * 用户验证码登录
     * 检验短信验证码
     *
     * @param doLogin
     * @return
     */
    public DoLoginResult verifyPhoneValidCode(DoLoginReqDTO doLogin) {
        return identityAuthWebService.verifyPhoneValidCode(doLogin);
    }

    /**
     * 被授权用户PIN码登录
     *
     * @param pinLoginBasicReq
     * @return
     * @throws PkiException
     * @throws CloudKeyException
     */
    public DoLoginResult loginByPIN(AuthorizedUserPINLoginBasicReq pinLoginBasicReq, String pinSM3Hash) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.loginByPIN(pinSM3Hash, pinLoginBasicReq, userKeypairInfoManager);
    }

    /**
     * 企业微信登录
     *
     * @param req 请求dto
     * @since V2.29
     */
    public DoLoginResult loginByCorpWechatUserInfoAndPin(CorpWechatLoginReq req, String pinSM3Hash) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.loginByCorpWechatUserInfoAndPin(pinSM3Hash, req, userKeypairInfoManager);
    }

    /**
     * 被授权人PIN + 手机验证码
     *
     * @param pinLoginBasicReq
     * @return
     */
    public DoLoginResult loginByPINAndSMSValidCode(AuthorizedUserPINLoginBasicReq pinLoginBasicReq, String pinSM3Hash) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.loginByPINAndSMSValidCode(pinSM3Hash, pinLoginBasicReq, userKeypairInfoManager);
    }

    /**
     * 被授权人检验短信验证码
     *
     * @param pinLoginPhoneValidCodeReq
     * @return
     */
    public DoLoginResult verifyPhoneValidCode(AuthorizedUserPhoneValidCodeLoginReq pinLoginPhoneValidCodeReq) {
        return identityAuthWebService.verifyPhoneValidCode(pinLoginPhoneValidCodeReq);
    }

    /**
     * 用户二维码刷脸登录
     *
     * @param qrCodeFaceDataLogin
     * @return
     */
    public UserTokenResult qrCodeLoginByFaceData(QRCodeFaceDataLoginReqDTO qrCodeFaceDataLogin) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.qrCodeLoginByFaceData(qrCodeFaceDataLogin, userKeypairInfoManager);
    }

    /**
     * 手机提交设备口令身份认证
     *
     * @param qrCodePINLoginReq
     * @return
     */
    public UserTokenResult qrCodeLoginByPINAndDevice(QRCodePINLoginReqDTO qrCodePINLoginReq, String pinSM3Hash) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.qrCodeLoginByPINAndDevice(pinSM3Hash, qrCodePINLoginReq, userKeypairInfoManager);
    }

    /**
     * 指纹验证
     * 1.验证缓存
     * 2.验证签名
     * 3.验证口令
     *
     * @param qrCodeFingerLoginReq
     * @return
     */
    public UserTokenResult qrCodeLoginByFingerprintAndDevice(QRCodeFingerLoginReqDTO qrCodeFingerLoginReq) {
        MobileData data = qrCodeFingerLoginReq.getData();
        String deviceId = qrCodeFingerLoginReq.getDeviceId();
        Integer type = qrCodeFingerLoginReq.getType();
        String userPin = qrCodeFingerLoginReq.getUserPin();
        Preconditions.checkNotNull(deviceId, "参数deviceId不能为空...");
        Preconditions.checkNotNull(data, "参数data不能为空...");
        Preconditions.checkNotNull(userPin, "参数userPin不能为空...");
        Preconditions.checkNotNull(type, "参数type不能为空...");

        MobileTbs mobileTbs = CommonUtil.parseObject(data.getTbs(), MobileTbs.class);
        // 验证缓存
        String qrCodeIdentity = mobileTbs.getRaw();
        Preconditions.checkArgument(!CommonUtil.isStringEmpty(qrCodeIdentity), "raw is empty...");

        // 1.获取缓存 检查类型
        UserScanQRCodeCache userScanQRCodeCache = identityAuthWebService.getUnVerifyUserScanQRCodeCache(qrCodeIdentity);
        userScanQRCodeCache.checkQRCodeTypeBitValue(LoginTypeBitValue.PIN_FINGER_DEVICEID);
        // 2.验证设备签名（TODO 考虑将这一块逻辑迁移到具体用户那里实现）
        UserMobilePublickey userMobilePublickey = identityAuthWebService.getUserMobilePublickey(deviceId, userScanQRCodeCache);
        verify(userMobilePublickey.getAuthkeyPubKey(), data.getTbs(), data.getTbs_signature(), type, data.getTbs_signature_algo_oid());

        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.qrCodeLoginByFingerprintAndDevice(qrCodeFingerLoginReq, qrCodeIdentity, userScanQRCodeCache, userKeypairInfoManager);
    }

    /**
     * 手机提交口令设备人脸身份认证
     *
     * @param qrCodeFaceLoginReq
     * @return
     */
    public UserTokenResult qrCodeLoginByFaceAndDevice(QRCodeFaceLoginReqDTO qrCodeFaceLoginReq) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        return identityAuthWebService.qrCodeLoginByFaceAndDevice(qrCodeFaceLoginReq, userKeypairInfoManager);
    }

    /**
     * 通过设备获取退出令牌
     *
     * @param getUserTokenDevice
     * @return
     */
    public LogoutTokenRespDTO getLogoutUserTokenByDevice(GetUserTokenDeviceReqDTO getUserTokenDevice) {
        return identityAuthWebService.getLogoutUserTokenByDevice(getUserTokenDevice);
    }

    /**
     * 人脸认证获取退出令牌
     *
     * @param getUserTokenFaceData
     * @return
     */
    public LogoutTokenRespDTO getLogoutUserTokenByFaceData(GetUserTokenFaceDataReqDTO getUserTokenFaceData) {
        return identityAuthWebService.getLogoutUserTokenByFaceData(getUserTokenFaceData);
    }

    /**
     * 退出所有应用
     *
     * @param logout
     */
    public void logoutByUserToken(LogoutReqDTO logout) {
        identityAuthWebService.logoutByUserToken(logout);
    }

    /**
     * 退出指定应用
     *
     * @param logout
     */
    public void logoutByLoginUuid(LogoutReqDTO logout) {
        identityAuthWebService.logoutByLoginUuid(logout);
    }

    /**
     * 根据认证二维码获取用户
     *
     * @param qrCodeIdentity
     * @return
     */
    public UserUidRespDTO getUserUidByLoginQRCodeIdentity(String qrCodeIdentity) {
        return identityAuthWebService.getUserUidByLoginQRCodeIdentity(qrCodeIdentity);
    }

    /**
     * 获取证书所支持的身份认证类型
     * loginTypeBitValue 值
     *
     * @param certDTO 证书信息
     * @return loginTypeBitValue
     */
    public int getUserLoginTypeBitValue(CertReqDTO certDTO) {
        return identityAuthWebService.getUserLoginTypeBitValue(certDTO);
    }

    public ProjectInfoSearchRespDTO getProjectUid(CertReqDTO certDTO) {
        return identityAuthWebService.getProjectUid(certDTO);
    }

    public ResUserQRCodeVerifyStatusRespDTO pollingQRCodeStatus(PollingQRCodeStatusReqDTO pollingQRCodeStatus) {
        return identityAuthWebService.pollingQRCodeStatus(pollingQRCodeStatus);
    }

    /**
     * 获取用户已经支持（开通）的身份验证类型
     *
     * @param selectRegistedLoginType
     * @return
     */
    public int selectUserRegistedLoginType(SelectRegistedLoginTypeReqDTO selectRegistedLoginType) {
        return identityAuthWebService.selectUserRegistedLoginType(selectRegistedLoginType);
    }

    /**
     * 查询token状态
     *
     * @param userToken
     * @return
     */
    public UserTokenStatusRespDTO getUserTokenStatus(UserToken userToken) {

        return identityAuthWebService.getUserTokenStatus(userToken);
    }

    /**
     * 查询登陆者信息
     *
     * @param userToken
     * @return
     */
    public OprInfoRespDTO searchOprInfo(UserToken userToken) {
        return identityAuthWebService.searchOprInfo(userToken);
    }

    /**
     * 查询登陆者信息
     *
     * @param userToken
     * @return
     */
    public OprInfoRespDTO searchOprAllInfo(UserToken userToken) {
        return identityAuthWebService.searchOprAllInfo(userToken);
    }

    /**
     * 查询登陆信息
     *
     * @param token
     * @return
     */
    public LoginInfoDatagripRespDTO searchLoginInfo(TokenReqDTO token) {
        return identityAuthWebService.searchLoginInfo(token);
    }

    public ImgValidCodeRespDTO generateImgValidCode() {
        return imgValidCodeService.generateImgValidCode();
    }

    /**
     * 发送验证码
     * 包括证书用户和被授权用户，用于除登录、绑定发送验证码之外的用途，目前有解锁
     *
     * @param certId
     * @param authorizedUserId
     * @param validCode
     * @param usageType
     * @param userTypeConstant
     * @return {@link SmsValidCodeRespDTO}
     */
    public SmsValidCodeRespDTO sendValidCode(String phone, Integer certId, Integer authorizedUserId, String validCode,
                                             MobileValidCodeUsageTypeEnum usageType, UserTypeConstant userTypeConstant,
                                             String imgValidCodeIdentity, String imgValidCode) {
        return validCodeWebService.sendValidCode(phone, certId, authorizedUserId, validCode, usageType, userTypeConstant, imgValidCodeIdentity, imgValidCode);
    }

    /**
     * 发送验证码，与上一个接口相对应
     * 包括证书用户和被授权用户，用于除登录、绑定发送验证码之外的用途，目前有解锁
     *
     * @param certId
     * @param authorizedUserId
     * @param validCode
     * @param userTypeConstant
     */
    public boolean verifyValidCode(Integer certId, Integer authorizedUserId, String validCode, UserTypeConstant userTypeConstant, MobileValidCodeUsageTypeEnum usageType) {
        return validCodeWebService.verifyValidCode(certId, authorizedUserId, validCode, null, userTypeConstant, usageType);
    }

    /**
     * 发送验证码
     * 用于自助申请
     *
     * @param validCode 验证码
     * @param usageType 使用类型
     * @return {@link SmsValidCodeRespDTO}
     */
    public SmsValidCodeRespDTO sendValidCode(String phone, String validCode, MobileValidCodeUsageTypeEnum usageType,
                                             RegisterUserReqDTO registerUserReqDTO) {
        return validCodeWebService.sendValidCode(phone, validCode, usageType, registerUserReqDTO);
    }

    /**
     * 验证验证码，用于自助申请，与上一个接口相对应
     *
     * @param phone     手机号
     * @param validCode 验证码
     * @return boolean
     */
    public boolean verifyValidCode(String phone, String validCode) {
        return validCodeWebService.verifyValidCode(validCode, phone, MobileValidCodeUsageTypeEnum.SELF_REGISTER_VALID_CODE);
    }

    /**##################################身份认证相关##################################*/


    /**##################################身份绑定相关##################################*/
    /**
     * 根据绑定设备的二维码标识 查询用户信息
     *
     * @param qrCodeIdentity
     * @return
     */
    public CommonUserInfoRespDTO getUserInfoByRegisterQRCodeIdentity(String qrCodeIdentity) {
        return identityBindWebService.getUserInfoByRegisterQRCodeIdentity(qrCodeIdentity);
    }

    /**
     * 获取支持绑定的身份认证类型
     *
     * @param certDTO
     * @return
     */
    public int getUserDeviceRegisterTypeBitValue(CertReqDTO certDTO) {
        return identityBindWebService.getUserDeviceRegisterTypeBitValue(certDTO);
    }

    /**
     * 获取绑定需要的二维码
     *
     * @param bindDeviceQRCode
     * @return
     */
    public QRCodeResult genBindDeviceQRCode(BindDeviceQRCodeReqDTO bindDeviceQRCode) throws CloudKeyException {
        return identityBindWebService.genBindDeviceQRCode(bindDeviceQRCode);
    }

    /**
     * 检查用户设备是否已绑定
     *
     * @param qrCodeDeviceInfo
     * @return
     */
    public RegistStatusRespDTO checkUserRegistStatus(QRCodeDeviceInfoReqDTO qrCodeDeviceInfo) {
        return identityBindWebService.checkUserRegistStatus(qrCodeDeviceInfo);
    }

    /**
     * 检查手机设备是否能进行身份认证
     *
     * @param qrCodeDeviceInfo
     * @return
     */
    public CheckCanLoginStatusRespDTO checkUserDeviceInfoStatus(QRCodeDeviceInfoReqDTO qrCodeDeviceInfo) {
        return identityBindWebService.checkUserDeviceInfoStatus(qrCodeDeviceInfo);
    }

    /**
     * 绑定人脸数据
     *
     * @param bindFaceInfoData
     */
    public void bindFaceInfoData(BindFaceInfoDataReqDTO bindFaceInfoData) {
        identityBindWebService.bindFaceInfoData(bindFaceInfoData);
    }

    public void bindTencentFace(TencentFaceBindReqDTO tencentFaceBindReqDTO) {
        IUserKeypairInfoManager userKeypairInfoManager = KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();
        identityBindWebService.bindTencentFace(tencentFaceBindReqDTO, userKeypairInfoManager);
    }

    /**
     * 绑定手机设备信息
     *
     * @param bindDeviceInfo
     */
    public void bindDeviceForPINLogin(BindDeviceInfoReqDTO bindDeviceInfo) {
        identityBindWebService.bindDeviceForPINLogin(bindDeviceInfo);
    }

    /**
     * 绑定手机设备指纹信息（同时也会绑定设备）
     * 也就是说，开通指纹的同时，也会开通设备+口令方式的身份验证
     *
     * @param publicKeyRegister
     */
    public void bindFingerprintAndDevice(PublicKeyRegisterReqDTO publicKeyRegister) {
        Preconditions.checkNotNull(publicKeyRegister, "参数publicKeyRegister不能为空...");
        String deviceId = publicKeyRegister.getDeviceId();
        Preconditions.checkNotNull(deviceId, "参数deviceId不能为空...");

        PublicKeyJson authkey = publicKeyRegister.getAuthkey();
        Preconditions.checkNotNull(authkey, "Authkey must not be null");
        UserMobilePublickey userMobilePublickey = new UserMobilePublickey();
        userMobilePublickey.setType(publicKeyRegister.getType());

        // 最终签名数据
        @NotNull(message = "data不能为空") MobileData data = publicKeyRegister.getData();
        MobileTbs mobileTbs = CommonUtil.parseObject(data.getTbs(), MobileTbs.class);
        KeyJson authkeyKeyJson = CommonUtil.parseObject(authkey.getKeyjson(), KeyJson.class);
        this.verify(authkeyKeyJson.getPub_key(), data.getTbs(), data.getTbs_signature(), publicKeyRegister.getType(), data.getTbs_signature_algo_oid());
        userMobilePublickey.setAuthkeyPubKey(authkeyKeyJson.getPub_key());

        identityBindWebService.bindFingerprintAndDevice(publicKeyRegister, userMobilePublickey, mobileTbs);
    }

    /**
     * *
     * 绑定设备信息+人脸信息
     * (同时也会开通绑定设备信息)
     *
     * @param bindDeviceFaceInfo
     */
    public void bindFaceAndDevice(BindDeviceFaceInfoReqDTO bindDeviceFaceInfo) {
        identityBindWebService.bindFaceAndDevice(bindDeviceFaceInfo);
    }

    public Integer registerPublicKey(PublicKeyRegisterReqDTO publicKeyRegisterDTO) {
        Preconditions.checkNotNull(publicKeyRegisterDTO.getAuthkey(), "Authkey must not be null");
        UserMobilePublickey userMobilePublickey = new UserMobilePublickey();
        userMobilePublickey.setType(publicKeyRegisterDTO.getType());

        // 最终签名数据
        MobileTbs mobileTbs = CommonUtil.parseObject(publicKeyRegisterDTO.getData().getTbs(), MobileTbs.class);

        PublicKeyJson authkey = publicKeyRegisterDTO.getAuthkey();
        @NotNull(message = "data不能为空") MobileData data = publicKeyRegisterDTO.getData();
        KeyJson authkeyKeyJson = CommonUtil.parseObject(authkey.getKeyjson(), KeyJson.class);
        this.verify(authkeyKeyJson.getPub_key(), data.getTbs(), data.getTbs_signature(), publicKeyRegisterDTO.getType(), data.getTbs_signature_algo_oid());
        userMobilePublickey.setAuthkeyPubKey(authkeyKeyJson.getPub_key());

        return identityBindWebService.registerPublicKey(mobileTbs, userMobilePublickey);
    }

    /**##################################身份绑定相关##################################*/

    /**
     * 验证签名
     *
     * @param pubKye
     * @param data
     * @param signature
     * @param type
     * @see PublicKeyRegisterType
     */
    private void verify(String pubKye, String data, String signature, Integer type, String signAlgoOid) {
        log.info("签证签名开始：原文=" + data);
        log.info("签证签名开始：签名值=" + signature);
        log.info("签证签名开始：公钥=" + pubKye);
        // 验证签名
        RSAPublicKey rsaPublicKey = SignUtil.loadRsaPublicKey(pubKye);
        boolean verify = false;
        try {
            byte[] bytes = data.getBytes("UTF-8");
            if (PublicKeyRegisterType.GOOGLE.getCode().equals(type)) {
                verify = SignUtil.verifyP1ByNetca(rsaPublicKey, bytes, CodecUtil.base64DecodeStr(signature), signAlgoOid);
            } else {
                throw new CloudKeyRuntimeException(String.format("暂不支持的type=%d", type));
            }
        } catch (UnsupportedEncodingException e) {
            log.debug(e.getMessage(), e);
            throw new CloudKeyRuntimeException("utf-8编码错误");
        } catch (CodecFailException e) {
            log.debug(e.getMessage(), e);
            log.error(e.getMessage());
            throw new CloudKeyRuntimeException("请使用绑定的手机进行扫码验证");
        }
        if (!verify) {
            log.error("验证签名失败,转换异常信息提示用户");
            throw new CloudKeyRuntimeException("请使用绑定的手机进行扫码验证");
        }
    }
}
