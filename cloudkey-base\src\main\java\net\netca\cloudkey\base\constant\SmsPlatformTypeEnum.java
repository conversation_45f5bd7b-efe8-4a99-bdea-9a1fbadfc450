package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 短信平台枚举常量
 * PS 如果 net.netca.common.notice.constants.SupportSmsTypeEnum 有改动，这里也要进行修改
 * 正则替换:
 * (.*)\(.*"(.*)"\),
 * $1\("$1", "$2"\),
 *
 * @since V2.18
 */
@Getter
@AllArgsConstructor
public enum SmsPlatformTypeEnum implements IBaseEnum {
    // 默认值，允许短信平台为空
    DEFAULT("", "无"),
    ALIYUN("ALIYUN", "阿里云"), // 阿里云和189放前面
    OPEN189("OPEN189", "OPEN189"),
    BDSZ("BDSZ", "北大深圳医院短信平台"),
    CTC("CTC", "深圳市妇幼保健院"),
    EUREKA("EUREKA", "广州医科大学附属第一医院"),
    MAS10086("MAS10086", "番禺中心医院"),
    QYDX53API("QYDX53API", "北京中医药大学深圳医院"),
    OPENSMGP189("OPENSMGP189", "连州区卫"),
    UMS("UMS", "中山附属第一医院"),
    YXT("YXT", "龙岗人民医院"),
    JIAXU("JIAXU", "广东省第二人民医院"),
    GD2H("GD2H", "广东省第二人民医院-V2"),
    WSF("WSF", "福永人民医院"),
    STUNIV("STUNIV", "汕大附一"),
    MAILUN("MAILUN", "暨南大学附属第一医院"),
    DUANXIN10086("DUANXIN10086", "广药附一"),
    QIXINTONG("QIXINTONG", "汕头市中心医院"),
    NANFANGYY("NANFANGYY", "南方医科大学南方医院"),
    NANFANGYYKJY("NANFANGYYKJY", "南方医科大学南方医院-快加易短信"),
    GXYKDFY("GXYKDFY", "广西医科大附一"),
    MCWX("MCWX", "中山三院"),
    SZEY("SZEY", "深圳市第二人民医院"),
    ZHFY("ZHFY", "珠海市妇幼保健院"),
    MASJASSON("MASJASSON", "中山大学附属第六医院"),
    WUYI("WUYI", "五邑中医院"),
    SYSUCC("SYSUCC", "中山大学肿瘤防治中心"),
    DGKH("DGKH", "东莞康华医院"),
    HYMH("HYMH", "和祐美和医院"),
    GZBRAIN("GZBRAIN","惠爱医院"),
    GDSDYY("GDSDYY","顺德中医院"),
    DGP("DGP","东莞市人民医院"),
    SDWS("SDWS","汕头大学精神卫生中心"),
    DPXQKY("DPXQKY","深圳市大鹏新区葵涌人民医院"),
    NANFANGYYESB("NANFANGYYESB", "南方医科大学南方医院-ESB服务"),
    SUMCCH("SUMCCH", "汕头大学医学院附属肿瘤医院"),
    GZHOSP("GZHOSP", "广州市第一人民医院"),
    CONSOLE("CONSOLE", "控制台"),
    ZDFYNS("ZDFYNS", "中大附一南沙医院"),
    JMSZXYY("JMSZXYY", "江门市中心医院"),
    NETCA("NETCA", "网证通"),
    SDRMYY("SDRMYY", "南方医科大学顺德医院"),
    ;

    private String code;
    private String description;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
