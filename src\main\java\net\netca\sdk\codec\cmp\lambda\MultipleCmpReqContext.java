package net.netca.sdk.codec.cmp.lambda;

import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zys
 * @date: 2020/3/5 11:22
 */
public class MultipleCmpReqContext extends CmpRequestContext {


    private List<SingleCertReqContext> contexts;


    public MultipleCmpReqContext(CmpMessageConfigManagement cmpMessageConfigManagement) {
        super(cmpMessageConfigManagement);
        this.contexts = new ArrayList<>();
    }


    public List<SingleCertReqContext> getContexts() {
        return contexts;
    }


    public List<CmpRespResult> getCmpRespResults() {
        if (contexts.isEmpty()) {
            return null;
        }
        return contexts.stream()
                .filter(SingleCertReqContext::isDecoded)
                .map(SingleCertReqContext::getCmpRespResult)
                .collect(Collectors.toList());
    }

    public PKIMessages getPKIMessages() {
        if (contexts.isEmpty()) {
            return null;
        }
        PKIMessage[] pkiMessages = contexts.stream()
                .filter(SingleCertReqContext::isEncoded)
                .map(SingleCertReqContext::getPkiMessage).toArray(PKIMessage[]::new);
        return new PKIMessages(pkiMessages);
    }
}
