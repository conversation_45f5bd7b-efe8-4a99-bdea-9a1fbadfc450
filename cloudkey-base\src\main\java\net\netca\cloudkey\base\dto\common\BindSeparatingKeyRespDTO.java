package net.netca.cloudkey.base.dto.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindSeparatingKeyRespDTO {

    /**
     * 主密钥公钥加密的Base64编码的用户私钥分块d1
     */
    private String d1;

    /**
     * 用户公钥分量p2
     */
    private String p2;

    /**
     * 主密钥id
     */
    private String signKeypairId;
}
