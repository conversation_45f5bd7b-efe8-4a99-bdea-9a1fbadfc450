package net.netca.cloudkey.base.dto.thirdsystem;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class ThirdSystemLogPageSearchDTO {

    /**
     * 请求地址
     */
    private String url;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 响应状态
     *
     * @see net.netca.cloudkey.base.constant.WebResultEnum
     */
    private Integer success;

    /**
     * 接口调用方向
     *
     * @see net.netca.cloudkey.base.constant.InvokeDirectionEnum
     */
    private Integer direction;

    /**
     * 第三方系统权限编号
     */
    private String authId;

    /**
     * 最小耗时时长，单位：毫秒
     */
    private Long minDuration;

    /**
     * 最大耗时时长，单位：毫秒
     */
    private Long maxDuration;

    /**
     * 院区ID
     */
    private Integer districtId;

    /**
     * 类型
     */
    private Integer type;

}
