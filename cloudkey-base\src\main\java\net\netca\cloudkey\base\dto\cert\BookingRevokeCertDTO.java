package net.netca.cloudkey.base.dto.cert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-11-15 11:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BookingRevokeCertDTO extends RevokeCertDTO {
    @NotNull
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date reserveTime;
}
