package net.netca.cloudkey.base.dto.seal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @descriptions:
 * PDF签章时信息
 * @date: 2019/6/22
 * @author: SaromChars
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(builderMethodName = "sealInfoBuilder")
public class SealInfo {

    private String location;

    private String reason;

    private String signerName;

    private String fieldName;
}
