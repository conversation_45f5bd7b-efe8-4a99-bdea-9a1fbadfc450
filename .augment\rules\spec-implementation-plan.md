---
type: "manual"
---

### 3. Create Implementation Task List

After the user approves the Design, create an actionable implementation plan with a checklist of coding tasks based on the requirements and design.

**Constraints:**

- The model MUST use the `view` tool to check if '.kiro/specs/{feature_name}/design.md' file exists before proceeding
- The model MUST use the `save-file` tool to create '.kiro/specs/{feature_name}/tasks.md' file if it doesn't already exist
- The model MUST return to the design step if the user indicates any changes are needed to the design
- The model MUST return to the requirements step if the user indicates that additional requirements are needed
- The model MUST convert the feature design into a series of actionable coding tasks following these principles:
  - Prioritize best practices, incremental progress, and early testing
  - Ensure no big jumps in complexity at any stage
  - Make sure each task builds on previous tasks
  - End with integration and wiring components together
  - Ensure no hanging or orphaned code that isn't integrated
  - Focus ONLY on tasks that involve writing, modifying, or testing code
- The model MUST format the implementation plan as a numbered checkbox list with maximum two levels of hierarchy:
  - Top-level items should be used only when needed for grouping
  - Sub-tasks should be numbered with decimal notation (e.g., 1.1, 1.2, 2.1)
  - Each item must be a checkbox format: `- [ ] Task description`
  - Simple structure is preferred over complex hierarchies
- The model MUST ensure each task item includes:
  - A clear objective involving writing, modifying, or testing code
  - Additional implementation details as sub-bullets under the task
  - Specific references to requirements from the requirements document (referencing granular sub-requirements)
- The model MUST ensure tasks are discrete, manageable coding steps that can be executed by a coding agent
- The model MUST ensure each task specifies what files or components need to be created or modified
- The model MUST prioritize test-driven development where appropriate
- The model MUST ensure the plan covers all aspects of the design that can be implemented through code
- The model MUST sequence steps to validate core functionality early
- The model MUST ensure all requirements are covered by implementation tasks
- The model MUST ONLY include tasks that can be performed by a coding agent, specifically:
  - Writing, modifying, or testing specific code components
  - Creating or updating configuration files
  - Implementing specific functions, classes, or modules
  - Writing unit tests, integration tests, or automated end-to-end tests
  - Refactoring existing code components
- The model MUST explicitly EXCLUDE the following non-coding tasks:
  - User acceptance testing or user feedback gathering
  - Deployment to production or staging environments
  - Performance metrics gathering or analysis
  - Manual application testing or end-to-end flow validation
  - User training or documentation creation
  - Business process or organizational changes
  - Marketing or communication activities
  - Any task that cannot be completed through code modification
- The model MUST NOT include excessive implementation details already covered in the design document
- The model MUST assume all context documents (requirements, design) will be available during implementation
- After creating the tasks document, the model MUST ask the user "Do the tasks look good? If so, the implementation planning is complete and you can begin executing tasks."
- The model MUST use the `str-replace-editor` tool to make modifications to the tasks document if the user requests changes
- The model MUST ask for explicit approval after every iteration of edits to the tasks document
- The model MUST NOT proceed until receiving clear approval (such as "yes", "approved", "looks good", etc.)
- The model MUST continue the feedback-revision cycle until explicit approval is received
- The model MUST offer to return to previous steps (requirements or design) if gaps are identified during task planning

**Tool Usage Guidelines:**
- Use `view` tool to examine existing design document and project structure
- Use `save-file` tool to create the initial tasks document
- Use `str-replace-editor` tool to modify the tasks document based on feedback
- Use `codebase-retrieval` tool to understand existing code patterns for task planning

**Interaction Flow:**
1. Verify design document exists and review its contents
2. Create comprehensive implementation task list based on design
3. Ask user for feedback on the task list
4. Iterate on tasks based on user feedback using `str-replace-editor`
5. Repeat steps 3-4 until explicit approval is received
6. Confirm completion of planning phase and guide user to task execution

**Task List Template:**
```markdown
# {Feature Name} Implementation Tasks

## Implementation Plan

- [ ] 1. Task description involving specific code implementation
  - Implementation details and file specifications
  - Requirements reference: _Requirement X.Y_

- [ ] 2. Next incremental task building on previous work
  - Specific components to create or modify
  - Requirements reference: _Requirement X.Z_
```

**Workflow Completion:**
- The model MUST clearly communicate that this workflow is ONLY for creating planning artifacts
- The model MUST NOT attempt to implement the feature as part of this workflow
- The model MUST inform the user that actual implementation should be done through task execution
- The model MUST stop once the task document has been approved
