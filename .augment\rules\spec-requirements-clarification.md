---
type: "manual"
---

### 1. 需求收集阶段

首先，基于功能想法生成 EARS 格式的初始需求集合，然后与用户迭代完善，直到需求完整且准确。

在此阶段不要专注于代码探索，而是专注于编写需求，这些需求稍后将转化为设计。

**约束条件：**

- 模型必须使用 `view` 工具检查项目结构，了解是否存在规范文档目录
- 模型必须使用 `save-file` 工具创建 '.kiro/specs/{feature_name}/requirements.md' 文件（如果不存在）
- 模型必须基于用户的粗略想法生成需求文档的初始版本，而不是先询问连续问题
- 模型必须使用以下格式格式化初始 requirements.md 文档：
  - 清晰的介绍部分，总结功能特性
  - 分层编号的需求列表，每个需求包含：
    - 格式为"作为[角色]，我希望[功能]，以便[收益]"的用户故事
    - EARS 格式（需求语法简易方法）的编号验收标准列表
  - 示例格式：
    ```markdown
    # {功能名称} 需求规范
    
    ## 1. 功能概述
    [功能的简要描述和目标]
    
    ## 2. 需求详情
    
    ### 2.1 [需求类别]
    **用户故事：** 作为[角色]，我希望[功能]，以便[收益]
    
    **验收标准：**
    1. 当[条件]时，系统应该[行为]
    2. 如果[条件]，那么系统必须[行为]
    3. 在[情况]下，系统应当[行为]
    ```

- 模型应该在初始需求中考虑边缘情况、用户体验、技术约束和成功标准
- 更新需求文档后，模型必须询问用户："需求看起来如何？如果满意，我们可以继续进行设计阶段。"
- 如果用户要求更改或未明确批准，模型必须修改需求文档
- 每次编辑需求文档后，模型必须寻求明确批准
- 在收到明确批准（如"是的"、"批准"、"看起来不错"等）之前，模型不得进入设计阶段
- 模型必须继续反馈-修订循环，直到收到明确批准
- 模型应该建议需求可能需要澄清或扩展的具体领域
- 模型可以就需要澄清的需求特定方面提出针对性问题
- 当用户对特定方面不确定时，模型可以建议选项
- 用户接受需求后，模型必须进入设计阶段

**工具使用指南：**
- 使用 `view` 工具检查现有项目结构和规范目录
- 使用 `save-file` 工具创建新的需求文档
- 使用 `str-replace-editor` 工具修改现有需求文档
- 可选择使用任务管理工具跟踪需求收集进度
- 使用 `codebase-retrieval` 工具了解项目上下文（如果需要）

**交互流程：**
1. 检查项目结构，确定规范文档存放位置
2. 基于用户输入生成初始需求文档
3. 直接询问用户对需求的反馈
4. 根据反馈迭代修改需求文档
5. 重复步骤3-4直到获得明确批准
6. 获得批准后进入下一阶段