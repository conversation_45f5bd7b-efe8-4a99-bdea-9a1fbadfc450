package net.netca.sdk.constants;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.pki.AlgorithmIdentifier;
import net.netca.pki.encoding.asn1.pki.RSASSAPSSParams;
import net.netca.sdk.execption.UnknownSignatureAlgorithmException;

public enum SignatureAlgorithmEnum implements IBaseEnum {


    SHA1WithRSA(AlgorithmIdentifier.SHA1WithRSA_OID, "SHA1WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA1WithRSA_OID);
        }

    },
    SHA224WithRSA(AlgorithmIdentifier.SHA224WithRSA_OID, "SHA224WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA224WithRSA_OID);
        }
    },
    SHA256WithRSA(AlgorithmIdentifier.SHA256WithRSA_OID, "SHA256WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA256WithRSA_OID);
        }

    },
    SHA384WithRSA(AlgorithmIdentifier.SHA384WithRSA_OID, "SHA384WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA384WithRSA_OID);
        }

    },
    SHA512WithRSA(AlgorithmIdentifier.SHA512WithRSA_OID, "SHA512WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA512WithRSA_OID);
        }

    },
    SHA512_224WithRSA(AlgorithmIdentifier.SHA512_224WithRSA_OID, "SHA512/224WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA512_224WithRSA_OID);
        }

    },
    SHA512_256WithRSA(AlgorithmIdentifier.SHA512_256WithRSA_OID, "SHA512/256WithRSA") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA512_256WithRSA_OID);
        }

    },
    DSAWithSHA1(AlgorithmIdentifier.DSAWithSHA1_OID, "DSAWithSHA1") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.DSAWithSHA1_OID);
        }

    },
    DSAWithSHA224(AlgorithmIdentifier.DSAWithSHA224_OID, "DSAWithSHA224") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.DSAWithSHA224_OID);
        }

    },
    DSAWithSHA256(AlgorithmIdentifier.DSAWithSHA256_OID, "DSAWithSHA256") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.DSAWithSHA256_OID);
        }

    },
    ECDSAWithSHA1(AlgorithmIdentifier.ECDSAWithSHA1_OID, "ECDSAWithSHA1") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.ECDSAWithSHA1_OID);
        }

    },
    ECDSAWithSHA224(AlgorithmIdentifier.ECDSAWithSHA224_OID, "ECDSAWithSHA224") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.ECDSAWithSHA224_OID);
        }

    },
    ECDSAWithSHA256(AlgorithmIdentifier.ECDSAWithSHA256_OID, "ECDSAWithSHA256") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.ECDSAWithSHA256_OID);
        }

    },
    ECDSAWithSHA384(AlgorithmIdentifier.ECDSAWithSHA384_OID, "ECDSAWithSHA384") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.ECDSAWithSHA384_OID);
        }

    },
    ECDSAWithSHA512(AlgorithmIdentifier.ECDSAWithSHA512_OID, "ECDSAWithSHA512") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.ECDSAWithSHA512_OID);
        }

    },
    SM3WithSM2(AlgorithmIdentifier.SM3WithSM2_OID, "SM3WithSM2") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            return AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SM3WithSM2_OID);
        }

    },
    RSASSA_PSS_SHA1(AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA1", "RSASSA PSS SHA1") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            /**
             RSASSA-PSS-params ::= SEQUENCE {
             hashAlgorithm      [0] HashAlgorithm    DEFAULT sha1,
             maskGenAlgorithm   [1] MaskGenAlgorithm DEFAULT mgf1SHA1,
             saltLength         [2] INTEGER          DEFAULT 20,
             trailerField       [3] TrailerField     DEFAULT trailerFieldBC
             }
             */
            String hashAlgorithm = AlgorithmIdentifier.SHA1_OID; //default
            String maskGenAlgorithm = AlgorithmIdentifier.SHA1_OID;//default
            int saltLen = 20; //default
            RSASSAPSSParams param = new RSASSAPSSParams(hashAlgorithm, maskGenAlgorithm, saltLen);

            return new AlgorithmIdentifier(AlgorithmIdentifier.RSASSA_PSS_OID, param.getASN1Object());
        }

    },
    RSASSA_PSS_SHA256(AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA256", "RSASSA PSS SHA256") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            String hashAlgorithm = AlgorithmIdentifier.SHA256_OID;
            String maskGenAlgorithm = AlgorithmIdentifier.SHA256_OID;
            int saltLen = 32;
            RSASSAPSSParams param = new RSASSAPSSParams(hashAlgorithm, maskGenAlgorithm, saltLen);

            return new AlgorithmIdentifier(AlgorithmIdentifier.RSASSA_PSS_OID, param.getASN1Object());
        }

    },
    RSASSA_PSS_SHA384(AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA384", "RSASSA PSS SHA384") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            String hashAlgorithm = AlgorithmIdentifier.SHA384_OID;
            String maskGenAlgorithm = AlgorithmIdentifier.SHA384_OID;
            int saltLen = 48;
            RSASSAPSSParams param = new RSASSAPSSParams(hashAlgorithm, maskGenAlgorithm, saltLen);

            return new AlgorithmIdentifier(AlgorithmIdentifier.RSASSA_PSS_OID, param.getASN1Object());
        }

    },
    RSASSA_PSS_SHA512(AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA512", "RSASSA PSS SHA512") {
        @Override
        public AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException {
            String hashAlgorithm = AlgorithmIdentifier.SHA512_OID;
            String maskGenAlgorithm = AlgorithmIdentifier.SHA512_OID;
            int saltLen = 64; //default
            RSASSAPSSParams param = new RSASSAPSSParams(hashAlgorithm, maskGenAlgorithm, saltLen);

            return new AlgorithmIdentifier(AlgorithmIdentifier.RSASSA_PSS_OID, param.getASN1Object());
        }

    };


    private String oid;
    private String name;

    SignatureAlgorithmEnum(String oid, String name) {
        this.oid = oid;
        this.name = name;
    }

    @Override
    public String getCode() {
        return oid;
    }

    @Override
    public String getDescription() {
        return name;
    }



    public abstract AlgorithmIdentifier generateAlgorithmIdentifier() throws PkiException;


    public static SignatureAlgorithmEnum getByName(String name)  {
        for (SignatureAlgorithmEnum value : values()) {
            if (value.getDescription().equals(name)) {
                return value;
            }
        }
        throw new IllegalArgumentException(String.format("未识别的签名算法:%s", name));
    }

    public static SignatureAlgorithmEnum getSignatureAlgorithmConstant(String oid) throws UnknownSignatureAlgorithmException {
        if (AlgorithmIdentifier.SHA1WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA1WithRSA;
        }

        if (AlgorithmIdentifier.SHA224WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA224WithRSA;
        }

        if (AlgorithmIdentifier.SHA256WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA256WithRSA;
        }

        if (AlgorithmIdentifier.SHA384WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA384WithRSA;
        }

        if (AlgorithmIdentifier.SHA512WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA512WithRSA;
        }

        if (AlgorithmIdentifier.SHA512_224WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA512_224WithRSA;
        }

        if (AlgorithmIdentifier.SHA512_256WithRSA_OID.equalsIgnoreCase(oid)) {
            return SHA512_256WithRSA;
        }

        if (AlgorithmIdentifier.DSAWithSHA1_OID.equalsIgnoreCase(oid)) {
            return DSAWithSHA1;
        }

        if (AlgorithmIdentifier.DSAWithSHA224_OID.equalsIgnoreCase(oid)) {
            return DSAWithSHA224;
        }

        if (AlgorithmIdentifier.DSAWithSHA256_OID.equalsIgnoreCase(oid)) {
            return DSAWithSHA256;
        }

        if (AlgorithmIdentifier.ECDSAWithSHA1_OID.equalsIgnoreCase(oid)) {
            return ECDSAWithSHA1;
        }

        if (AlgorithmIdentifier.ECDSAWithSHA224_OID.equalsIgnoreCase(oid)) {
            return ECDSAWithSHA224;
        }

        if (AlgorithmIdentifier.ECDSAWithSHA256_OID.equalsIgnoreCase(oid)) {
            return ECDSAWithSHA256;
        }

        if (AlgorithmIdentifier.ECDSAWithSHA384_OID.equalsIgnoreCase(oid)) {
            return ECDSAWithSHA384;
        }

        if (AlgorithmIdentifier.ECDSAWithSHA512_OID.equalsIgnoreCase(oid)) {
            return ECDSAWithSHA512;
        }

        if (AlgorithmIdentifier.SM3WithSM2_OID.equalsIgnoreCase(oid)) {
            return SM3WithSM2;
        }

        if ((AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA1").equalsIgnoreCase(oid)) {
            return RSASSA_PSS_SHA1;
        }

        if ((AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA256").equalsIgnoreCase(oid)) {
            return RSASSA_PSS_SHA256;
        }

        if ((AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA384").equalsIgnoreCase(oid)) {
            return RSASSA_PSS_SHA384;
        }

        if ((AlgorithmIdentifier.RSASSA_PSS_OID + ".SHA512").equalsIgnoreCase(oid)) {
            return RSASSA_PSS_SHA512;
        }

        throw new UnknownSignatureAlgorithmException(String.format("未识别的签名算法:%s", oid));

    }
}
