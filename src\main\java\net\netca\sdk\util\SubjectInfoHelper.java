package net.netca.sdk.util;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.sdk.message.SubjectInfo;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-10-17 9:55
 */
@CommonsLog
public abstract class SubjectInfoHelper {



    /**
     * Map{
     * key : set method name
     * value : Method
     * }
     */
    private final static Map<String, Method> SUBJECT_SETTER_MAP;

    /**
     * Map{
     * key : get method name
     * value : Method
     * }
     */
    private final static Map<String, Method> SUBJECT_GETTER_MAP;


    private final static List<String> FIELD_NAMES;

    static {
        Map<String, Method> tempSubjectSetterMap = new HashMap<>(36);
        Map<String, Method> tempSubjectGetterMap = new HashMap<>(36);
        List<String> tempFieldNames = new ArrayList<>();
        Class<SubjectInfo> clazz = SubjectInfo.class;
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            tempFieldNames.add(fieldName);
            String getStr = "get" + StringUtils.capitalize(fieldName);
            String setStr = "set" + StringUtils.capitalize(fieldName);
            try {
                Method getFieldMethod = ReflectionUtils.findMethod(clazz, getStr);
                Method setFieldMethod = ReflectionUtils.findMethod(clazz, setStr, String.class);
                if (getFieldMethod != null) {
                    tempSubjectGetterMap.put(getStr, getFieldMethod);
                }
                if (setFieldMethod != null) {
                    tempSubjectSetterMap.put(setStr, setFieldMethod);
                }
            } catch (SecurityException e) {
                log.warn(e.getMessage(), e);
            }
        }
        SUBJECT_SETTER_MAP = Collections.unmodifiableMap(tempSubjectSetterMap);
        SUBJECT_GETTER_MAP = Collections.unmodifiableMap(tempSubjectGetterMap);
        FIELD_NAMES = Collections.unmodifiableList(tempFieldNames);
    }


    /**
     * 利用反射对目标对象{@link SubjectInfo}进行设值
     *
     * @param subjectInfo 目标对象{@link SubjectInfo}
     * @param value       预设值
     * @param fieldName   预设属性名
     */
    public static boolean setPropertyValue(SubjectInfo subjectInfo, String value, String fieldName) throws InvocationTargetException, IllegalAccessException {
        String lowercaseSetterName = "set" + StringUtils.capitalize(fieldName);
        if (SUBJECT_SETTER_MAP.containsKey(lowercaseSetterName)) {
            SUBJECT_SETTER_MAP.get(lowercaseSetterName).invoke(subjectInfo, value);
            return true;
        }
        return false;
    }

    public static Map<String, Method> getSubjectSetterMap() {
        return SUBJECT_SETTER_MAP;
    }

    public static Map<String, Method> getSubjectGetterMap() {
        return SUBJECT_GETTER_MAP;
    }

    public static List<String> getFieldNames() {
        return FIELD_NAMES;
    }
}
