package net.netca.cloudkey.base.dto.certalertpolicy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/6 10:35
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CertAlertPolicyReqDTO implements ISignature {

    private transient OperatorSignature operatorSignature;

    @NotNull(message = "id 不允许为空")
    private Integer id;

}
