package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.Gender;
import net.netca.cloudkey.base.constraints.check.CheckType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class GenderVali<PERSON>tor implements ConstraintValidator<Gender, Integer> {


    @Override
    public void initialize(Gender constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (Objects.nonNull(value)) {
            isValid = CheckType.GENDER.check(value + "");
        }
        return isValid;
    }
}
