package net.netca.sdk.message;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 证书主题信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@Builder
public class SubjectInfo {
    private String cn;
    private String c;
    private String st;
    private String l;
    private String o;
    private String ou;
    private String email;
    private String dc;
    private String uid;
    private String serialNumber;
    // Street
    private String street;
    // SURNAME
    private String surname;
    // GIVENNAME
    private String givenName;
    // INITIALS
    private String initials;
    // GENERATION
    private String generation;
    // unstructuredAddress
    private String unstructuredAddress;
    // unstructuredName
    private String unstructuredName;
    // UniqueIdentifier
    private String uniqueIdentifier;
    // DN
    private String dn;
    // Pseudonym
    private String pseudonym;
    // PostalAddress
    private String postalAddress;
    // NameAtBirth
    private String nameAtBirth;
    // CountryOfCitizenship
    private String countryOfCitizenship;
    // CountryOfResidence
    private String countryOfResidence;
    // Gender
    private String gender;
    // PlaceOfBirth
    private String placeOfBirth;
    // DateOfBirth
    private String dateOfBirth;
    // PostalCode
    private String postalCode;
    // BusinessCategory
    private String businessCategory;
    // TelephoneNumber
    private String telephoneNumber;
    // Name
    private String name;
    // JurisdictionLocality
    private String jurisdictionLocality;
    // JurisdictionState
    private String jurisdictionState;
    // JurisdictionCountry
    private String jurisdictionCountry;
    // organizationIdentifier
    private String organizationIdentifier;
    // description
    private String description;
    // title
    private String title;
    // uri
    private String uri;
    // urn
    private String urn;
    // url
    private String url;
    // telexNumber
    private String telexNumber;
    // aliasedEntryName
    private String aliasedEntryName;
}
