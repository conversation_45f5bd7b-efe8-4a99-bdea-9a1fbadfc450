package net.netca.cloudkey.base.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.constant.BatchTaskStatusEnum;

import java.io.Serializable;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2025-03-14 09:44:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReapplyCertUserVO implements Serializable {
    private static final long serialVersionUID = 1741917112L;
    private Integer id;
    private String uid;
    private String name;
    private String province;
    private String city;
    private String officialResidence;
    private Integer organizationId;
    /**
     * @see BatchTaskStatusEnum
     */
    private Integer status = BatchTaskStatusEnum.NOT_START.getCode();

    private String taskErrorMsg;
}
