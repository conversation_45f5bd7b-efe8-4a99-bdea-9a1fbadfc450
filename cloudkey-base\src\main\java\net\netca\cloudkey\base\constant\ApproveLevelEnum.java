package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description  审核等级，目前默认为一级，珠江医院为二级审核
 * @Date 10:52 2020/12/4
 **/
public enum ApproveLevelEnum implements IBaseEnum {

    LEVEL_2(2, "二级审核", null,"复审","userManager:approve:level2"),
    LEVEL_1(1, "一级审核", ApproveLevelEnum.LEVEL_2,"初审","userManager:approve:level1");

    private Integer code;
    private String description;
    private ApproveLevelEnum nextApproveLevelEnum;
    private String otherName;
    private String permissionCode;

    private ApproveLevelEnum(Integer code, String description, ApproveLevelEnum nextApproveLevelEnum,String otherName,String permissionCode) {
        this.code = code;
        this.description = description;
        this.nextApproveLevelEnum = nextApproveLevelEnum;
        this.otherName = otherName;
        this.permissionCode = permissionCode;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public ApproveLevelEnum getNextApproveLevelEnumByCurrent() {
        return this.nextApproveLevelEnum;
    }

    public String getOtherName() {
        return otherName;
    }

    public void setOtherName(String otherName) {
        this.otherName = otherName;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public static String getOtherName(int code) {
        Object obj = "未知";
        for (ApproveLevelEnum approveLevelEnum : values()) {
            if(approveLevelEnum.getCode() == code){
                obj = approveLevelEnum.getOtherName();
            }
        }
        return obj.toString();
    }

    public static ApproveLevelEnum getApproveLevelEnumByCode(Integer code) {
        if (Objects.equals(code, ApproveLevelEnum.LEVEL_1.getCode())) {
            return ApproveLevelEnum.LEVEL_1;
        } else if (Objects.equals(code, ApproveLevelEnum.LEVEL_2.getCode())) {
            return ApproveLevelEnum.LEVEL_2;
        } else {
            throw new CloudKeyRuntimeException("未知类型");
        }
    }
}
