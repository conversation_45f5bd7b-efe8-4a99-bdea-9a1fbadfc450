package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(description = "签署者")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoordSignerDto {

    private Long id;

    /**
     * 签署者uid
     */
    @ApiModelProperty(value = "签署者uid")
    private String signerUid;

    @ApiModelProperty(value = "签署者新uid")
    private String signerNewUid;

    /**
     * 签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成
     */
    @ApiModelProperty(value = "签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成")
    private Integer signStatus;

    @ApiModelProperty(value = "签署状态描述")
    private String signStatusDesc;

    /**
     * 签署意见
     */
    @ApiModelProperty(value = "拒绝意见 仅当签名状态为已拒绝时有效")
    private String signOpinion;


    @ApiModelProperty(value = "签署者签章配置json")
    private String sealOption;

    /**
     * 签署者签章图片
     */
    @ApiModelProperty(value = "签署者签章图片")
    private String sealPicture;

    /**
     * 签署者签章图片
     */
    @ApiModelProperty(value = "签署者名称")
    private String signerName;

    @ApiModelProperty(value = "签署者最后操作的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
