package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Arrays;
import java.util.Optional;

/**
 * @descriptions: 变更授权状态的操作常量
 * @date: 2019/10/10
 * @author: <PERSON>romChars
 * @see AuthorizeStatusConstant
 */
public enum AuthorizeOperationConstant implements IBaseEnum {

    AUTHORIZE_OPERATION_COFIRM(0, "同意授权",
            1),
    AUTHORIZE_OPERATION_FREEZE(1, "冻结授权",
            2),
    AUTHORIZE_OPERATION_ACTIVE(2, "解冻授权",
            1),
    AUTHORIZE_OPERATION_UNLOCK(3, "授权解锁",
            1),
    AUTHORIZE_OPERATION_DELETE(4, "删除授权",
            4);

    private Integer code;
    private String description;
    private Integer nextStatus;

    AuthorizeOperationConstant(Integer code, String description, Integer nextStatus) {
        this.code = code;
        this.description = description;
        this.nextStatus = nextStatus;
    }

    public static AuthorizeOperationConstant getAuthorizeOperatorConstant(Integer code){
        Optional<AuthorizeOperationConstant> optianalAuthorizeOperatorConstant = Arrays.asList(AuthorizeOperationConstant.values()).stream()
                .filter(authorizeOperatorConstant -> authorizeOperatorConstant.getCode().equals(code))
                .findFirst();

        if (!optianalAuthorizeOperatorConstant.isPresent()) {
            throw new CloudKeyRuntimeException("错误的授权操作");
        }

        return optianalAuthorizeOperatorConstant.get();
    }

    public Integer getNextStatus() {
        return this.nextStatus;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
