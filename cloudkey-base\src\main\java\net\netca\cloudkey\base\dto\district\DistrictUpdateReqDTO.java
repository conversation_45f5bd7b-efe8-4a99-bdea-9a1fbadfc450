package net.netca.cloudkey.base.dto.district;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 院区管理 - 修改院区 Request DTO
 *
 * <AUTHOR>
 * @date 2024/2/28 19:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistrictUpdateReqDTO implements ISignature {

    /**
     * 院区ID
     */
    @NotNull(message = "id 不允许为空")
    private Integer id;

    /**
     * 院区编号
     */
    @NotBlank(message = "code 不允许为空")
    private String code;

    /**
     * 院区名称
     */
    @NotBlank(message = "name 不允许为空")
    private String name;

    private transient OperatorSignature operatorSignature;

}
