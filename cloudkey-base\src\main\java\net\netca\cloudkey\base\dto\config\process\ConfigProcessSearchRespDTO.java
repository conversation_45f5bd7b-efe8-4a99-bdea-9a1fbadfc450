package net.netca.cloudkey.base.dto.config.process;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description: 流程配置返回查询DTO
 * @Author: dyr
 * @CreateDate: 2020/8/14 15:22
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessSearchRespDTO {

    private Integer id;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 操作顺序
     */
    private String operationSequence;

    /**
     * 流程对应平台
     */
    private String processPlatform;

    /**
     * 最新一次修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
