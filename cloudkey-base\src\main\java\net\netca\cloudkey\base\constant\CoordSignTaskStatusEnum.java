package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 协同签名任务状态枚举
 * -1-初始化， 0-未签名，1-进行中，2-已拒绝，3-已过期，4-已取消，5-已完成
 */
@Getter
@AllArgsConstructor
public enum CoordSignTaskStatusEnum implements IBaseEnum {

    /**
     * 待发起
     */
    INITIAL(-1, "待发起"),

    /**
     * 未签名
     */
    NOT_SIGNED(0, "待签署"),
    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    /**
     * 已拒绝
     */
    REJECTED(2, "已拒绝"),
    /**
     * 已过期
     */
    EXPIRED(3, "已过期"),

    /**
     * 已取消
     */
    CANCEL(4, "已取消"),
    /**
     * 已完成
     */
    COMPLETED(5, "已完成");

    private final Integer code;
    private final String description;

    public static String getDesc(Integer code) {
        for (CoordSignTaskStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.description;
            }
        }
        return "";
    }



}
