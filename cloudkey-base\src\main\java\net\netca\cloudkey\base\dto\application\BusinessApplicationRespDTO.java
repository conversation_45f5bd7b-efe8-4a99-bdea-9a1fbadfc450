package net.netca.cloudkey.base.dto.application;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/1 16:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessApplicationRespDTO {

    private Integer id;

    /**
     * 应用id
     */
    private String applicationId;

    /**
     * 应用名称
     */
    private String name;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private Integer status;

    /**
     * 院区ID
     */
    private Integer districtId;

    /**
     * 院区名称
     */
    private String districtName;

}
