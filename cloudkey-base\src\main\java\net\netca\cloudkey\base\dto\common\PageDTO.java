package net.netca.cloudkey.base.dto.common;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @descriptions:
 * @date: 2019/1/17
 * @author: SaromChars
 */
@Data
public class PageDTO<T> {
    private T condition;

    @NotNull(message = "查询页数不能为空")
    @Min(1)
    private Integer pageIndex;

    //单页限制最大一百条数据
    @Max(value = 100, message = "页查询记录不能超过100条")
    private Integer pageSize;
}
