package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum FaceTypeEnum implements IBaseEnum {

    FACE_NETCA(1, "网证通人脸识别"),
    FACE_WEAPP(2, "微信小程序互动视频人脸识别");

    FaceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
