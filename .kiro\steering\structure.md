# Project Structure & Architecture

## Multi-Module Maven Project
The CloudKey system follows a modular architecture with clear separation of concerns:

## Module Organization

### Core Modules
- **cloudkeyserver**: Main server application and entry point
- **cloudkey-base**: Shared base module with common utilities, DTOs, and POJOs
- **cloudkey-api**: Top-level API layer exposing external interfaces

### Business Domain Modules
- **cloudkey-identity**: Identity management, authentication, and user binding
- **cloudkey-pki**: PKI operations and certificate management
- **cloudkey-cert-life-cycle**: Certificate lifecycle management
- **cloudkey-wechat**: WeChat integration services
- **cloudkey-client**: Client-side functionality
- **cloudkey-sdk**: Software development kit

### Infrastructure Modules
- **cloudkey-extendibility**: Extension and plugin framework
- **cloudkeymq**: Message queue handling
- **cloudkeynetcajni**: JNI interface for NETCA cryptographic operations
- **common-notice**: Notification services

## Layered Architecture (Alibaba Java Development Manual V1.3.0)

### Horizontal Layers
1. **WebService Layer**: Top-level business layer providing external data access and control
2. **Manager Layer**: General capabilities for non-business logic (PKI, caching, certificate lifecycle)
3. **Service Layer**: Domain/business foundation with CRUD operations and entities
4. **Base/Basic Layer**: Common classes and POJOs not tied to specific business domains

### Vertical Divisions
- **WebService**: Split into OpenAPI, Web, ThirdService
- **Manager**: PKI, Authorize, UserLogin, CertLife, Notice
- **Service**: UserService, CertService, LogService, ConfigService

## Module Dependencies
- **cloudkey-api** → depends on cloudkey-identity, cloudkey-pki
- **cloudkey-identity** → depends on cloudkey-base, common-notice, cloudkeynetcajni, cloudkey-wechat
- **cloudkey-base** → foundation module, minimal dependencies

## Directory Structure
```
├── conf/                    # Configuration files
│   ├── cloudkey-config.properties
│   ├── DataModels/         # Data model definitions
│   └── *.jks              # Keystore files
├── [module-name]/
│   ├── src/main/java/     # Java source code
│   ├── src/main/resources/ # Resources
│   ├── src/test/          # Test code
│   ├── target/            # Build output
│   └── pom.xml           # Module POM
└── pom.xml               # Parent POM
```

## Naming Conventions
- **Request DTOs**: End with `ReqDTO`
- **Response DTOs**: End with `RespDTO`
- **Utility Classes**: Use Spring's StringUtils, custom utilities in `CustomStringUtils`

## Module Interaction Rules
1. Upper layers can call lower layers
2. Avoid circular dependencies
3. Common utilities should be in `cloudkey-base`
4. External interfaces only through WebService layer
5. DTOs, constants, exceptions should be pushed down to base modules