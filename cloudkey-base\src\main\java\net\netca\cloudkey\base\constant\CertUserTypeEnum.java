package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.util.EnumUtil;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * 证书类型
 *
 * <AUTHOR>
 * @since 2018年10月17日12:51:46
 */
public enum CertUserTypeEnum implements IBaseEnum {
    EMPLOYEE(1, "员工证书"), //
    PERSON(2, "个人证书"), //
    ORG(3, "机构证书");

    private Integer code;
    private String description;

    private CertUserTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        String obj = null;
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CertUserTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = (String) linkedHashMap.get("value");
                break;
            }
        }
        return obj;
    }

}
