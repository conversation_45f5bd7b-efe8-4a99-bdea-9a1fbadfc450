package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.MobilePhoneValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对手机号码的校验注解
 * @see MobilePhoneValidator
 */
@Documented
@Constraint(validatedBy = MobilePhoneValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface MobilePhone {

    String message() default "手机号码格式错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
