package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.Identity;
import net.netca.cloudkey.base.constraints.check.CheckType;
import net.netca.cloudkey.base.constraints.check.IdentityCheck;
import net.netca.cloudkey.base.dto.abstractclass.IIdentityReqDTO;
import net.netca.cloudkey.base.util.CustomStringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class IdentityValidator implements ConstraintValidator<Identity, IIdentityReqDTO> {

    private String type = null;

    @Override
    public void initialize(Identity constraintAnnotation) {
        String type = constraintAnnotation.type();
        this.type = type;
    }

    @Override
    public boolean isValid(IIdentityReqDTO value, ConstraintValidatorContext context) {
        boolean isValid = true;

        Integer identityType = value.getIdentityType();
        String identity = value.getIdentity();

        Integer legalPersonIdentityType = value.getLegalPersonIdentityType();
        String legalPersonIdentity = value.getLegalPersonIdentity();
        

        if (Objects.nonNull(identityType) && !CustomStringUtils.isBlank(identity)) {
            // 只当需要校验的值为空时，才进行校验
            // 根据type判断是哪种用户的证件校验
            if (Objects.equals("organization", type)) {
                isValid = this.isOrganizationValid(identityType, identity, context, null);
            } else {
                isValid = this.isUserValid(identityType, identity, context, null);
            }
        }

        if (Objects.nonNull(legalPersonIdentityType) && !CustomStringUtils.isBlank(legalPersonIdentity)) {
            isValid = this.isUserValid(legalPersonIdentityType, legalPersonIdentity, context, "法人");
        }

        return isValid;
    }

    private boolean isOrganizationValid(Integer identityType, String identity, ConstraintValidatorContext context, String prefix) {
        boolean isValid = CheckType.ORGIDENTITYTYPE.check(identityType + "");
        if (!isValid) {
            context.disableDefaultConstraintViolation();
            String tips = CustomStringUtils.isBlank(prefix) ? "证件类型值非法" : prefix + "证件类型值非法";
            context.buildConstraintViolationWithTemplate(tips).addConstraintViolation();
            return isValid;
        }

        isValid  = IdentityCheck.getChecker(identityType).check(identity);
        if (!isValid) {
            context.disableDefaultConstraintViolation();
            String tips = CustomStringUtils.isBlank(prefix) ? "证件号码格式错误" : prefix + "证件号码格式错误";
            context.buildConstraintViolationWithTemplate(tips).addConstraintViolation();
            return isValid;
        }
        return isValid;
    }

    private boolean isUserValid(Integer identityType, String identity, ConstraintValidatorContext context, String prefix) {
        boolean isValid = CheckType.PERSONIDENTITYTYPE.check(identityType + "");
        if (!isValid) {
            context.disableDefaultConstraintViolation();
            String tips = CustomStringUtils.isBlank(prefix) ? "证件类型值非法" : prefix + "证件类型值非法";
            context.buildConstraintViolationWithTemplate(tips).addConstraintViolation();
            return isValid;
        }

        isValid  = IdentityCheck.getChecker(identityType).check(identity);
        if (!isValid) {
            context.disableDefaultConstraintViolation();
            String tips = CustomStringUtils.isBlank(prefix) ? "证件号码格式错误" : prefix + "证件号码格式错误";
            context.buildConstraintViolationWithTemplate(tips).addConstraintViolation();
            return isValid;
        }
        return isValid;
    }
}
