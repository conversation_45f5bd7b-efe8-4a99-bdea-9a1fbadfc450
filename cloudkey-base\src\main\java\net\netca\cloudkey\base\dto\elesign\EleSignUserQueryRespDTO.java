package net.netca.cloudkey.base.dto.elesign;

import lombok.Data;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.List;

import static net.netca.cloudkey.base.constant.UserTypeConstant.BELONG_AUTHORIZED_USER;
import static net.netca.cloudkey.base.constant.UserTypeConstant.BELONG_CERT_USER;

@Secret
@Data
public class EleSignUserQueryRespDTO {

    private Integer id;

    private String name;

    private String uid;

    private Integer userType = BELONG_CERT_USER.getCode();

    private Integer status;

    private String projectUid;

    @SecurityDataField
    private String phone;

    @SecurityDataField
    private List<AuthorizedUserRespDTO> authorizedUsers;
}
