# Technology Stack

## Build System
- **Maven**: Multi-module project with parent <PERSON>OM
- **Java Version**: 1.8
- **Spring Boot**: 2.3.4.RELEASE

## Core Frameworks & Libraries
- **Spring Boot Starter Web**: REST API development
- **Spring Boot AOP**: Aspect-oriented programming
- **Spring Session Data Redis**: Session management
- **MyBatis Plus**: 3.5.9 - Database ORM with code generation
- **Apache Shiro**: 1.13.0 - Security framework
- **Redisson**: 3.15.0 - Distributed Redis client

## Database & Caching
- **MySQL**: 8.0.32 - Primary database
- **Redis**: Session storage and caching
- **Apache ShardingSphere**: 5.3.2 - Database sharding

## Cryptography & PKI
- **Bouncy Castle**: 1.70 - Cryptographic operations
- **NetcaJCrypto**: 6.1.0.0 - NETCA cryptographic library
- **Netca_PDFSign_JAVA**: 1.8.2.6 - PDF signing
- **netca-ofd-java**: 0.0.3-SNAPSHOT - OFD document handling

## Messaging & Integration
- **Spring Integration MQTT**: Message queuing
- **Pushy**: 0.13.11 - iOS push notifications
- **OkHttp**: 3.14.0 - HTTP client

## Utilities
- **Lombok**: 1.18.34 - Code generation
- **MapStruct**: 1.5.5.Final - Bean mapping
- **Hutool**: 5.8.7 - Java utility toolkit
- **Apache Commons**: Various utilities
- **Google Guava**: 30.0-jre - Core libraries

## Testing
- **Mockito**: 3.4.6 - Mocking framework
- **Spring Boot Test**: Testing support

## Logging
- **Log4j2**: 2.18.0 - Primary logging framework
- **SLF4J**: 1.7.7 - Logging facade

## Common Build Commands
```bash
# Build entire project
mvn clean install

# Build specific module
mvn clean install -pl cloudkey-api

# Skip tests
mvn clean install -DskipTests

# Run tests only
mvn test

# Generate code (MyBatis Plus)
mvn test -Dtest=CodeGenerator
```

## Configuration
- Configuration files located in `conf/` directory
- Main config: `cloudkey-config.properties` (deploy to Tomcat conf/)
- Supports debug, message, and notice property files