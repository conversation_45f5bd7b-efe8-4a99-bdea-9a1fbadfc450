package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @Description
 * @Date Created in 11:10 2024/11/15
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "协同签名任务分页-请求DTO")
public class CoordSignTaskPageReqDTO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发起者姓名")
    private String creatorName;

    @ApiModelProperty(value = "签名者姓名")
    private String signerName;

    @ApiModelProperty(value = "签名者uid")
    private String signerUid;

    @ApiModelProperty(value = "签名类型:0-P7签名，1-电子签章")
    private Integer signType;

    @ApiModelProperty(value = "任务状态：0-未签名，1-进行中,2-已拒绝,3-已过期,5-已完成")
    private Integer taskStatus;

    @ApiModelProperty(value = "第三方业务流水号")
    private String thirdOrderId;

    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    @ApiModelProperty(value = "任务id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long taskId;

    @ApiModelProperty(value = "任务创建开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "任务创建结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty(value = "过期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTimeStart;

    @ApiModelProperty(value = "过期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTimeEnd;

}
