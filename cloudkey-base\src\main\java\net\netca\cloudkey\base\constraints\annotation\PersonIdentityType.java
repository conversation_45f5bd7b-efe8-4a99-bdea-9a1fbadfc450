package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.PersonIdentityTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对个人证件类型的校验注解
 * @see PersonIdentityTypeValidator
 */
@Documented
@Constraint(validatedBy = PersonIdentityTypeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface PersonIdentityType {

    String message() default "证件类型值非法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
