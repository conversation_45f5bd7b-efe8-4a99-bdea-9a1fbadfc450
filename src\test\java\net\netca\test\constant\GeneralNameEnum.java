package net.netca.test.constant;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.GeneralName;
import net.netca.pki.encoding.asn1.pki.X500Name;
import net.netca.sdk.util.json.JsonUtils;
import net.netca.test.asn1.format.OtherNameFormat;
import net.netca.test.asn1.format.RDNSequenceFormat;
import net.netca.test.exception.ExtensionFormatException;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

public enum GeneralNameEnum implements IBaseEnum{
	
	otherName(0, "otherName"){

		@Override
		public Object parseValue(Object value) {
			if(value instanceof String){
				return JsonUtils.parseJson((String)value, OtherNameFormat.class);
			}
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			OtherNameFormat otherNameFormat;
			if(value instanceof OtherNameFormat){
				otherNameFormat = (OtherNameFormat) value;
				return GeneralName.NewOtherName(otherNameFormat.generateOtherName());
			}else if(value instanceof String){
				otherNameFormat = JsonUtils.parseJson((String)value, OtherNameFormat.class);
				return GeneralName.NewOtherName(otherNameFormat.generateOtherName());
			}
			return null;
		}
		
	},
	rfc822Name(1, "rfc822Name"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return GeneralName.NewRFC822Name((String)value);
		}
		
	},
	dNSName(2, "dNSName"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return GeneralName.NewDNSName((String)value);
		}
		
	},
	/*x400Address(3, "x400Address"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return null;
		}
		
	},*/
	directoryName(4, "directoryName"){

		@Override
		public Object parseValue(Object value) {
			if(value instanceof String){
				return JsonUtils.parseJson((String)value, RDNSequenceFormat.class);
			}
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			if(value instanceof RDNSequenceFormat){
				X500Name x500Name=((RDNSequenceFormat)value).generate();
				return GeneralName.NewDirectoryName(x500Name);
			}else if(value instanceof String){
				RDNSequenceFormat rdnSequenceFormat = JsonUtils.parseJson((String)value, RDNSequenceFormat.class);
				X500Name x500Name=rdnSequenceFormat.generate();
				return GeneralName.NewDirectoryName(x500Name);
			}
			return null;
		}
		
	},
	/*ediPartyName(5, "ediPartyName"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return null;
		}
		
	},*/
	uniformResourceIdentifier(6, "uniformResourceIdentifier"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return GeneralName.NewURI((String)value);
		}
		
	},
	iPAddress(7, "iPAddress"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			String hexValue=(String)value;
			byte[] byteValue = null;
			try {
				byteValue = Hex.decode(hexValue);
			}catch(PkiException e){
				try {//IPV4
					InetAddress inetAddress = InetAddress.getByName(hexValue);
					byteValue = inetAddress.getAddress();
				}  catch (UnknownHostException e1) { //IPV6
					try {
						InetAddress inet6Address = Inet6Address.getByName(hexValue);
						byteValue = inet6Address.getAddress();
					} catch (UnknownHostException e2) {
						// 解决SZZS-98，先内部捕获成PkiException异常，再往上抛出，告之产生Value失败
						throw new PkiException(e2);
					}
				}
			}
			return GeneralName.NewIPAddress(byteValue);
		}
		
	},
	registeredID(8, "registeredID"){

		@Override
		public Object parseValue(Object value) {
			return value;
		}

		@Override
		public GeneralName generalValue(Object value) throws PkiException {
			return GeneralName.NewRegisteredID((String)value);
		}
		
	};
	

	private GeneralNameEnum(Integer code, String description) {
		this.code = code;
		this.description = description;
	}
	
	private Integer code;
	private String description;
	
	public Integer getCode() {
		return code;
	}
	public void setCode(Integer code) {
		this.code = code;
	}
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	public abstract Object parseValue(Object value);
	public abstract GeneralName generalValue(Object value) throws PkiException;
	
	public static GeneralNameEnum getGeneralNameEnumByType(int type) throws ExtensionFormatException {
		switch(type){
			case 0:
				return otherName;
			case 1:
				return rfc822Name;
			case 2:
				return dNSName;
			/*case 3:
				return x400Address;*/
			case 4:
				return directoryName;
			/*case 5:
				return ediPartyName;*/
			case 6:
				return uniformResourceIdentifier;
			case 7:
				return iPAddress;
			case 8:
				return registeredID;
			default:
				throw new ExtensionFormatException(String.format("未识别的GeneralName 类型: %d ", type));
		}
	}
}
