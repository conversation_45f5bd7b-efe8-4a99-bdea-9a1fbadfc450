package net.netca.sdk.constants;

/**
 * @author: zys
 * @date: 2020/3/2 22:23
 */
public enum BusinessTypeEnum implements IBaseEnum {
    P10_REGISTER(1, "证书注册 - p10"),
    PUBLIC_KEY_REGISTER(2, "证书注册 - 公钥"),
    RENEWAL(3, "证书续期"),
    UPDATE_KEY(4, "密钥更新"),
    REVOKE(5, "证书注销"),
    SUSPEND(6, "挂失"),
    UNSUSPEND(7, "解挂"),
    RECOVERY(8, "密钥恢复"),
    MODIFICATION(9, "证书变更")
    ;


    private Integer code;

    private String description;

    BusinessTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
