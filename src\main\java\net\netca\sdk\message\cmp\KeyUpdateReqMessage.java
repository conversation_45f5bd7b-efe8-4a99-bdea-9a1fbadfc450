package net.netca.sdk.message.cmp;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 证书续期/密钥更新
 *
 * @author: zys
 * @date: 2020/3/2 17:24
 */
@SuperBuilder(toBuilder = true)
@Setter
@Getter
public abstract class KeyUpdateReqMessage extends CmpMessage {


    /**
     * 证书序列号，用于证书续期|密钥更新
     * 详见：RFC 4211 #6.5 OldCert ID Control(https://tools.ietf.org/html/rfc4211#section-6.5)
     *
     * @see org.bouncycastle.asn1.crmf.Controls
     */
    private String serialNumber;

    /**
     * 颁发者
     */
    private String issuer;
}
