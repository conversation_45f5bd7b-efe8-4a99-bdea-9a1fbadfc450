package net.netca.sdk.util.json;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonObjectFormatVisitor;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.ser.PropertyFilter;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.databind.type.TypeFactory;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;

public class JsonUtils {

    public static final String  DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String[] DEFAULT_SENSITIVE_PROPERTIES = { "password" };

    /**
     * 只能用于不修改ObjectMapper配置的方法中
     */
    private static final ObjectMapper OBJECT_MAPPER_TO_THREAD_SAFE;
    static {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setDateFormat(new SimpleDateFormat(DEFAULT_DATE_FORMAT))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER_TO_THREAD_SAFE = objectMapper;
    }

    /**
     * 只能用于不修改ObjectMapper配置的方法中
     */
//    public static final String[] PROPERTIES_TO_EXCLUDE_WHEN_LOG = { "contextMac" };
    private static final ObjectMapper OBJECT_MAPPER_TO_THREAD_SAFE_WHEN_LOG;
    static {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
//        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(SimpleBeanPropertyFilter.serializeAllExcept(PROPERTIES_TO_EXCLUDE_WHEN_LOG)));
//        objectMapper.addMixIn(Object.class, BeanPropertyFilter.class);
        OBJECT_MAPPER_TO_THREAD_SAFE_WHEN_LOG = objectMapper;
    }

    /**
     * 只能用于不修改ObjectMapper配置的方法中
     */
    private static final ObjectMapper OBJECT_MAPPER_TO_THREAD_SAFE_IN_MILLISECOND_MODE;
    static {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER_TO_THREAD_SAFE_IN_MILLISECOND_MODE = objectMapper;
    }

    /**
     * 只能用于不修改ObjectMapper配置的方法中
     */
    private static final ObjectMapper OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE;
    static {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL).setDateFormat(new SimpleDateFormat(DEFAULT_DATE_FORMAT))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(new HashSet<>(Arrays.asList(DEFAULT_SENSITIVE_PROPERTIES)))))
                .addMixIn(Object.class, BeanPropertyFilter.class);
        OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE = objectMapper;
    }

    /**
     * 只能用于不修改ObjectMapper配置的方法中
     */
    public static final String[] PROPERTIES_TO_EXCLUDE_IN_SENSITIVE_MODEL_WHEN_LOG = { "operatorSignature" };
    private static final ObjectMapper OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE_WHEN_LOG;
    static {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL).setDateFormat(new SimpleDateFormat(DEFAULT_DATE_FORMAT))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .setFilterProvider(new SimpleFilterProvider().setDefaultFilter(
                CustomBeanPropertyFilter.newInstance(new HashSet<>(Arrays.asList(DEFAULT_SENSITIVE_PROPERTIES)), new HashSet<>(Arrays.asList(PROPERTIES_TO_EXCLUDE_IN_SENSITIVE_MODEL_WHEN_LOG)))))
                .addMixIn(Object.class, BeanPropertyFilter.class);
        OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE_WHEN_LOG = objectMapper;
    }

    private JsonUtils() {}

    public static String toJson(Object o) {
        return doToJson(OBJECT_MAPPER_TO_THREAD_SAFE, o);
    }

    public static String toJson(Object o, boolean pretty) {
        return pretty ? toJsonInPrettyFormatMode(o) : toJson(o);
    }

    public static String toJsonInPrettyFormatMode(Object o) {
        try {
            return OBJECT_MAPPER_TO_THREAD_SAFE.writerWithDefaultPrettyPrinter().writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new JsonException("To json error.", e);
        }
    }

    public static String toJsonWhenLog(Object o) {
        return doToJson(OBJECT_MAPPER_TO_THREAD_SAFE_WHEN_LOG, o);
    }

    public static String toJsonInDateMillisecondMode(Object o) {
        return doToJson(OBJECT_MAPPER_TO_THREAD_SAFE_IN_MILLISECOND_MODE, o);
    }

    public static String toJson(Object o, String dateFormat) {
        ObjectMapper objectMapper = getObjectMapper();

        objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));

        return doToJson(objectMapper, o);
    }

    public static String toJson(Object o, String ...propertiesToExclude) {
        return toJson(o, new HashSet<>(Arrays.asList(propertiesToExclude)));
    }

    public static String toJson(Object o, List<String> propertiesToExclude) {
        return toJson(o, new HashSet<>(propertiesToExclude));
    }

    public static String toJson(Object o, String dateFormat, List<String> propertiesToExclude) {
        return toJson(o, dateFormat, new HashSet<>(propertiesToExclude));
    }

    public static String toJson(Object o, Set<String> propertiesToExclude) {
        ObjectMapper objectMapper = getObjectMapper();

        SimpleFilterProvider simpleFilterProvider = new SimpleFilterProvider();
        simpleFilterProvider.setDefaultFilter(SimpleBeanPropertyFilter.serializeAllExcept(propertiesToExclude));
        objectMapper.setFilterProvider(simpleFilterProvider);
        objectMapper.addMixIn(o.getClass(), BeanPropertyFilter.class);

        return doToJson(objectMapper, o);
    }

    public static String toJson(Object o, String dateFormat, Set<String> propertiesToExclude) {
        ObjectMapper objectMapper = getObjectMapper();

        objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));

        SimpleFilterProvider simpleFilterProvider = new SimpleFilterProvider();
        simpleFilterProvider.setDefaultFilter(SimpleBeanPropertyFilter.serializeAllExcept(propertiesToExclude));
        objectMapper.setFilterProvider(simpleFilterProvider);
        objectMapper.addMixIn(o.getClass(), BeanPropertyFilter.class);

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o) {
        return doToJson(OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE, o);
    }

    public static String toJsonInSensitiveMode(Object o, String ...propertiesToSensitive) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(new HashSet<>(Arrays.asList(propertiesToSensitive)))));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, List<String> propertiesToSensitive) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(new HashSet<>(propertiesToSensitive))));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, String dateFormat, List<String> propertiesToSensitive) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(new HashSet<>(propertiesToSensitive))));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, Set<String> propertiesToSensitive) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(propertiesToSensitive)));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, String dateFormat, Set<String> propertiesToSensitive) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(propertiesToSensitive)));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, Set<String> propertiesToSensitive, Set<String> propertiesToExclude) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(propertiesToSensitive, propertiesToExclude)));

        return doToJson(objectMapper, o);
    }

    public static String toJsonInSensitiveMode(Object o, String dateFormat, Set<String> propertiesToSensitive, Set<String> propertiesToExclude) {
        ObjectMapper objectMapper = getObjectMapperInSensitiveModel();
        objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));
        objectMapper.setFilterProvider(new SimpleFilterProvider().setDefaultFilter(CustomBeanPropertyFilter.newInstance(propertiesToSensitive, propertiesToExclude)));

        try {
            return objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new JsonException("To json error.", e);
        }
    }

    public static String toJsonInSensitiveModeWhenLog(Object o) {
        try {
            return OBJECT_MAPPER_TO_THREAD_SAFE_IN_SENSITIVE_MODE_WHEN_LOG.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new JsonException("To json error.", e);
        }
    }

    public static <T> T parseJson(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER_TO_THREAD_SAFE.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new JsonException("Parse json error.", e);
        }
    }

    public static Map<String, Object> parseJson2Map(String json) {
        return parseJson2Map(json, Object.class);
    }

    public static <V> Map<String, V> parseJson2Map(String json, Class<V> valueClazz) {
        return parseJson(json, TypeFactory.defaultInstance().constructMapType(LinkedHashMap.class, String.class, valueClazz));
    }

    public static List parseJson2List(String json) {
        return parseJson2List(json, Object.class);
    }

    public static <E> List<E> parseJson2List(String json, Class<E> clazz) {
        return parseJson(json, TypeFactory.defaultInstance().constructCollectionType(List.class, clazz));
    }

    public static boolean isJson(String content) {
        try {
            OBJECT_MAPPER_TO_THREAD_SAFE.readTree(content);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    private static <T> T parseJson(String json, JavaType valueType) {
        try {
            return OBJECT_MAPPER_TO_THREAD_SAFE.readValue(json, valueType);
        } catch (JsonProcessingException e) {
            throw new JsonException("Parse json error.", e);
        }
    }

    private static String doToJson(ObjectMapper objectMapper, Object o) {
        try {
            return objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new JsonException("To json error.", e);
        }
    }


    private static ObjectMapper getObjectMapper() {
        // TODO 使用对象池
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setDateFormat(new SimpleDateFormat(DEFAULT_DATE_FORMAT))
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        return objectMapper;
    }

    private static ObjectMapper getObjectMapperInSensitiveModel() {
        ObjectMapper objectMapper = getObjectMapper();
        objectMapper.addMixIn(Object.class, BeanPropertyFilter.class);
        return objectMapper;
    }

    @JsonFilter("BeanPropertyFilter")
    interface BeanPropertyFilter {}

    static class CustomBeanPropertyFilter implements PropertyFilter, Serializable {

        private final Set<String> propertiesToSensitive;

        private final Set<String> propertiesToExclude;

        public CustomBeanPropertyFilter(Set<String> propertiesToSensitive) {
            this(propertiesToSensitive, Collections.EMPTY_SET);
        }

        public CustomBeanPropertyFilter(Set<String> propertiesToSensitive, Set<String> propertiesToExclude) {
            this.propertiesToSensitive = Collections.unmodifiableSet(propertiesToSensitive);
            this.propertiesToExclude = Collections.unmodifiableSet(propertiesToExclude);
        }

        @Override
        public void serializeAsField(Object pojo, JsonGenerator gen, SerializerProvider prov, PropertyWriter writer) throws Exception {
            if (include(writer)) {
                if (isSensitive(writer)) {
                    gen.writeObjectField(writer.getName(), "*");
                } else {
                    writer.serializeAsField(pojo, gen, prov);
                }
            } else if (!gen.canOmitFields()) { // since 2.3
                writer.serializeAsOmittedField(pojo, gen, prov);
            }
        }

        @Override
        public void serializeAsElement(Object elementValue, JsonGenerator gen, SerializerProvider prov, PropertyWriter writer) throws Exception {
            if (includeElement(elementValue)) {
                if (isSensitiveElement(elementValue)) {
                    gen.writeString("*");
                } else {
                    writer.serializeAsElement(elementValue, gen, prov);
                }
            }
        }

        @Deprecated
        @Override
        public void depositSchemaProperty(PropertyWriter writer, ObjectNode propertiesNode, SerializerProvider provider) throws JsonMappingException {
            if (include(writer)) {
                writer.depositSchemaProperty(propertiesNode, provider);
            }
        }

        @Override
        public void depositSchemaProperty(PropertyWriter writer, JsonObjectFormatVisitor objectVisitor, SerializerProvider provider) throws JsonMappingException {
            if (include(writer)) {
                writer.depositSchemaProperty(objectVisitor, provider);
            }
        }

        public static CustomBeanPropertyFilter newInstance(Set<String> propertiesToSensitive) {
            return new CustomBeanPropertyFilter(propertiesToSensitive);
        }

        public static CustomBeanPropertyFilter newInstance(String ...propertiesToSensitive) {
            return new CustomBeanPropertyFilter(new HashSet<>(Arrays.asList(propertiesToSensitive)));
        }

        public static CustomBeanPropertyFilter newInstance(Set<String> propertiesToSensitive, Set<String> propertiesToExclude) {
            return new CustomBeanPropertyFilter(propertiesToSensitive, propertiesToExclude);
        }

        private boolean isSensitive(PropertyWriter writer) {
            return propertiesToSensitive.contains(writer.getName());
        }

        private boolean isSensitiveElement(Object elementValue) {
            return false;
        }

        private boolean include(PropertyWriter writer) {
            return !propertiesToExclude.contains(writer.getName());
        }

        private boolean includeElement(Object elementValue) {
            return true;
        }
    }
}
