package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.Extension;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.Validity;
import org.bouncycastle.asn1.cmp.PKIHeader;

import java.util.List;

/**
 * 密钥更新业务的信息体
 * <AUTHOR>
 * @date 2020-04-13 17:36
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Getter
public class UpdateKeyReqMessage extends KeyUpdateReqMessage {
    /**
     * 用户公钥
     */
    private byte[] publicKey;

    /**
     * 用户私钥信息
     */
    private byte[] privateKey;

    /**
     * 证书有效期
     */
    private Validity validity;

    /**
     * 主题项信息，用于密钥更新
     */
    private SubjectInfo subject;


    /**
     * 自定义的扩展信息
     */
    private CustomFreeText customFreeText;

    /**
     * 扩展
     */
    private List<Extension> requestExtensions;

    /**
     * 初始化默认参数
     *
     * @return {@link UpdateKeyReqMessage}
     */
    public static UpdateKeyReqMessage defaultMessage(){
        return UpdateKeyReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.UPDATE_KEY)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
