package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import org.bouncycastle.crypto.digests.*;

import java.util.function.Function;

public enum HashEnum {

    MD5(4096, "md5", (content) -> {
        MD5Digest digest = new MD5Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SHA1(8192, "sha1", (content) -> {
        SHA1Digest digest = new SHA1Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SHA224(12288, "sha224", (content) -> {
        SHA224Digest digest = new SHA224Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SHA256(16384, "sha256", (content) -> {
        SHA256Digest digest = new SHA256Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SHA384(20480, "sha384", (content) -> {
        SHA384Digest digest = new SHA384Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SHA512(24576, "sha512", (content) -> {
        SHA512Digest digest = new SHA512Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }),

    SM3(28672, "sm3", (content) -> {
        SM3Digest digest = new SM3Digest();
        digest.update(content, 0, content.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    });

    private Integer code;
    private String description;
    private Function<byte[], byte[]> computeFunction;

    private HashEnum(Integer code, String description, Function<byte[], byte[]> computeFunction) {
        this.code = code;
        this.description = description;
        this.computeFunction = computeFunction;
    }

    public static HashEnum getHashEum(int hashAlgo) {

        if (hashAlgo == HashEnum.MD5.code) {
            return HashEnum.MD5;
        } else if (hashAlgo == HashEnum.SHA1.code) {
            return HashEnum.SHA1;
        } else if (hashAlgo == HashEnum.SHA224.code) {
            return HashEnum.SHA224;
        } else if (hashAlgo == HashEnum.SHA256.code) {
            return HashEnum.SHA256;
        } else if (hashAlgo == HashEnum.SHA384.code) {
            return HashEnum.SHA384;
        } else if (hashAlgo == HashEnum.SHA512.code) {
            return HashEnum.SHA512;
        } else if (hashAlgo == HashEnum.SM3.code) {
            return HashEnum.SM3;
        } else {
            throw new CloudKeyRuntimeException("不支持的hash算法");
        }

    }

    public byte[] compute(byte[] content) {
        return this.computeFunction.apply(content);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}