package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @date 2021-04-19 10:36
 */
public enum SmsAuthModeEnum implements IBaseEnum {
    /**
     * 通用模式
     */
    ONLY_SHOW_AUTHORIZED_NAME(0, "只显示被授权人姓名"),
    /**
     * uid 对于医院来说，通常是工号
     */
    SHOW_AUTHORIZED_NAME_AND_UID(1, "显示被授权人姓名和工号");


    SmsAuthModeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
