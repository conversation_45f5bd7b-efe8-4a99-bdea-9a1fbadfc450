package net.netca.cloudkey.base.dto.district;

import lombok.*;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

/**
 * 院区管理 - 分页查询院区 Request DTO
 *
 * <AUTHOR>
 * @date 2024/2/28 19:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class DistrictPageReqDTO extends DistrictPageConditionDTO implements ISignature {

    private transient OperatorSignature operatorSignature;

}
