package net.netca.cloudkey.base.dto.redis;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 过期的延迟队列DTO
 * @Date Created in 10:36 2024/11/25
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpireDelayQueueDTO implements Serializable {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 任务过期时间，用于延迟队列排序。格式：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime expireTime;
}
