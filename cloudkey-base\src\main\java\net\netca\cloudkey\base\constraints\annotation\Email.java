package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.EmailValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对手机号码的校验注解
 * @see EmailValidator
 */
@Documented
@Constraint(validatedBy = EmailValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Email {

    String message() default "email格式错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
