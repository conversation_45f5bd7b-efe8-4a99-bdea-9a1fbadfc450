package net.netca.cloudkey.base.dto.thirdsystem;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.util.CommonUtil;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: dyr
 * @CreateDate: 2020/6/18 11:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThirdSystemDTO {

    private String cloudkeyProjectId;

    private String signCert;

    private int id;

    private Integer districtId;

    private String coordPushUrl;

    public String selectProjectId(String reqProject) {
        if (CommonUtil.isStringEmpty(this.cloudkeyProjectId)) {
            return null;
        }

        List<String> project = CommonUtil.parseArray(this.cloudkeyProjectId, String.class);
        if (!CommonUtil.isStringEmpty(reqProject)) {
            if (project.stream().anyMatch(reqProject::equals)) {
                return reqProject;
            }
            throw new CloudKeyRuntimeException("不允许请求使用该项目");
        } else {
            if (project.size() == 1) {
                return project.get(0);
            }
            throw new CloudKeyRuntimeException("请求参数不带cloudkeyProjectId，第三方配置不允许配置多个项目");
        }
    }
}


