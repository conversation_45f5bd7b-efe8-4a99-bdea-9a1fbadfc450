package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 云密钥业务业务类型
 */
public enum CloudkeyRequestTypeConstant implements IBaseEnum {
	REQ_TYPE_APPLY_CERT(0, "新申请证书"),
	REQ_TYPE_RENEWAL_CERT(1, "证书续期"),
	REQ_TYPE_UPDATE_CERT(2, "证书更新"),
	REQ_TYPE_LOSS_CERT(3, "证书挂失"),
	REQ_TYPE_REVOKE_CERT(4, "证书注销"),
	REQ_TYPE_UNLOCK(5,"解锁"),
	REQ_TYPE_CHANGE_PIN(6,"修改PIN码"),
	REQ_TYPE_CHANGE_CERT(7,"证书变更");


	private int code;
	private String description;

	CloudkeyRequestTypeConstant(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}


	public static String getDescByCode(Integer code) {
		for (CloudkeyRequestTypeConstant value : CloudkeyRequestTypeConstant.values()) {
			if (value.getCode().equals(code)) {
				return value.getDescription();
			}
		}
		return null;
	}

}
