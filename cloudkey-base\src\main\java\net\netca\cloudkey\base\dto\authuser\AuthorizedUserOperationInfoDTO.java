package net.netca.cloudkey.base.dto.authuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;
import net.netca.cloudkey.base.constant.AuthorizeOperationConstant;
import net.netca.cloudkey.base.constant.AuthorizeStatusConstant;
import net.netca.cloudkey.base.util.CommonUtil;

import java.util.Date;
import java.util.List;

/**
 * created by zaj on 2019/4/24
 */
@Secret
public class AuthorizedUserOperationInfoDTO {

    private Integer id;

    private String name;

    private Integer authStatus;

    private Integer certId;

    @SecurityDataField
    private String phone;

    private boolean allowAuthorized;

    private boolean allowReAuthorized;

    private boolean allowUnAuthorized;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;


    public AuthorizedUserOperationInfoDTO() {
    }


    /**
     * 对象实例化后，调用该方法可以初始化操作许可
     */
    public void initAllowOper() {
        this.allowAuthorized = false;
        this.allowReAuthorized = false;
        this.allowUnAuthorized = false;

        List<AuthorizeOperationConstant> allowOperations = AuthorizeStatusConstant.getAuthorizeStatusConstant(authStatus).getAllowOperations();

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_COFIRM)){
            this.allowAuthorized = true;
        }

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_UNLOCK)){
            this.allowReAuthorized = true;
        }

        if(allowOperations.contains(AuthorizeOperationConstant.AUTHORIZE_OPERATION_FREEZE)){
            this.allowUnAuthorized = true;
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(Integer authStatus) {
        this.authStatus = authStatus;
        initAllowOper();
    }


    public boolean isAllowAuthorized() {
        return allowAuthorized;
    }

    public boolean isAllowReAuthorized() {
        return allowReAuthorized;
    }

    public boolean isAllowUnAuthorized() {
        return allowUnAuthorized;
    }

    public Date getApplicationTime() {
        return applicationTime;
    }

    public void setApplicationTime(Date applicationTime) {
        this.applicationTime = applicationTime;
    }

    public String getPhone() {
        if (!CommonUtil.isStringEmpty(phone)) {
            return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getCertId() {
        return certId;
    }

    public void setCertId(Integer certId) {
        this.certId = certId;
    }
}
