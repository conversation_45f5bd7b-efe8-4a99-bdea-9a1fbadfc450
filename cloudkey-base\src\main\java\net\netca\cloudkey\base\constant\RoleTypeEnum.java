package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum RoleTypeEnum implements IBaseEnum {

	Operator(0, "操作员"), //
	Auditor(1, "审计员"),
	Admin(2, "管理员");

	private Integer code;
	private String description;

	RoleTypeEnum(Integer code, String description) {
		this.code = code;
		this.description = description;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public Integer getCode() {
		return code;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}

}
