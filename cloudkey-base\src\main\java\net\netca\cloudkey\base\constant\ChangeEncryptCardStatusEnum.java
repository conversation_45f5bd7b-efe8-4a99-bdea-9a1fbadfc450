package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 更换密码卡的状态
 * <AUTHOR>
 */
public enum ChangeEncryptCardStatusEnum implements IBaseEnum {

    NOT_CHANGE(0, "未更换"),
    FINISH_CHANGE(1, "已更换");

    private Integer code;
    private String description;

    ChangeEncryptCardStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }


}
