package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @Description  云密钥业务类型
 * @Date 10:52 2020/12/7
 **/
public enum BusinessTypeEnum implements IBaseEnum {

    USER_REGISTER(0,"用户申请"),
    CERT_RENEWAL(1,"证书续期"),
    CERT_REVOKE(2,"证书注销"),
    CERT_CHANGE(3,"证书变更"),
    CERT_UPDATE(4,"证书密钥更新");

    private Integer code;
    private String description;

    private BusinessTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
