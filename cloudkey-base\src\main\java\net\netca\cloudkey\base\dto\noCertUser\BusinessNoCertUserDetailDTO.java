package net.netca.cloudkey.base.dto.noCertUser;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.io.Serializable;
import java.util.Date;

/**
 * 说明： 暂时贴图签章用户 后面可以转化为证书用户
 *
 * <AUTHOR>
 * @date 2024-02-21 10:23:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessNoCertUserDetailDTO{
    private static final long serialVersionUID = 235235434L;
    private Integer id;
    /**
     * 用户唯一标识符
     */
    private String uid;
    private String name;
    private String phone;

    private String province;
    private String city;
    private String officialResidence;
    private String countryName;
    private Integer gender;
    private Integer identityType;
    private String identity;
    private String email;
    private Integer organizationId;

    private String department;

    private String occupation;

    private String cloudkeyProjectId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
    /**
     * 冗余config_project的cert_user_type
     */
    private Integer certUserType;

    private String pendingPicData;

    private Integer pendingPicStatus;

    private String picData;

}
