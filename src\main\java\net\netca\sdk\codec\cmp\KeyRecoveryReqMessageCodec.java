package net.netca.sdk.codec.cmp;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.execption.CmpDecodeException;
import net.netca.sdk.execption.CmpEncodeException;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.KeyRecoveryReqMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.bouncycastle.asn1.cmp.PKIBody;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

import java.util.Objects;

/**
 * {@link KeyRecoveryReqMessage} 的编解码器
 *
 * @author: zys
 * @date: 2020/3/13 18:32
 */
@CommonsLog
public class KeyRecoveryReqMessageCodec extends CmpMessageCodec {

    private static KeyRecoveryReqMessageEncoder encoder;

    private static KeyRecoveryReqMessageDecoder decoder;

    public static KeyRecoveryReqMessageCodec createEncoder(CmpMessageConfigManagement configManagement) {
        encoder = new KeyRecoveryReqMessageEncoder(configManagement);
        return new KeyRecoveryReqMessageCodec();
    }

    public static KeyRecoveryReqMessageCodec createDecoder(CmpMessageConfigManagement configManagement) {
        decoder = new KeyRecoveryReqMessageDecoder(configManagement);
        return new KeyRecoveryReqMessageCodec();
    }

    final static class KeyRecoveryReqMessageEncoder extends CmpMessageToAnyObjectEncoder {
        public KeyRecoveryReqMessageEncoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void encode(RequestContext context, CmpMessage cmpMessage) throws CmpEncodeException {
            checkEncodeArgs(context, cmpMessage);
            if (context instanceof SingleCertReqContext && cmpMessage instanceof KeyRecoveryReqMessage) {
                KeyRecoveryReqMessage keyRecoveryReqMessage = (KeyRecoveryReqMessage) cmpMessage;
                SingleCertReqContext singleCertReqContext = ((SingleCertReqContext) context);
                try {
                    singleCertReqContext.setEncodeResult(generateKeyRecoveryReqData(keyRecoveryReqMessage));
                } catch (Exception e) {
                    throw new CmpEncodeException(e.getMessage(), e);
                }
            }
        }

        public PKIMessage generateKeyRecoveryReqData(KeyRecoveryReqMessage keyRecoveryReqMessage) throws Exception {
            PKIMessageGenerator.PKIHeaderParams pkiHeaderParams =
                    PKIMessageGenerator.PKIHeaderParams.newInstanceBuilder(keyRecoveryReqMessage)
                            .customFreeText(keyRecoveryReqMessage.getCustomFreeText())
                            .build();
            PKIHeader pkiHeader = pkiMessageGenerator.generatePKIHeader(pkiHeaderParams);
            byte[] publicKey = keyRecoveryReqMessage.getPublicKey();
            Objects.requireNonNull(publicKey, "keyRecoveryReqMessage.publicKey isn't allowed to be null");
            PKIMessageGenerator.CertReqMessagesParams certReqMessagesParams = PKIMessageGenerator.CertReqMessagesParams.builder()
                    .publicKey(publicKey)
                    .privateKey(keyRecoveryReqMessage.getPrivateKey())
                    .certSn(keyRecoveryReqMessage.getSerialNumber())
                    .issuer(keyRecoveryReqMessage.getIssuer())
                    .certRequestId(keyRecoveryReqMessage.getCertRequestId())
                    .build();
            PKIBody pkiBody = pkiMessageGenerator.generateKrrPKIBody(certReqMessagesParams);
            return pkiMessageGenerator.signPKIMessage(pkiHeader, pkiBody);
        }
    }


    final static class KeyRecoveryReqMessageDecoder extends PKIMessageToCmpRespResultDecoder {
        public KeyRecoveryReqMessageDecoder(CmpMessageConfigManagement configManagement) {
            super(configManagement);
        }

        @Override
        public void decode(RequestContext context, PKIMessage respMessage) throws CmpDecodeException {
            checkDecodeArgs(context, respMessage);
            if (context instanceof SingleCertReqContext) {
                SingleCertReqContext singleCertReqContext = (SingleCertReqContext) context;
                CmpMessage cmpReqMessage = singleCertReqContext.getCmpReqMessage();
                if ((cmpReqMessage instanceof KeyRecoveryReqMessage || cmpReqMessage instanceof NestedMessage) &&
                        BusinessTypeEnum.RECOVERY.equals(cmpReqMessage.getBusinessTypeEnum())) {
                    try {
                        dealWithKeyRecoveryRespMessage(respMessage, singleCertReqContext);
                        singleCertReqContext.setDecoded(true);
                    } catch (Exception e) {
                        throw new CmpDecodeException(e.getMessage(), e);
                    }
                }
            }
        }
    }

    @Override
    public void decode(RequestContext context, PKIMessage message) throws CmpDecodeException {
        decoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, CmpMessage message) throws CmpEncodeException {
        encoder.encode(context, message);
    }
}
