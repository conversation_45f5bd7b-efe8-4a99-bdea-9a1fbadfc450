package net.netca.sdk.codec.cmp;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.util.Preconditions;
import net.netca.sdk.util.json.JsonUtils;
import net.netca.sdk.validator.PKIMessageValidator;
import org.bouncycastle.asn1.ASN1BitString;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.DERBitString;
import org.bouncycastle.asn1.DERUTF8String;
import org.bouncycastle.asn1.cmp.*;
import org.bouncycastle.asn1.crmf.EncryptedValue;
import org.bouncycastle.asn1.x509.Certificate;

import java.math.BigInteger;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: zys
 * @date: 2020/3/9 16:55
 */

public abstract class CmpMessageCodec implements MessageEncoder<CmpMessage>, MessageDecoder<PKIMessage> {


    @CommonsLog
    abstract static class CmpMessageToAnyObjectEncoder implements MessageEncoder<CmpMessage> {

        PKIMessageGenerator pkiMessageGenerator;

        public void destroy() {
            // good to gc
            pkiMessageGenerator.destroy();
        }

        public CmpMessageToAnyObjectEncoder(CmpMessageConfigManagement configManagement) {
            Objects.requireNonNull(configManagement, "configManagement isn't allow to be null");
            configManagement.verifyEncodeValid();
            this.pkiMessageGenerator = new PKIMessageGenerator(configManagement);
        }

        void checkEncodeArgs(RequestContext context, CmpMessage cmpMessage) {
            Objects.requireNonNull(context, "context isn't allow to be null");
            Objects.requireNonNull(cmpMessage, "cmpMessage isn't allow to be null");
        }
    }

    @CommonsLog
    abstract static class PKIMessageToCmpRespResultDecoder implements MessageDecoder<PKIMessage> {

        X509Certificate communicationCert;


        public PKIMessageToCmpRespResultDecoder(CmpMessageConfigManagement configManagement) {
            Objects.requireNonNull(configManagement, "configManagement isn't allow null");
            configManagement.verifyDecodeValid();
            this.communicationCert = configManagement.getCommunicationCert();
        }

        public void destroy() {
            // good to gc
            this.communicationCert = null;
        }


        void checkDecodeArgs(RequestContext context, PKIMessage decodedMsg) {
            Objects.requireNonNull(context, "context isn't allow to be null");
            Objects.requireNonNull(decodedMsg, "decodeMsg isn't allow to be null");
        }

        /**
         * @param singleCertReqContext
         * @param respMessage
         * @return Certificate[]{ signCert, encCert }
         * @throws Exception
         */
        void dealWithP10CertReqOrKeyUpdateRespPKIMessage(SingleCertReqContext singleCertReqContext, PKIMessage respMessage) throws Exception {
            PKIMessage pkiMessage = singleCertReqContext.getEncodeResult();
            CmpMessage cmpReqMessage = singleCertReqContext.getCmpReqMessage();
            long certReqId = 0;
            if (cmpReqMessage instanceof NestedMessage) {
                NestedMessage nestedMessage = (NestedMessage) cmpReqMessage;
                certReqId = nestedMessage.getInlineMessage().getCertRequestId();
            } else {
                certReqId = cmpReqMessage.getCertRequestId();
            }
            CmpRespResult cmpRespResult = singleCertReqContext.getCmpRespResult();
            net.netca.pki.encoding.asn1.pki.PublicKey publicKey = communicationCert.getSubjectPublicKeyInfo().getPublicKey();
            PKIMessageValidator.checkRespHeader(respMessage, pkiMessage.getHeader(), publicKey);
            CertResponse[] certResponses =
                    CertRepMessage.getInstance(respMessage.getBody().getContent())
                            .getResponse();
            CertResponse signCertResponse = certResponses[0];
            Objects.requireNonNull(signCertResponse);
            PKIStatusInfo status = signCertResponse.getStatus();
            int statusCode = status.getStatus().intValue();
            if (PKIStatusEnum.ACCEPTED.getCode() == statusCode) {
                Long respCertReqId = Optional.of(signCertResponse.getCertReqId())
                        .map(ASN1Integer::getValue)
                        .map(BigInteger::longValue)
                        .orElseThrow(NullPointerException::new);
                if (certReqId != respCertReqId) {
                    throw new Exception("请求的certReqId与响应的certReqId不一致！");
                }
                cmpRespResult.setRespCertRequestId(respCertReqId);
                String customFreeTextJson = Optional.ofNullable(respMessage)
                        .map(PKIMessage::getHeader)
                        .map(PKIHeader::getFreeText)
                        .map(pkiFreeText -> pkiFreeText.getStringAt(0))
                        .map(DERUTF8String::getString)
                        .orElse("{}");
                CustomFreeText customFreeText = JsonUtils.parseJson(customFreeTextJson, CustomFreeText.class);
                cmpRespResult.setRevokedSigCertSn(customFreeText.getRevokedSigCertSn());
                CMPCertificate cmpCertificate = Optional.of(signCertResponse.getCertifiedKeyPair())
                        .map(CertifiedKeyPair::getCertOrEncCert)
                        .map(CertOrEncCert::getCertificate)
                        .get();
                X509Certificate signCert = new X509Certificate(cmpCertificate.getX509v3PKCert().getEncoded());
                Objects.requireNonNull(signCert, "signCert is not allow to be null");
                if (certResponses.length > 1) {
                    CertifiedKeyPair encCertifiedKeyPair = certResponses[1].getCertifiedKeyPair();
                    Objects.requireNonNull(encCertifiedKeyPair, "encCertifiedKeyPair is null");
                    Certificate certificate = Optional.of(encCertifiedKeyPair.getCertOrEncCert()).map(CertOrEncCert::getCertificate).map(CMPCertificate::getX509v3PKCert).get();
                    X509Certificate encCert = new X509Certificate(certificate.getEncoded());
                    Objects.requireNonNull(encCert, "encCert is not allow to be null");
                    cmpRespResult.setEncCertPem(encCert.pemEncode());
                    cmpRespResult.setEncCertDer(encCert.derEncode());
                    EncryptedValue privateKey = encCertifiedKeyPair.getPrivateKey();
                    byte[] encPrivateKey = Optional.ofNullable(privateKey)
                            .map(EncryptedValue::getEncValue)
                            .map(ASN1BitString::getBytes)
                            .orElse(null);
                    cmpRespResult.setEncPrivateKey(encPrivateKey);
                }
                cmpRespResult.setSignCertPem(signCert.pemEncode());
                cmpRespResult.setSignCertDer(signCert.derEncode());
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.ACCEPTED);
            } else if (PKIStatusEnum.WAITING.getCode() == statusCode) {
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.WAITING);
                cmpRespResult.setRespCertRequestId(certReqId);
            } else {
                dealWithFailure(cmpRespResult, status);
            }
        }

        void dealWithFailure(CmpRespResult cmpRespResult, PKIStatusInfo status) {
            log.error("请求失败的CMP错误码：" + status.getFailInfo().intValue());
            log.error("请求失败的原因：" + status.getStatusString().getStringAt(0).getString());
            cmpRespResult.setPkiStatusEnum(PKIStatusEnum.REJECTION);
            cmpRespResult.setFailureCode(status.getFailInfo().intValue());
            cmpRespResult.setFailureReason(status.getStatusString().getStringAt(0).getString());
        }

        void dealWithRevocationRespPKIMessage(SingleCertReqContext singleCertReqContext, PKIMessage revokeRespPkiMessage) throws Exception {
            CmpRespResult cmpRespResult = singleCertReqContext.getCmpRespResult();
            PKIMessage pkiMessage = singleCertReqContext.getEncodeResult();
            net.netca.pki.encoding.asn1.pki.PublicKey publicKey = new X509Certificate(communicationCert.derEncode()).getSubjectPublicKeyInfo().getPublicKey();
            PKIMessageValidator.checkRespHeader(revokeRespPkiMessage, pkiMessage.getHeader(), publicKey);
            RevRepContent revRepContent = (RevRepContent) revokeRespPkiMessage.getBody().getContent();
            PKIStatusInfo[] pkiStatusInfos = revRepContent.getStatus();
            // 目前只支持一条记录
            Preconditions.checkArgument(pkiStatusInfos.length == 1, "only support one PKIStatusInfo");
            PKIStatusInfo pkiStatusInfo = pkiStatusInfos[0];
            int statusCode = pkiStatusInfo.getStatus().intValue();
            if (PKIStatusEnum.ACCEPTED.getCode() == statusCode) {
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.ACCEPTED);
                log.debug(singleCertReqContext.getCmpReqMessage().getBusinessTypeEnum().getDescription() + "请求成功！");
            } else if (PKIStatusEnum.WAITING.getCode() == statusCode) {
                // 待审核
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.WAITING);
                cmpRespResult.setRespCertRequestId(singleCertReqContext.getCmpReqMessage().getCertRequestId());
                log.debug(pkiStatusInfo.getStatusString().getStringAt(0).getString());
            } else {
                // 业务请求失败
                dealWithFailure(cmpRespResult, pkiStatusInfo);
            }
        }


        void dealWithKeyRecoveryRespMessage(PKIMessage keyRecoveryRespMessage, SingleCertReqContext singleCertReqContext) throws Exception {
            CmpMessage cmpReqMessage = singleCertReqContext.getCmpReqMessage();
            PKIMessage pkiMessage = singleCertReqContext.getEncodeResult();
            boolean isNested = cmpReqMessage instanceof NestedMessage;
            long certReqId = isNested ? ((NestedMessage) cmpReqMessage).getInlineMessage().getCertRequestId()
                    : cmpReqMessage.getCertRequestId();
            CmpRespResult cmpRespResult = singleCertReqContext.getCmpRespResult();
            net.netca.pki.encoding.asn1.pki.PublicKey publicKey = new X509Certificate(communicationCert.derEncode()).getSubjectPublicKeyInfo().getPublicKey();

            if (isNested) {
                PKIMessage inlinePkiMessage = ((PKIMessages) pkiMessage.getBody().getContent()).toPKIMessageArray()[0];
                PKIMessageValidator.checkRespHeaderForNested(keyRecoveryRespMessage, pkiMessage.getHeader(), inlinePkiMessage.getHeader(), publicKey);
            } else {
                PKIMessageValidator.checkRespHeader(keyRecoveryRespMessage, pkiMessage.getHeader(), publicKey);
            }
            String customFreeText = Optional.of(keyRecoveryRespMessage).map(PKIMessage::getHeader)
                    .map(PKIHeader::getFreeText)
                    .map(pkiFreeText -> pkiFreeText.getStringAt(0))
                    .map(DERUTF8String::getString)
                    .orElseThrow(NullPointerException::new);
            Long respCertReqId = JsonUtils.parseJson(customFreeText, CustomFreeText.class).getCertReqId();
            PKIBody respBody = keyRecoveryRespMessage.getBody();
            KeyRecRepContent keyRecRepContent = KeyRecRepContent.getInstance(respBody.getContent());
            Objects.requireNonNull(keyRecRepContent);
            PKIStatusInfo status = keyRecRepContent.getStatus();
            Objects.requireNonNull(status);
            int statusCode = Optional.of(status.getStatus())
                    .map(BigInteger::intValue)
                    .get();
            if (PKIStatusEnum.ACCEPTED.getCode() == statusCode) {
                // 检查certReqId
                if (certReqId != respCertReqId) {
                    throw new Exception("certReqId 前后不一致！");
                }
                cmpRespResult.setRespCertRequestId(respCertReqId);
                X509Certificate signCert = new X509Certificate(keyRecRepContent.getNewSigCert().getX509v3PKCert().getEncoded());
                Objects.requireNonNull(signCert, "signCert is not allow to be null");
                log.debug(signCert.pemEncode());
                log.debug("signCert's issuer：" + signCert.getIssuer());
                log.debug("signCert's serialNumber：" + Hex.encode(false, signCert.getSerialNumber()));
                CertifiedKeyPair[] keyPairHist = keyRecRepContent.getKeyPairHist();
                if (keyPairHist != null && keyPairHist.length == 1) {
                    // 获取加密证书
                    Certificate encCertificate = Optional.of(keyPairHist[0].getCertOrEncCert()).map(CertOrEncCert::getCertificate).map(CMPCertificate::getX509v3PKCert).get();
                    X509Certificate encCert = new X509Certificate(encCertificate.getEncoded());
                    Objects.requireNonNull(encCert, "encCert is not allow to be null");
                    // 获取加密密钥对
                    cmpRespResult.setEncCertDer(encCert.derEncode());
                    cmpRespResult.setEncCertPem(encCert.pemEncode());
                    byte[] encPrivateKey = Optional.of(keyPairHist[0].getPrivateKey()).map(EncryptedValue::getEncValue).map(DERBitString::getBytes).get();
                    cmpRespResult.setEncPrivateKey(encPrivateKey);
                }
                cmpRespResult.setSignCertDer(signCert.derEncode());
                cmpRespResult.setSignCertPem(signCert.pemEncode());
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.ACCEPTED);
            } else if (PKIStatusEnum.WAITING.getCode() == statusCode) {
                // 待审核
                cmpRespResult.setPkiStatusEnum(PKIStatusEnum.WAITING);
                cmpRespResult.setRespCertRequestId(certReqId);
                log.debug(status.getStatusString().getStringAt(0).getString());
            } else {
                // 业务请求失败
                dealWithFailure(cmpRespResult, status);
            }
        }
    }
}
