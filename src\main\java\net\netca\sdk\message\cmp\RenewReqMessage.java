package net.netca.sdk.message.cmp;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.Validity;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * <AUTHOR>
 * @date 2020-04-13 17:32
 */

@SuperBuilder(toBuilder = true)
@Getter
public class RenewReqMessage extends KeyUpdateReqMessage {
    /**
     * 证书有效期
     */
    private Validity validity;


    public static RenewReqMessage defaultMessage(){
        return RenewReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.RENEWAL)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
