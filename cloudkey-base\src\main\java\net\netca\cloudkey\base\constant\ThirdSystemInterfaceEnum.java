package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ThirdSystemInterfaceEnum implements IBaseEnum {

    //
    ORG_REGISTER(1, "机构信息注册"),
    USER_REGISTER(2, "用户信息注册"),
    USER_UNLOCK(3, "用户解锁"),
    UPLOAD_SEAL_PIC(4, "上传签章图片"),
    CERT_RENEWAL(5, "证书续期"),
    CERT_REVOKE(6, "证书注销"),
    CERT_FREEZE(7, "证书冻结"),
    CERT_UNFREEZE(8, "证书解冻"),

    EVENTCERT_APPLY(9,"事件证书签发接口"),
    EVENTCERT_APPLY_SIGN(10,"事件证书签发并签名接口"),
    EVENTCERT_SIGN(11,"事件证书签名接口"),
    USER_UPDATE(12, "用户信息修改"),
    CERT_RESERVE_CANCEL(13, "证书预约取消"),
    CERT_LIST(14, "证书列表查询"),

    THIRD_SYSTEM_LOG_QUERY(15, "第三方系统日志查询"),

    // 协同签名相关的接口 暂定5000 - 5999 范围内
    COOP_SIGN_TEXT_TASK_CREATE_EDIT(5001, "创建/编辑文本协同签署任务接口"),
    COOP_SIGN_TASK_REVOKE(5002, "取消协同签署任务接口"),
    COOP_SIGN_TASK_GET(5003, "查询协同签署任务接口"),
    COOP_SIGN_TASK_PUSH(5004, "推送协同签署任务接口"),
    COOP_SIGN_TASK_UNSIGNED_GET(5005, "签署者待签任务列表接口"),
    COOP_SIGN_FILE_TASK_CREATE_EDIT(5006, "创建/编辑文件协同签署任务接口"),
    COOP_SIGN_TEXT_TASK_CREATE_EDIT_WITH_TPL(5007, "创建/编辑文本协同签署任务接口（模板方式）"),
    COOP_SIGN_FILE_TASK_CREATE_EDIT_WITH_TPL(5008, "创建/编辑文件协同签署任务接口（模板方式）"),
    COOP_SIGN_TASK_COMMIT(5009, "发起协同签署任务接口"),
    COOP_SIGN_TASK_TEMPLATE_CREATE_EDIT(5010, "创建/编辑协同签署模板接口"),
    COOP_SIGN_TASK_TEMPLATE_DETAIL(5011, "查看协同签署模板详情接口"),
    COOP_SIGN_TASK_TEMPLATE_DIAGRAM(5012, "获取查看流程图令牌接口"),
    ;

    private final Integer code;
    private final String description;

    private static List<Integer> coorpServerList = new ArrayList<>();

    static {
        coorpServerList.add(COOP_SIGN_TEXT_TASK_CREATE_EDIT.getCode());
        coorpServerList.add(COOP_SIGN_TASK_REVOKE.getCode());
        coorpServerList.add(COOP_SIGN_TASK_GET.getCode());
        coorpServerList.add(COOP_SIGN_TASK_UNSIGNED_GET.getCode());
        coorpServerList.add(COOP_SIGN_FILE_TASK_CREATE_EDIT.getCode());
        coorpServerList.add(COOP_SIGN_TEXT_TASK_CREATE_EDIT_WITH_TPL.getCode());
        coorpServerList.add(COOP_SIGN_FILE_TASK_CREATE_EDIT_WITH_TPL.getCode());
        coorpServerList.add(COOP_SIGN_TASK_COMMIT.getCode());
        coorpServerList.add(COOP_SIGN_TASK_TEMPLATE_CREATE_EDIT.getCode());
        coorpServerList.add(COOP_SIGN_TASK_TEMPLATE_DETAIL.getCode());
        coorpServerList.add(COOP_SIGN_TASK_TEMPLATE_DIAGRAM.getCode());
    }

    /**
     * 返回是否是作为协同签名系统的服务端
     */
    public static boolean isCoorpServer(Integer code) {
        return coorpServerList.contains(code);
    }

}
