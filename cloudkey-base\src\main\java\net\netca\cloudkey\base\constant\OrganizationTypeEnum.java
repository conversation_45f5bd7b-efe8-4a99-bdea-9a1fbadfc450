package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.EnumUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * 证书类型
 *
 * <AUTHOR>
 * @since 2018年10月17日12:51:46
 */
public enum OrganizationTypeEnum implements IBaseEnum {

    ENTERPRISE(1, "企业"), //
    INSTITITUTE(2, "事业"), //
    NATIONALORGANS(3, "国家机关"),
    COMMUNITYGROUP(4, "社会团体"), //
    CIVILGROUP(5, "人民团体"), //
    OTHERS(6, "其它");//;

    private Integer code;
    private String description;

    private OrganizationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(OrganizationTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = linkedHashMap.get("value");
                break;
            }
        }
        return obj.toString();
    }

}
