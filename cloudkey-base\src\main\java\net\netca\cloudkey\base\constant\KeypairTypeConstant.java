package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.pki.KeyPair;

/**
 * 密钥对
 */
public enum KeypairTypeConstant implements IBaseEnum {
	KEYPAIR_TYPE_ENC(KeyPair.ENCRYPT, "加密密钥对"),
	KEYPAIR_TYPE_SIGN(KeyPair.SIGNATURE, "签名密钥对");

	private int code;
	private String description;

	private KeypairTypeConstant(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

}
