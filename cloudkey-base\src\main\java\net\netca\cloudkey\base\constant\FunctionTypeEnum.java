package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum FunctionTypeEnum implements IBaseEnum {
    //

    SERVICE_CONFIG(0, "服务配置功能（配置app初始化二维码内容）",ConfigKeyValueEnum.SERVICE_CONFIG_OPEN.getCode()),
    SERVICE_MANAGE(1, "服务管理功能（重启服务功能）",ConfigKeyValueEnum.SERVICE_MANAGE_OPEN.getCode()),
    SERVICE_SELF_REGISTER(2, "自助申请服务",ConfigKeyValueEnum.SERVICE_SELF_REGISTER_OPEN.getCode()),
    ;
    private Integer code;
    private String description;
    private String configKey;


    FunctionTypeEnum(Integer code, String description, String configKey) {
        this.code = code;
        this.description = description;
        this.configKey = configKey;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public String getConfigKey() {
        return this.configKey;
    }

}
