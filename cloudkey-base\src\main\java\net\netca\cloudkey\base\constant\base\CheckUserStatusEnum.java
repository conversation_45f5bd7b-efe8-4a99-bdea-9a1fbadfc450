package net.netca.cloudkey.base.constant.base;

import net.netca.cloudkey.base.util.EnumUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @Description  检验用户返回的status
 * @Date 17:58 2021/4/1
 * 0 表示成功
 * -1 用户工号或者姓名为空
 * -2 用户工号在HIS中不存在
 * -3 用户工号在HIS中已停用
 * -4 用户工号与姓名不匹配
 **/
public enum CheckUserStatusEnum implements IBaseEnum {

    SUCCESS(0,"成功"),
    EMPTY(-1,"用户工号或者姓名为空"),
    NON_EXIST(-2,"用户工号在HIS中不存在"),
    STOP(-3,"用户工号在HIS中已停用"),
    NO_MATCH(-4,"用户工号与姓名不匹配")
    ;

    private Integer code;
    private String description;

    CheckUserStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        Object obj = "未知";
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CheckUserStatusEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = linkedHashMap.get("value");
                break;
            }
        }
        return obj.toString();
    }

}
