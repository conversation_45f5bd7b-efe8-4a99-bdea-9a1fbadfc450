package net.netca.cloudkey.base.constant;

import lombok.Getter;

import java.util.Arrays;

/**
 * @descriptions:
 * @date: 2019/3/13
 * @author: <PERSON>romChars
 */
@Getter
public enum DataTemplateFieldsEnum {

    // PS 如果增加了员工的字段 记得机构和员工的字段下标的关系 因为公用了一个类，而不是分开
    // 如这里需要处理 net.netca.cloudkey.base.service.BusinessUserService.convertUser

    // 机构和员工公共用字段
    TEMPLATE_UID_INDEX(0,"用户唯一标识")
    ,TEMPLATE_USER_NAME_INDEX(1,"用户名称")
    ,TEMPLATE_USER_PHONE_INDEX(2,"用户手机")
    ,TEMPLATE_USER_EMAIL_INDEX(3,"用户邮箱")
    ,TEMPLATE_USER_PROVINCE_INDEX(4,"用户所在省份")
    ,TEMPLATE_USER_CITY_INDEX(5,"用户所在城市")
    ,TEMPLATE_USER_OFFICIALRESIDENCE_INDEX(6,"用户住址")
    ,TEMPLATE_USER_COUNTRY_INDEX(7,"用户所在国家")
    ,TEMPLATE_USER_IDENTITY_TYPE_INDEX(8,"用户证件类型")
    ,TEMPLATE_USER_IDENTITY_INDEX(9,"用户证件号码")

    // 员工字段
    ,TEMPLATE_USER_GENDER_INDEX(10,"用户性别")
    ,TEMPLATE_DEPARTMENT_INDEX(11,"部门")
    ,TEMPLATE_OCCUPATION_INDEX(12,"职业")

    // 机构和员工公共用字段 上面3个是员工字段 所以下标对于机构 下标10的才是证书到期时间字段
    // net.netca.cloudkey.base.service.BusinessUserService.convertUser
    ,TEMPLATE_CERT_VALIDITY_END_INDEX(13,"证书到期时间 格式yyyy-MM-dd")

    /*,TEMPLATE_ORGAN_IDENTITY_TYPE_INDEX(11,"机构证件类型")
    ,TEMPLATE_ORGAN_IDENTITY_INDEX(12,"机构证件号码")
    ,TEMPLATE_ORGAN_TYPE_INDEX(13,"机构类型")
    ,TEMPLATE_ORGAN_NAME_INDEX(14,"机构名称")
    ,TEMPLATE_ORGAN_OFFICIALRESIDENCE_INDEX(15,"机构地址")
    ,TEMPLATE_ORGAN_TELL_INDEX(16,"机构电话")
    ,TEMPLATE_ORGAN_EMAIL_INDEX(17,"机构邮箱")
    ,TEMPLATE_ORGAN_COUNTRY_INDEX(18,"机构所在国家")
    ,TEMPLATE_ORGAN_PROVINCE_INDEX(19,"机构所在省份")
    ,TEMPLATE_LEGALPERSON_NAME_INDEX(20,"机构法人名称")
    ,TEMPLATE_LEGALPERSON_PHONE_INDEX(21,"机构法人电话")
    ,TEMPLATE_LEGALPERSON_IDENTITY_TYPE_INDEX(22,"机构法人证件类型")
    ,TEMPLATE_LEGALPERSON_IDENTITY_INDEX(23,"机构法人证件号码")*/

    ;
    private final int index;
    private final String description;
    private static int maxIndex;

    DataTemplateFieldsEnum(int index, String description){
        this.index = index;
        this.description = description;
    }

    public static int maxOfIndex() {
        maxIndex = maxIndex == 0
                ? maxIndex = Arrays.stream(DataTemplateFieldsEnum.values())
                .map(dataTemplateFieldsEnum -> dataTemplateFieldsEnum.getIndex())
                .max(Integer::compareTo)
                .orElse(0)
                : maxIndex;
        return maxIndex == 0
                ? maxIndex
                : maxIndex + 1;
    }
}
