package net.netca.cloudkey.base.dto.cert;

import lombok.Data;

/**
 * {
 *     "cert":{
 *         "keySN":"设备序列号",
 *         "keyType":"102,设备类行",
 *         "certSN":"证书序列号-大写",
 *         "issuer":"证书颁发者DN的16进制编码-大写",
 *         "certThumbPrints":[
 *             {
 *                 "algorithm":"16834,//计算微缩图的算法",
 *                 "hash":"证书微缩图16进制编码-大写"
 *             }
 *         ]
 *     }
 * }
   公用类---参数校验请手动校验
 * created by zaj on 2019/1/21
 */
@Data
public class Cert {
    private String keySN;
    private String keyType;
    private String certSN;
    private String issuer;
    private CertThumbPrint[] certThumbPrints;
    private String certContent;
}
