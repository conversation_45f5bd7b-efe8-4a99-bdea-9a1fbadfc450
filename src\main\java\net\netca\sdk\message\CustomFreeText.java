package net.netca.sdk.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-07-04 16:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomFreeText {
    /**
     * 可以使用此值来覆盖 P10中的主题项，所以需要将其设置进 CMP Header的 freeText 中
     */
    private SubjectInfo subjectInfo;

    /**
     * 证书有效期
     */
    private Validity validity;

    /**
     * 替代 CMP 中 POP 的作用，验证 P10 的有效性
     */
    private String p10;

    /**
     * 证书业务请求Id
     */
    private Long certReqId;

    /**
     * 证书用户
     */
    private UserInfo userInfo;

    /**
     * 密钥保护对称算法
     */
    private Integer symmAlgo;

    /**
     * 证书更新业务是否保留加密证书密钥对
     */
    private Boolean keepEncCertKeypair;

    /**
     * 证书更新业务是否注销旧证书
     */
    private Boolean revokeOldCert;

    /**
     * 注销的旧签名证书序列号
     */
    private String revokedSigCertSn;

}
