package net.netca.cloudkey.base.dto.seal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @descriptions:
 * 签章关键字
 * @date: 2019/6/20
 * @author: SaromChars
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SealKeyword {

    private String keyword;

    /**
     * 下标从1开始 支持 -1 代表最后一页 -2 代表倒数第二页 逆序指定
     * 为0时代表签全部
     */
    private Integer keywordIndex = -1;

    private Integer offsetX;

    private Integer offsetY;

    /**
     * 下标从1开始 支持 -1 代表最后一页 -2 代表倒数第二页 逆序指定
     */
    private Integer startPage = 1;

    /**
     * 下标从1开始 支持 -1 代表最后一页 -2 代表倒数第二页 逆序指定
     */
    private Integer endPage = -1;
}
