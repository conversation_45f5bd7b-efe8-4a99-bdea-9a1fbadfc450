package net.netca.cloudkey.base.dto.certalertpolicy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Phone;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2024/3/6 10:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CertAlertPolicyUpdateReqDTO implements ISignature {

    private transient OperatorSignature operatorSignature;

    @NotNull(message = "id 不允许为空")
    private Integer id;

    /**
     * 策略名称
     */
    @NotBlank(message = "name 不允许为空")
    private String name;

    /**
     * 院区ID
     */
    @NotNull(message = "districtId 不允许为空")
    private Integer districtId;

    /**
     * 通知间隔时间（天数）
     */
    @Min(value = 1, message = "通知间隔天数最小值为1")
    @Max(value = 31, message = "通知间隔天数最大值为31")
    @NotNull(message = "notifyInterval 不允许为空")
    private Integer notifyInterval;

    /**
     * 证书过期报警天数
     */
    @Min(value = 1, message = "临近过期天数最小值为1")
    @NotNull(message = "certAlertDays 不允许为空")
    private Integer certAlertDays;

    /**
     * 通知时间
     */
    @NotNull(message = "notifyTime 不允许为空")
    private LocalTime notifyTime;

    /**
     * 通知号码
     */
    @Phone
    @NotBlank(message = "notifyNumber 不允许为空")
    private String notifyNumber;

    /**
     * 短信平台
     */
    @NotBlank(message = "smsPlatform 不允许为空")
    private String smsPlatform;

}
