package net.netca.cloudkey.base.dto.audit;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2023-06-05 17:21:38
 */
@Data
public class GetInfoReqDTO {

    @NotNull(message = "日志编号不允许为空")
    private Integer id;

    @NotNull(message = "请求时间不允许为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date requestTime;
}
