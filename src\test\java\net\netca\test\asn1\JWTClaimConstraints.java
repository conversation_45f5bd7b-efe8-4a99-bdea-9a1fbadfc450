package net.netca.test.asn1;


import net.netca.pki.encoding.asn1.*;
import net.netca.test.asn1.pojo.JWTClaimConstraintsDTO;
import net.netca.test.asn1.pojo.JWTClaimPermittedValues;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-23 11:32
 */
public class JWTClaimConstraints {
    public static final String JWT_CLAIM_CONSTRAINTS_OID = "1.3.6.1.5.5.7.1.27";

    public static SequenceType JWT_CLAIM_CONSTRAINS_TYPE;

    public static SequenceOfType JWT_CLAIM_NAMES_TYPE;
    public static SequenceOfType JWT_CLAIM_PERMITTED_VALUES_LIST_TYPE;
    public static SequenceType JWT_CLAIM_PERMITTED_VALUES_TYPE;
    public static SequenceOfType PERMITTED_TYPE;

    public static TaggedType JWT_CLAIM_NAMES_TAGGED_TYPE;

    public static TaggedType JWT_CLAIM_PERMITTED_VALUES_LIST_TAGGED_TYPE;

    public JWTClaimConstraints(TaggedValue mustInclude, TaggedValue permittedValues) {
        this.mustInclude = mustInclude;
        this.permittedValues = permittedValues;
    }


    private TaggedValue mustInclude;

    private TaggedValue permittedValues;

    static {
        try {
            preCompile();
        } catch (ASN1Exception e) {
            e.printStackTrace();
        }
    }

    private static void preCompile() throws ASN1Exception {
        IA5StringType jwtClaimNameType = IA5StringType.getInstance();
        JWT_CLAIM_NAMES_TYPE = new SequenceOfType(jwtClaimNameType);

        JWT_CLAIM_NAMES_TAGGED_TYPE = new TaggedType(ASN1Object.Context, 0, false, JWT_CLAIM_NAMES_TYPE);

        PERMITTED_TYPE = new SequenceOfType(UTF8StringType.getInstance(), 1);
        JWT_CLAIM_PERMITTED_VALUES_TYPE = new SequenceType();
        JWT_CLAIM_PERMITTED_VALUES_TYPE.add("claim", jwtClaimNameType);
        JWT_CLAIM_PERMITTED_VALUES_TYPE.add("permitted", PERMITTED_TYPE);
        JWT_CLAIM_PERMITTED_VALUES_LIST_TYPE = new SequenceOfType(JWT_CLAIM_PERMITTED_VALUES_TYPE);
        JWT_CLAIM_PERMITTED_VALUES_LIST_TAGGED_TYPE = new TaggedType(ASN1Object.Context, 1, false, JWT_CLAIM_PERMITTED_VALUES_LIST_TYPE);
        JWT_CLAIM_CONSTRAINS_TYPE = new SequenceType();
        JWT_CLAIM_CONSTRAINS_TYPE.add("mustInclude", JWT_CLAIM_NAMES_TAGGED_TYPE, true);
        JWT_CLAIM_CONSTRAINS_TYPE.add("permittedValues", JWT_CLAIM_PERMITTED_VALUES_LIST_TAGGED_TYPE, true);
    }

    public byte[] getASN1Object() throws ASN1Exception {
        Sequence sequence = new Sequence(JWT_CLAIM_CONSTRAINS_TYPE);
        sequence.set("mustInclude", this.mustInclude);
        sequence.set("permittedValues", this.permittedValues);
        return sequence.encode();
    }

    public static JWTClaimConstraintsDTO parse(byte[] jwtClaimConstraintsBytes) throws ASN1Exception {
        Sequence jwtClaimConstraintsSeq = (Sequence) Sequence.decode(jwtClaimConstraintsBytes, JWT_CLAIM_CONSTRAINS_TYPE);
//            SequenceOf mustInclude = (SequenceOf) jwtClaimConstraintsSeq.get("mustInclude");
        TaggedValue mustInclude = (TaggedValue) jwtClaimConstraintsSeq.get("mustInclude");
        SequenceOf mustIncludeSeq = (SequenceOf) mustInclude.getInnerValue();
        List<String> mustIncludeList = new ArrayList<>();
        for (long i = 0; i < mustIncludeSeq.size(); i++) {
            IA5String jwtClaimName = (IA5String) mustIncludeSeq.get((int) i);
            mustIncludeList.add(jwtClaimName.getString());
        }

        TaggedValue permittedValues = (TaggedValue) jwtClaimConstraintsSeq.get("permittedValues");
        SequenceOf permittedValuesSeq = (SequenceOf) permittedValues.getInnerValue();
        List<JWTClaimPermittedValues> permittedValuesList = new ArrayList<>();
        for (long i = 0; i < permittedValuesSeq.size(); i++) {
            Sequence jwtClaimPermittedValuesSeq = (Sequence) permittedValuesSeq.get((int) i);
            IA5String claim = (IA5String) jwtClaimPermittedValuesSeq.get("claim");
            SequenceOf permittedSeq = (SequenceOf) jwtClaimPermittedValuesSeq.get("permitted");
            List<String> permittedList = new ArrayList<>();
            for (long j = 0; j < permittedSeq.size(); j++) {
                UTF8String utf8String = ((UTF8String) permittedSeq.get((int) j));
                permittedList.add(utf8String.getString());
            }
            JWTClaimPermittedValues jwtClaimPermittedValues = new JWTClaimPermittedValues(claim.getString(), permittedList);
            permittedValuesList.add(jwtClaimPermittedValues);
        }
        return new JWTClaimConstraintsDTO(mustIncludeList, permittedValuesList);
    }
}
