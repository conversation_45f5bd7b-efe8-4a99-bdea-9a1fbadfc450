package net.netca.test;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.Base64;
import net.netca.pki.encoding.asn1.*;
import net.netca.pki.encoding.asn1.pki.*;
import org.junit.Test;

import java.lang.Integer;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;

public class TestEncodeExt {


    @Test
    public void generate() throws Exception {

        //主题备用名
        //Extension subjectAltName = this.generateSubjectAltName("<EMAIL>", null, null);
        Extension subjectAltName = this.generateSubjectAltName(null, null, "www.netca.net");
        //Extension subjectAltName = this.generateSubjectAltName("<EMAIL>", null, null);
        System.out.println(" 主题备用名  ---- " + Base64.encode(subjectAltName.getASN1Object().encode()));


        //用户证书服务号
        Extension userCertServiceId = this.generateUserCertServiceId("12345@10");
        System.out.println(" 证书服务号  ---- " + Base64.encode(userCertServiceId.getASN1Object().encode()));

        //企业身份标识符
        Extension enterpriseExtension = this.generateEnterprise(501, "87654321");
        System.out.println(" 企业身份标识  ---- " + Base64.encode(enterpriseExtension.getASN1Object().encode()));


        //个人身份识别号
        Extension personExtension = this.generatePersonExt(1, "12345678");
        System.out.println(" 个人身份标识  ---- " + Base64.encode(personExtension.getASN1Object().encode()));


    }

    private Extension generateSubjectAltName(String email, String ip, String domain) throws PkiException {

        GeneralNames gns = new GeneralNames();
        GeneralName gn = null;
        if (email != null) {
            gn = GeneralName.NewRFC822Name(email);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }

        if (ip != null) {
            byte[] byteValue;
            try {//IPV4
                InetAddress inetAddress = InetAddress.getByName(ip);
                byteValue = inetAddress.getAddress();
            } catch (UnknownHostException e1) { //IPV6
                try {
                    InetAddress inet6Address = Inet6Address.getByName(ip);
                    byteValue = inet6Address.getAddress();
                } catch (UnknownHostException e2) {
                    throw new PkiException(e2);
                }
            }

            gn = GeneralName.NewIPAddress(byteValue);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }

        if (domain != null) {
            gn = GeneralName.NewDNSName(domain);
            gns.add(gn);
            return new Extension(Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode());
        }


        return null;
    }

    private Extension generateUserCertServiceId(String userCertServiceId) throws PkiException {

        return new Extension(Extension.NETCA_USERCERTSERVICEID_OID, false, new UTF8String(userCertServiceId).encode());

    }

    //企业身份标识符

    /**
     * @param idType 证件类型
     * @param code   证件号
     * @return
     * @throws Exception
     */
    private Extension generateEnterprise(Integer idType, String code) throws Exception {
        /**
         *  定义骨架,详细描述查看 http://netca-moss/product/技术规范/SiteAssets/SitePages/主页/NETCA自定义扩展格式规范.pdf 2.2 章节
         *  Netca Crypto 已经内置了此扩展的定义，可以直接使用
         */
        SequenceOfType sequenceOfType = (SequenceOfType) ASN1TypeManager.getInstance().get("NetcaIdentificationItems");
        //IdentificationItems
        SequenceOf identificationItems = new SequenceOf(sequenceOfType);

        //IdentificationItem
        SequenceType sequenceType = (SequenceType) ASN1TypeManager.getInstance().get("NetcaIdentificationItem");


        //原文（UTF-8）
        Sequence identificationItemUtf8 = new Sequence(sequenceType);
        identificationItemUtf8.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemUtf8.set("encode", new net.netca.pki.encoding.asn1.Integer(1L));// utf8编码

        identificationItemUtf8.set("value", new OctetString(code.getBytes(StandardCharsets.UTF_8)));

        identificationItems.add(identificationItemUtf8);


        //Base64
        Sequence identificationItemBase64 = new Sequence(sequenceType);
        identificationItemBase64.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemBase64.set("encode", new net.netca.pki.encoding.asn1.Integer(2L));//base64编码

        identificationItemBase64.set("value", new OctetString(Base64.encode(code.getBytes(StandardCharsets.UTF_8)).getBytes(StandardCharsets.US_ASCII)));

        identificationItems.add(identificationItemBase64);

        // SHA1
        Sequence identificationItemSHA1 = new Sequence(sequenceType);
        identificationItemSHA1.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA1.set("encode", new net.netca.pki.encoding.asn1.Integer(20L));//sha1编码
        JCEHasher jceHasher = new JCEHasher();
        byte[] sha1 = jceHasher.hash(AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA1_OID), code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSHA1.set("value", new OctetString(sha1));

        identificationItems.add(identificationItemSHA1);

        return new Extension(Extension.NETCA_ENTERPRISEID_ITEMS_OID, false, identificationItems.encode());
    }

    //个人身份标识符

    /**
     * @param idType 证件类型
     * @param code   证件号
     * @return
     * @throws Exception
     */
    private Extension generatePersonExt(Integer idType, String code) throws Exception {

        /**
         *  定义骨架,详细描述查看 http://netca-moss/product/技术规范/SiteAssets/SitePages/主页/NETCA自定义扩展格式规范.pdf 2.3 章节
         *  Netca Crypto 已经内置了此扩展的定义，可以直接使用
         */
        SequenceOfType sequenceOfType = (SequenceOfType) ASN1TypeManager.getInstance().get("NetcaIdentificationItems");
        //IdentificationItems
        SequenceOf identificationItems = new SequenceOf(sequenceOfType);

        //IdentificationItem
        SequenceType sequenceType = (SequenceType) ASN1TypeManager.getInstance().get("NetcaIdentificationItem");
        // SHA1
        Sequence identificationItemSHA1 = new Sequence(sequenceType);
        identificationItemSHA1.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA1.set("encode", new net.netca.pki.encoding.asn1.Integer(20L));//sha1编码
        JCEHasher jceHasher = new JCEHasher();
        byte[] codeBytes = code.getBytes(StandardCharsets.UTF_8);
        byte[] sha1 = jceHasher.hash(AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA1_OID), codeBytes, 0, codeBytes.length);
        identificationItemSHA1.set("value", new OctetString(sha1));

        identificationItems.add(identificationItemSHA1);

        //SHA256
        Sequence identificationItemSHA256 = new Sequence(sequenceType);
        identificationItemSHA256.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSHA256.set("encode", new net.netca.pki.encoding.asn1.Integer(30L));//sha256编码
        byte[] sha256 = jceHasher.hash(AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SHA256_OID)
                , code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSHA256.set("value", new OctetString(sha256));

        identificationItems.add(identificationItemSHA256);


        //SM3
        Sequence identificationItemSM3 = new Sequence(sequenceType);
        identificationItemSM3.set("type", new net.netca.pki.encoding.asn1.Integer(idType));//身份证
        identificationItemSM3.set("encode", new net.netca.pki.encoding.asn1.Integer(40L));//sm3编码

        byte[] sm3 = jceHasher.hash(AlgorithmIdentifier.CreateAlgorithmIdentifier(AlgorithmIdentifier.SM3_OID), code.getBytes(StandardCharsets.UTF_8), 0, code.getBytes(StandardCharsets.UTF_8).length);
        identificationItemSM3.set("value", new OctetString(sm3));

        identificationItems.add(identificationItemSM3);

        return new Extension(Extension.NETCA_IDENTIFY_ITEMS_OID, false, identificationItems.encode());
    }
}