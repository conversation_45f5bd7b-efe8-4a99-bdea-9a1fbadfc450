package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 密钥对
 */
public enum KeypairStatusConstant implements IBaseEnum {

	STATUS_INIT(0,"初始化"),
	STATUS_CERT_IS_INSTALLED(1,"证书已签发"),
	STATUS_CERT_REVOKE(2, "证书已注销");

	private int code;
	private String description;

	private KeypairStatusConstant(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

}
