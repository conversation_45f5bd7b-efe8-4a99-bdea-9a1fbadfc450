package net.netca.cloudkey.base.constraints.annotation;

import org.hibernate.validator.constraints.CompositionType;
import org.hibernate.validator.constraints.ConstraintComposition;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对电话号码的校验注解，包括固定电话和手机号码
 * 满足@TelPhone或@MobilePhone之一即可校验通过
 */
@TelPhone(message = "联系电话格式错误")
@ConstraintComposition(CompositionType.OR)
@MobilePhone(message = "联系电话格式错误")
@Documented
@Constraint(validatedBy = {})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface Phone {

    String message() default "电话号码格式错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
