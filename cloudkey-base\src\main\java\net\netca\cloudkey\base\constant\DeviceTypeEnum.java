package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

@Getter
@AllArgsConstructor
public enum DeviceTypeEnum implements IBaseEnum {
    UNKNOWN_DEVICE(0, "未知设备"), //
    PRIMARY_DEVICE(1, "主设备"), //
    ACCESSORY_DEVICE(2, "附属设备"), //
    ;
    private final Integer code;
    private final String description;
}
