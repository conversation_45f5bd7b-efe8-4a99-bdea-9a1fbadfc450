package net.netca.test.cert;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.PkiException;
import net.netca.pki.algorithm.ecc.Curve;
import net.netca.pki.algorithm.ecc.ECCKeyPair;
import net.netca.pki.encoding.Base64;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.*;
import net.netca.pki.encoding.asn1.pki.Extension;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.codec.cmp.*;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.IdentityTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.*;
import net.netca.sdk.message.cmp.*;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.message.cmp.config.CompositeMessageConfigManagement;
import net.netca.sdk.util.PKCS10Generator;
import net.netca.sdk.util.StringUtils;
import net.netca.test.asn1.GeneralName;
import net.netca.test.asn1.OtherName;
import net.netca.test.asn1.format.*;
import net.netca.test.asn1.pojo.JWTClaimConstraintsDTO;
import net.netca.test.asn1.pojo.JWTClaimPermittedValues;
import net.netca.test.config.Config;
import net.netca.test.config.GMConfig;
import net.netca.test.config.TrunkConfig;
import net.netca.test.constant.AttributeTypeValueMapping;
import net.netca.test.constant.EncodeTypeEnum;
import net.netca.test.constant.GeneralNameEnum;
import net.netca.test.exception.ExtensionFormatException;
import net.netca.test.util.DateUtil;
import net.netca.test.util.EncodeExtUtil;
import net.netca.test.util.KeyStoreUtil;
import net.netca.test.util.PropertiesUtil;
import okhttp3.*;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Before;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.PublicKey;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-06-28 10:12
 */

@CommonsLog
public class TestCmpProtocol {

    //构造 OkHttpClient 可以用 HTTPClient
    private OkHttpClient okHttpClient = new OkHttpClient()
            .newBuilder()
            .connectTimeout(20, TimeUnit.SECONDS)//设置连接超时时间
            .readTimeout(20, TimeUnit.SECONDS)//设置读取超时时间
            .build();


    // 证书模板ID（根据实际情况，自行配置）
    private String certTemplateId;

    //请求 url
    private String url;

    /**
     * RA 通讯证书
     */
    X509Certificate raCommCert;

    /**
     * 第三方通讯证书的颁发者证书
     */
    X509Certificate caCert;

    /**
     * 第三方通讯证书
     */
    X509Certificate thirdPartyServerCert;

    /**
     * 第三方通讯证书的私钥信息
     */
    PrivateKeyInfo thirdPartyServerCertPrivateKeyInfo;
    /**
     * 第三方通讯证书的签名算法
     */
    String serverCommCertSignAlgoName;

    PrivateKey mockRSAPrivateKey;

    PublicKey mockRSAPublicKey;

    KeyPair mockKeyPair;

    KeyPair mockRSAKeyPair;

    ECCKeyPair mockSM2KeyPair;

    X509Certificate operatorCert;
    PrivateKeyInfo operatorCertPrivateKeyInfo;
    String operatorCertSignAlgoName;


    MultipleMessageCodec multipleMessageEncoder;
    MessageEncoder<CmpMessage> p10CertReqMessageEncoder;
    MessageEncoder<CmpMessage> keyUpdateReqMessageEncoder;
    MessageEncoder<CmpMessage> keyRecoveryReqMessageEncoder;
    MessageEncoder<CmpMessage> revocationReqMessageEncoder;
    MessageEncoder<CmpMessage> pollReqMessageEncoder;
    MessageEncoder<CmpMessage> nestedMessageEncoder;
    MessageEncoder<CmpMessage> certConfirmReqMessageEncoder;

    MultipleMessageCodec multipleMessageDecoder;
    MessageDecoder<PKIMessage> p10CertReqMessageDecoder;
    MessageDecoder<PKIMessage> keyUpdateReqMessageDecoder;
    MessageDecoder<PKIMessage> keyRecoveryReqMessageDecoder;
    MessageDecoder<PKIMessage> revocationReqMessageDecoder;
    MessageDecoder<PKIMessage> pollReqMessageDecoder;
    MessageDecoder<PKIMessage> nestedMessageDecoder;
    MessageDecoder<PKIMessage> certConfirmReqMessageDecoder;

    CmpMessageConfigManagement cmpMessageConfigManagement;

    CompositeMessageConfigManagement compositeMessageConfigManagement;

    @Before
    public void setUp() throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException, UnrecoverableKeyException, PkiException {
        Config config = new TrunkConfig();
        //CA系统的通讯证书sha256摘要（可以弄成系统配置的形式，根据实际情况，自行配置）
        String caCommCertSha256 = config.getCaCommCertSha256();
        String raCommCertStr = config.getRaCommCert();
        certTemplateId = config.getCertTempleId();
        url = config.getBaseUrl() + caCommCertSha256 + certTemplateId;

        KeyStore keyStore = KeyStoreUtil.generateKeyStore(PropertiesUtil.getProperty("keyStorePath"), PropertiesUtil.getProperty("keyStoreType"), PropertiesUtil.getProperty("keyStorePwd"));
        KeyStore mockKeyStore = KeyStoreUtil.generateKeyStore(PropertiesUtil.getProperty("mockKeyPairPath"), PropertiesUtil.getProperty("mockStoreType"), PropertiesUtil.getProperty("mockKeyStorePwd"));
        mockRSAPrivateKey = (PrivateKey) mockKeyStore.getKey(PropertiesUtil.getProperty("mockKeyStoreAlias"), PropertiesUtil.getProperty("mockPrivateKeyPwd").toCharArray());
        mockRSAPublicKey = mockKeyStore.getCertificate(PropertiesUtil.getProperty("mockKeyStoreAlias")).getPublicKey();
        raCommCert = new X509Certificate(raCommCertStr);
        thirdPartyServerCert = new X509Certificate(keyStore.getCertificate(PropertiesUtil.getProperty("certAlias")).getEncoded());
        thirdPartyServerCertPrivateKeyInfo = PrivateKeyInfo.decode(keyStore.getKey(PropertiesUtil.getProperty("keyStoreAlias"), PropertiesUtil.getProperty("privateKeyPwd").toCharArray()).getEncoded());
        serverCommCertSignAlgoName = "SHA256WithRSA";
        PrivateKey mockRSAPrivateKey2 = (PrivateKey) mockKeyStore.getKey(PropertiesUtil.getProperty("mockKeyStoreAlias2"), PropertiesUtil.getProperty("mockPrivateKeyPwd2").toCharArray());
        PublicKey mockRSAPublicKey2 = mockKeyStore.getCertificate(PropertiesUtil.getProperty("mockKeyStoreAlias2")).getPublicKey();
        mockRSAKeyPair = new KeyPair(mockRSAPublicKey2, mockRSAPrivateKey2);
        mockSM2KeyPair = generateSM2KeyPair();
        mockKeyPair = mockRSAKeyPair;
        KeyStore operatorKeyStore = KeyStoreUtil.generateKeyStore(PropertiesUtil.getProperty("operatorKeyStorePath"), PropertiesUtil.getProperty("operatorKeyStoreType"), PropertiesUtil.getProperty("operatorKeyStorePwd"));
        operatorCert = new X509Certificate(operatorKeyStore.getCertificate(PropertiesUtil.getProperty("operatorCertAlias")).getEncoded());
        operatorCertPrivateKeyInfo = PrivateKeyInfo.decode(operatorKeyStore.getKey(PropertiesUtil.getProperty("operatorKeyStoreAlias"), PropertiesUtil.getProperty("operatorPrivateKeyPwd").toCharArray()).getEncoded());
        operatorCertSignAlgoName = "SHA256WithRSA";

        String caCertBase64 = "-----BEGIN CERTIFICATE-----\n" +
                "MIIGBTCCA+2gAwIBAgIUCnKU6KoNGSEoMyLphjZdlDEZmGswDQYJKoZIhvcNAQEL\n" +
                "BQAwWTELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1\n" +
                "dGhvcml0eTEkMCIGA1UEAwwbTkVUQ0FfVGVzdDIwMTkgUlNBIFJvb3RDQTAyMB4X\n" +
                "DTE5MTIyMjE2MDAwMFoXDTM0MTIyMjE2MDAwMFowPjELMAkGA1UEBhMCQ04xDjAM\n" +
                "BgNVBAoMBU5FVENBMR8wHQYDVQQDDBZURVNUMjAxOSBORVRDQSBSU0EgQ0ExMIIB\n" +
                "IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyz6b7J6HrPW5z++vPxPQDKys\n" +
                "2kX/kn1n7LpOlwlheiYfb2QjE6hrg1bgcxt7r+xcJUGstdlpgffTuSsTACA4zT7y\n" +
                "iLhLYBz5CfixgUchmz9ppifQMLXptEOAcJCHk4xMNnho8vWzl3/Eoi9BlN3tCGxG\n" +
                "yjFCBwEFlNOw2zEdcFz5rjvcXd80ZQ1pW/lbVhsgdxJgtWagSj2XUZbd3FKL/d3C\n" +
                "ea95TyMQOom0xwrWfMrkGkr5WW4UsOoG3krHz8i6B3ivPDWV3Kr4zHCJ4zvWfGDj\n" +
                "1RAxMjgrIt8AobkhDC7dxm7ncQdfwTDsYnnqSBA+tc1ouNsuLZXZw6sPXWQWpwID\n" +
                "AQABo4IB3jCCAdowgYsGCCsGAQUFBwEBBH8wfTA8BggrBgEFBQcwAoYwaHR0cDov\n" +
                "L2xhY2EuY25jYS5uZXQvbGFjYS9DQ1NORVRDQVJTQVJPT1RMQTEuY2VyMD0GCCsG\n" +
                "AQUFBzABhjFodHRwOi8vbGFvY3NwdGVzdC5jbmNhLm5ldC9vY3NwbWFuYWdlci9v\n" +
                "Y3NwL2NoZWNrME8GCCsGAQUFBwELBEMwQTA/BggrBgEFBQcwBYYzaHR0cDovL2xh\n" +
                "Y2EuY25jYS5uZXQvbGFjYS9Jc3N1ZWRCeVRQSU5FVENBUlNBQ0EucDdjMB0GA1Ud\n" +
                "DgQWBBRLHW2inaJxCIiEx3qGY4ycFjeecTAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0T\n" +
                "AQH/BAUwAwEB/zCBhAYDVR0fBH0wezA7oDmgN4Y1aHR0cDovLzE5Mi4xNjguMC4y\n" +
                "OC9jcmwvTkVUQ0EtVGVzdDIwMTlSU0FSb290Q0EwMS5jcmwwPKA6oDiGNmh0dHA6\n" +
                "Ly90ZXN0LmNuY2EubmV0L2NybC9ORVRDQS1UZXN0MjAxOVJTQVJvb3RDQTAxLmNy\n" +
                "bDARBgNVHSAECjAIMAYGBFUdIAAwHwYDVR0jBBgwFoAUbfL3wAzYphkLyCt2TVFo\n" +
                "fziKL4owDQYJKoZIhvcNAQELBQADggIBACsaoV8aixjiPbeAyrqwP/O6pGX65miQ\n" +
                "1HpJB0yjUwvLw1fpUeOJRYfu0S4BlyXYoWdUXh5LJLDnPm+FDf0kCTqGgmqLQ3DX\n" +
                "7Q5IgM52+dEFZbwrhdI5OCO5lvP1Tl1lTfTFuydG2pr33RG+n19SogBBXg6mrtH/\n" +
                "PoIzkRPuMz7wLLkkjGA9KA/necQ5LKQI0lxznF81LFrzb1Qa0OqfdM7RMIsBKxSz\n" +
                "VDZkNPMrcpQZStudeXqaGl0qiXcwtJ7Wyuy3/VmXeSR6YZmRNnM7kxzGa3gZlw0G\n" +
                "Wrfi/fRBWBuEGdSk2Ok4M8opDyCtKxMxpZXw+LxYJ2kI8XVmO21ZPRHObNIhTxuI\n" +
                "tIG1ASEPdxjKXiFhD5suhR7h9YtLdOzGFvWOmKblmIan1B39+N9ZRXJ0bLdGAVl3\n" +
                "XSuVCYQXrEYgaY9kmOH75oWk3KzQj4LsGSh39xOB9pItxoMtyhoQNzQiRENO1n1R\n" +
                "tfFHF/AGNtkJiQQemYP9CzEHMyykKFqE8eFqhzwaGlLxroeNrhsTJCHABN9HzTOl\n" +
                "Btt0cnyt60fwv4/Wb7X05g9zJz39padWJPD2XgMnpJZKglH6Kv39gWz2AX6C8XBz\n" +
                "0Qa4zN2N41g8+blfDr+zoK+2nFdqiE3/C2LSzvOVJS/1cymPd0nul7E3gpFgULUW\n" +
                "NkBJXZ6fSx8l\n" +
                "-----END CERTIFICATE-----\n";
//        caCert = new X509Certificate(caCertBase64);
        caCert = null;

        cmpMessageConfigManagement = CmpMessageConfigManagement.builder()
                .communicationCert(raCommCert)
                .thirdPartyServerCommCert(thirdPartyServerCert)
                .caCert(caCert)
                .thirdPartyServerCommCertPrivateKeyInfo(thirdPartyServerCertPrivateKeyInfo)
                .thirdPartyServerCommCertSignAlgoName(serverCommCertSignAlgoName)
                .build();
        compositeMessageConfigManagement = CompositeMessageConfigManagement.builder()
                .communicationCert(raCommCert)
                .thirdPartyServerCommCert(thirdPartyServerCert)
                .caCert(caCert)
                .thirdPartyServerCommCertPrivateKeyInfo(thirdPartyServerCertPrivateKeyInfo)
                .thirdPartyServerCommCertSignAlgoName(serverCommCertSignAlgoName)
                .operatorCert(operatorCert)
                .operatorCertPrivateKeyInfo(operatorCertPrivateKeyInfo)
                .certSignAlgoName(operatorCertSignAlgoName)
                .build();
        initEncoders();
        initDecoders();

    }

    private void initDecoders() {
        p10CertReqMessageDecoder = P10CertReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        keyUpdateReqMessageDecoder = KeyUpdateReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        keyRecoveryReqMessageDecoder = KeyRecoveryReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        revocationReqMessageDecoder = RevocationReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        pollReqMessageDecoder = PollReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        certConfirmReqMessageDecoder = CertConfirmReqMessageCodec.createDecoder(cmpMessageConfigManagement);
        nestedMessageDecoder = NestedMessageCodec.createDecoder(compositeMessageConfigManagement);
        List<MessageDecoder<PKIMessage>> decoders = new ArrayList<>();
        decoders.add(p10CertReqMessageDecoder);
        decoders.add(keyUpdateReqMessageDecoder);
        decoders.add(keyRecoveryReqMessageDecoder);
        decoders.add(revocationReqMessageDecoder);
        decoders.add(pollReqMessageDecoder);
        decoders.add(certConfirmReqMessageDecoder);
        decoders.add(nestedMessageDecoder);
        multipleMessageDecoder = MultipleMessageCodec.createDecoder(decoders);
    }

    private void initEncoders() {
        p10CertReqMessageEncoder = P10CertReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        keyUpdateReqMessageEncoder = KeyUpdateReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        keyRecoveryReqMessageEncoder = KeyRecoveryReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        revocationReqMessageEncoder = RevocationReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        pollReqMessageEncoder = PollReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        certConfirmReqMessageEncoder = CertConfirmReqMessageCodec.createEncoder(cmpMessageConfigManagement);
        nestedMessageEncoder = NestedMessageCodec.createEncoder(compositeMessageConfigManagement);
        List<MessageEncoder<CmpMessage>> encoders = new ArrayList<>();
        encoders.add(p10CertReqMessageEncoder);
        encoders.add(keyUpdateReqMessageEncoder);
        encoders.add(keyRecoveryReqMessageEncoder);
        encoders.add(revocationReqMessageEncoder);
        encoders.add(pollReqMessageEncoder);
        encoders.add(certConfirmReqMessageEncoder);
        encoders.add(nestedMessageEncoder);
        multipleMessageEncoder = MultipleMessageCodec.createEncoder(encoders);
    }


    /**
     * 模拟获取一张新的签名证书
     *
     * @return
     * @throws Exception
     */
    X509Certificate getNewSignCert() throws Exception {
        X509Certificate signCert = null;
        //通过设备获取密钥对
        String rsap10 = generateRSAP10();
//        String rsap10 = generateSM2P10();
        P10CertReqMessage p10CertReqMessage = getP10CertReqMessage(rsap10);
        SingleCertReqContext context = new SingleCertReqContext(p10CertReqMessage);
        p10CertReqMessageEncoder.encode(context, p10CertReqMessage);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        p10CertReqMessageDecoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);
        CmpRespResult cmpRespResult = context.getCmpRespResult();
        if (PKIStatusEnum.ACCEPTED.equals(cmpRespResult.getPkiStatusEnum())) {
            if (cmpRespResult.getFailureReason() != null) {
                log.error(cmpRespResult.getFailureReason());
                log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
            }
            Assert.assertNotNull(cmpRespResult.getSignCertPem());
//            Assert.assertNotNull(cmpRespResult.getEncCertPem());
            signCert = new X509Certificate(cmpRespResult.getSignCertDer());
        }
        return signCert;
    }

    /**
     * 生成p10
     *
     * @return
     * @throws PkiException
     */
    protected String generateRSAP10() throws PkiException {
        JCESigner jceSigner = new JCESigner(mockRSAKeyPair.getPrivate());
        return generateP10(jceSigner, mockRSAKeyPair.getPublic().getEncoded(), AlgorithmIdentifier.SHA256WithRSA_OID);
    }


    protected String generateSM2P10() throws PkiException {
        JCESigner jceSigner = new JCESigner(mockSM2KeyPair);
        ECCPublicKey eccPublicKey = new ECCPublicKey(AlgorithmIdentifier.SM2Curve_OID, mockSM2KeyPair.getPublicKey().getX(), mockSM2KeyPair.getPublicKey().getY());
        return generateP10(jceSigner, eccPublicKey.toSubjectPublicKeyInfo().getASN1Object().encode(), AlgorithmIdentifier.SM3WithSM2_OID);
    }


    private ECCKeyPair generateSM2KeyPair() {
        return ECCKeyPair.generateKeyPair(Curve.getSM2Curve(), new JCESecureRandomGenerator());
    }

    /**
     * 生成p10
     *
     * @return p10
     * @throws PkiException
     */
    private String generateP10(JCESigner jceSigner, byte[] publicKey, String algorithmOID) throws PkiException {
        SubjectInfo subject = SubjectInfo.builder()
                .cn("mockUser")
                .c("CN")
//                .o("广东省电子商务认证有限公司")
//                .ou("开发部")
//                .title("开发工程师")
//                .postalAddress("postalAddress")
//                .dateOfBirth("2020-02-15 12:12:12")
//                .initials("test initials")
//                .generation("test generationQuilifer")
//                .pseudonym("test pseudonyn")
//                .uri("test uri")
//                .urn("test urn")
//                .url("test url")
//                .organizationIdentifier("test organizationIdentifier")
//                .telephoneNumber("test telephoneNumber")
                .build();

        return Base64.encode(PKCS10Generator.generateP10(publicKey, jceSigner, subject, algorithmOID));
    }


    /**
     * 根据p10生产证书申请所需要的消息体
     *
     * @param p10Str p10
     * @return
     * @throws Exception
     */
    P10CertReqMessage getP10CertReqMessage(String p10Str) throws Exception {
        // 证书请求号，业务标识，主要是唯一标识一个请求，由调用方自行赋值，需保证来自同一个调用方的证书请求号唯一
        long certRequestId = new Random().nextLong();
        //设置sender和recipient
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        Date now = new Date();
        UserInfo userInfo = UserInfo.builder()
                .name("测试")
                .identityType(IdentityTypeEnum.OTHERPERSONIDENTITYTYPE.getCode())
                .identity("test")
                .build();
        // 证书有效期
        Validity validity = Validity.builder()
                .startTime(now)
                .endTime(DateUtil.dateAddYear(now, 2))
                .build();
        // 构建 P10 证书注册 CMP 请求所需的参数，包括：senderStr、recipientStr、p10Base64、证书有效期（必选）
        CustomFreeText customFreeText = CustomFreeText.builder()
                .validity(validity)
                .certReqId(certRequestId)
                .userInfo(userInfo)
                .build();
        net.netca.pki.encoding.asn1.pki.Extension extension = EncodeExtUtil.generatePhoneWithDirectoryName("18814622356");
        List<net.netca.sdk.message.Extension> list =  new ArrayList<>();
        list.add(new net.netca.sdk.message.Extension(extension.getOid(), extension.isCritical(), extension.getExtensionValue()));
        return P10CertReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .p10Base64(p10Str)
                .certRequestId(certRequestId)
                .requestExtensions(list)
                .customFreeText(customFreeText)
                .build();
    }


    void suspendOrUnSuspendCert(X509Certificate signCert, BusinessTypeEnum businessTypeEnum) throws Exception {
        Objects.requireNonNull(signCert);
        String serialNumber = Hex.encode(false, signCert.getSerialNumber());
        String issuer = signCert.getIssuer().getLdapName();
        RevocationReqMessage suspendCertMessage = getSuspendOrUnSuspendCertMessage(issuer, serialNumber, businessTypeEnum);
        normalSuspendOrUnSuspendCert(suspendCertMessage);
    }


    void normalSuspendOrUnSuspendCert(CmpMessage cmpMessage) throws Exception {
        SingleCertReqContext context = new SingleCertReqContext(cmpMessage);
        revocationReqMessageEncoder.encode(context, cmpMessage);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        Assert.assertNotNull(respPkiMessages);
        revocationReqMessageDecoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);
        CmpRespResult cmpRespResult = context.getCmpRespResult();
        Assert.assertSame(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
        if (cmpRespResult.getFailureReason() != null) {
            log.error(cmpRespResult.getFailureReason());
            log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
        }
        log.debug("证书挂失成功");
    }


    private PollReqMessage generateCmpParams(String senderStr, String recipientStr, long certReqId) {
        return PollReqMessage.builder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .certRequestId(certReqId)
                .build();
    }


    byte[] send(byte[] sendData) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.get("application/pkixcmp"), sendData))
                .build();
        Response response = okHttpClient.newCall(request).execute();
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.body());
        return response.body().bytes();
    }


    List<Extension> getExtensions() throws Exception {
        //主题备用名
        List<Extension> extensionArrayList = new ArrayList<>();
        extensionArrayList.add(generateSubjectAltName(Collections.singletonList("<EMAIL>"), Collections.singletonList("***********"), Arrays.asList("gsta.example.com", "gsta.exmaple.net")));
        return extensionArrayList;
    }

    private Extension generateSubjectAltName(List<String> emailList, List<String> ipList, List<String> domainList) throws PkiException {

        GeneralNames gns = new GeneralNames();
        net.netca.pki.encoding.asn1.pki.GeneralName gn;
        if (emailList != null && !emailList.isEmpty()) {
            for (String email : emailList) {
                gn = net.netca.pki.encoding.asn1.pki.GeneralName.NewRFC822Name(email);
                gns.add(gn);
            }
        }

        if (ipList != null && !ipList.isEmpty()) {
            for (String ip : ipList) {
                byte[] byteValue;
                try {//IPV4
                    InetAddress inetAddress = InetAddress.getByName(ip);
                    byteValue = inetAddress.getAddress();
                } catch (UnknownHostException e1) { //IPV6
                    try {
                        InetAddress inet6Address = Inet6Address.getByName(ip);
                        byteValue = inet6Address.getAddress();
                    } catch (UnknownHostException e2) {
                        throw new PkiException(e2);
                    }
                }
                gn = net.netca.pki.encoding.asn1.pki.GeneralName.NewIPAddress(byteValue);
                gns.add(gn);
            }
        }

        if (domainList != null && !domainList.isEmpty()) {
            for (String domain : domainList) {
                gn = net.netca.pki.encoding.asn1.pki.GeneralName.NewDNSName(domain);
                gns.add(gn);
            }
        }
        return gns.size() > 0 ? new Extension(net.netca.pki.encoding.asn1.pki.Extension.SUBJECT_ALTNAME_OID, false, gns.getASN1Object().encode()) : null;
    }

    private GeneralNameFormat formatGeneralName(GeneralName generalName) throws ExtensionFormatException {
        GeneralNameFormat generalNameFormat;
        generalNameFormat = new GeneralNameFormat();

        Integer type = generalName.getType();
        GeneralNameEnum generalNameEnum = GeneralNameEnum.getGeneralNameEnumByType(type);
        generalNameFormat.setType(generalNameEnum);

        if (GeneralNameEnum.otherName.equals(generalNameEnum)) {
            OtherName otherName = generalName.getOtherName();
            OtherNameFormat otherNameFormat = new OtherNameFormat();
            otherNameFormat.setTypeId(otherName.getTypeId());
            otherNameFormat.setValue(otherName.getValue());

            generalNameFormat.setValue(otherNameFormat);

        } else if (GeneralNameEnum.directoryName.equals(generalNameEnum)) {
            SubjectInfo subjectInfo = generalName.getSubject();
            //TODO esimca
            List<AttributeTypeValueFormat> attributeTypeValueFormats = dealSubject(subjectInfo);

            RDNSequenceFormat rdn = new RDNSequenceFormat();
            List<RelativeDistinguishedNameFormat> relativeDistinguishedNames = new ArrayList<>();

            RelativeDistinguishedNameFormat rdnf = new RelativeDistinguishedNameFormat();
            rdnf.setAttributeTypeValueFormat(attributeTypeValueFormats);
            relativeDistinguishedNames.add(rdnf);
            rdn.setRelativeDistinguishedNames(relativeDistinguishedNames);

            generalNameFormat.setValue(rdn);

        } else {
            generalNameFormat.setValue(generalName.getValue());
        }
        return generalNameFormat;
    }

    public List<AttributeTypeValueFormat> dealSubject(SubjectInfo subjectInfo) throws ExtensionFormatException {
        List<AttributeTypeValueFormat> attributeTypeValueFormats = new ArrayList<>();

        if (subjectInfo == null) {
            return Collections.emptyList();
        }
        Class clazz = subjectInfo.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            String getStr = "get" + StringUtils.capitalize(fieldName);
            Method method;
            try {
                method = clazz.getMethod(getStr);
            } catch (NoSuchMethodException | SecurityException e) {
                log.warn(e.getMessage(), e);
                log.warn("no such method ：" + getStr);
                continue;
            }
            String value;
            try {
                value = (String) method.invoke(subjectInfo);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                log.warn(e.getMessage(), e);
                log.warn("invoke：" + getStr + " error。");
                continue;
            }


            String dn = fieldName.toUpperCase();

            String oid = AttributeTypeValueMapping.getOidFromAttributeType(dn);
            if (StringUtils.isBlank(oid)) {
                throw new ExtensionFormatException(String.format("没有找到%s对应的OID值", dn));
            }

            String encodeType = AttributeTypeValueMapping.getEncodeTypeFromOid(oid);
            EncodeTypeEnum encodeTypeEnum = EncodeTypeEnum.getEncodeTypeEnum(encodeType);


            //考虑用分号隔离多项
            String[] vals = value.split(";");
            if (vals.length > 0) {
                int insertOrder = 1;
                for (String val : vals) {
                    //TODO 对DC这个扩展项还要进一步拆分
                    AttributeTypeValueFormat atf = new AttributeTypeValueFormat();
                    atf.setAttrOid(oid);
                    atf.setEncodeType(encodeTypeEnum);
                    atf.setOrder(0);
                    atf.setValue(val);
                    atf.setInsertOrder(insertOrder++);
                    attributeTypeValueFormats.add(atf);
                }
            }
        }

        return attributeTypeValueFormats;
    }

    private static JWTClaimConstraintsDTO createJwtClaimConstraintsDTO() {
        List<String> mustInclude = new ArrayList<>();
        mustInclude.add("confidence");
        mustInclude.add("test confidence");
        String claim = "confidence";
        String claim2 = "test confidence";
        List<String> permitted = new ArrayList<>();
        permitted.add("high");
        permitted.add("low");
        List<JWTClaimPermittedValues> permittedValues = new ArrayList<>();
        JWTClaimPermittedValues jwtClaimPermittedValues = new JWTClaimPermittedValues(claim, permitted);
        JWTClaimPermittedValues jwtClaimPermittedValues2 = new JWTClaimPermittedValues(claim2, permitted);
        permittedValues.add(jwtClaimPermittedValues);
        permittedValues.add(jwtClaimPermittedValues2);
        return new JWTClaimConstraintsDTO(mustInclude, permittedValues);
    }


    RevocationReqMessage getSuspendOrUnSuspendCertMessage(String issuer, String serialNumber, BusinessTypeEnum businessTypeEnum) throws PkiException {
        Random random = new Random();
        long certRequestId = random.nextLong();
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";

        // 构建 P10 证书注册 CMP 请求所需的参数，包括：senderStr、recipientStr、p10Base64、证书有效期（必选）
        CustomFreeText customFreeText = CustomFreeText.builder()
                .certReqId(certRequestId)
                .build();
        // 构建证书挂失/解挂 CMP 请求所需的参数，只需颁发者、证书序列号、certRequestId（随机数）
        return RevocationReqMessage.builder()
                .businessTypeEnum(businessTypeEnum)
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .issuer(issuer)
                .certSn(serialNumber)
                .isPublishLdap(true)
                .certRequestId(certRequestId)
                .freeText(customFreeText)
                .build();
    }


    NestedMessage getNestedMessage(CmpMessage inlineMessage, BusinessTypeEnum businessTypeEnum) {
        //设置sender和recipient
        String senderStr = "CN=netcabpmssystem,O=netca";
        String recipientStr = "CN=test";
        // 内嵌消息
        return NestedMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .inlineMessage(inlineMessage)
                .businessTypeEnum(businessTypeEnum)
                .build();
    }


    boolean compare(Date expect, Date actual) {
        Calendar expectDate = Calendar.getInstance();
        expectDate.setTime(expect);
        Calendar actualDate = Calendar.getInstance();
        actualDate.setTime(actual);
        return expectDate.get(Calendar.YEAR) == actualDate.get(Calendar.YEAR)
                && expectDate.get(Calendar.MONTH) == actualDate.get(Calendar.MONTH)
                && expectDate.get(Calendar.DAY_OF_MONTH) == actualDate.get(Calendar.DAY_OF_MONTH)
                && expectDate.get(Calendar.HOUR_OF_DAY) == actualDate.get(Calendar.HOUR_OF_DAY)
                && expectDate.get(Calendar.MINUTE) == actualDate.get(Calendar.MINUTE)
                && expectDate.get(Calendar.SECOND) == actualDate.get(Calendar.SECOND);
    }


    /**
     * 进行证书轮询，获取轮询结果
     *
     * @param respCertRequestId 证书请求Id
     * @param businessTypeEnum  业务类型
     * @return
     * @throws Exception
     */
    CmpRespResult getPollReqCmpRespResult(long respCertRequestId, BusinessTypeEnum businessTypeEnum) throws Exception {
        // 进行证书轮询
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        PollReqMessage pollReqMessage = PollReqMessage.defaultMessage().toBuilder()
                .businessTypeEnum(businessTypeEnum)
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .certRequestId(respCertRequestId)
                .build();
        SingleCertReqContext context = new SingleCertReqContext(pollReqMessage);
        pollReqMessageEncoder.encode(context, pollReqMessage);
        PKIMessages reqPKIMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPKIMessages);
        byte[] reqData = reqPKIMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        PKIMessages respPKIMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        Assert.assertNotNull(respPKIMessages);
        PKIMessage respPKIMessage = respPKIMessages.toPKIMessageArray()[0];
        Assert.assertNotNull(respPKIMessage);
        pollReqMessageDecoder.decode(context, respPKIMessage);
        return context.getCmpRespResult();
    }
}
