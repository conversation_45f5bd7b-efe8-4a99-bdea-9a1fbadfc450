package net.netca.sdk.message.cmp.config;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-04-07 15:33
 */
@SuperBuilder
@Getter
public class CompositeMessageConfigManagement extends CmpMessageConfigManagement {
    X509Certificate operatorCert;
    PrivateKeyInfo operatorCertPrivateKeyInfo;
    String certSignAlgoName;

    @Override
    public void verifyEncodeValid() {
        super.verifyEncodeValid();
        Objects.requireNonNull(operatorCert, "operatorCert isn't allow to be null");
        Objects.requireNonNull(operatorCertPrivateKeyInfo, "operatorCertPrivateKeyInfo isn't allow to be null");
        Objects.requireNonNull(certSignAlgoName, "thirdPartyServerCommCertSignAlgoName isn't allow to be null");
    }
}
