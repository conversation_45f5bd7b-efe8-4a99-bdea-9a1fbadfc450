package net.netca.cloudkey.base.dto.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @descriptions:
 * @date: 2019/1/28
 * @author: SaromChars
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperatorLogVO {

    private Integer id;

    private Integer operatorId;

    private String operatorName;

    private String clientIpAddr;

    private String clientIpPort;

    private String requestUri;

    private String requestDescription;

    private String requestSignature;

    private String requestSignatureOriginal;

    private String requestContent;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestEnd;

    private Integer responseCode;

    private String responseContent;

    private String memo;

    private Integer districtId; //院区Id

    private String auditStatus; //审计状态
    private String auditName; //审计员

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtAudit; //审计时间

}
