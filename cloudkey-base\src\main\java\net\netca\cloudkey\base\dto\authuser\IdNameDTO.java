package net.netca.cloudkey.base.dto.authuser;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

/**
 * created by zaj on 2019/4/24
 */
@Data
@Builder
@Secret
@NoArgsConstructor
@AllArgsConstructor
public class IdNameDTO {

    private Integer id;

    private String name;

    private String uid;

    @SecurityDataField
    private String phone;
}
