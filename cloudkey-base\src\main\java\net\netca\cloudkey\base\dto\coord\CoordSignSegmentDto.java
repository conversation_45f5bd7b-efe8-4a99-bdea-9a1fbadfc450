package net.netca.cloudkey.base.dto.coord;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(description = "签名环节")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoordSignSegmentDto {

    @ApiModelProperty(value = "签名环节id")
    private Integer id;

    @ApiModelProperty(value = "签名环节序号")
    private Integer segmentSeq;
    /**
     * 签名环节名称
     */
    @ApiModelProperty(value = "签名环节名称")
    private String segmentName;
    /**
     * 签名人数
     */
    @ApiModelProperty(value = "签名人数")
    private Integer signerNum;
    /**
     * 签名至少人数
     */
    @ApiModelProperty(value = "签名至少人数")
    private Integer limitNum;

    @ApiModelProperty(value = "推送状态：0-未推送，1-已推送，2-推送失败")
    private Integer pushResult;

    /**
     * 环节状态：0-未签名，1-进行中，2-已拒绝，3-已过期，4-已撤回，5-已完成
     */
    @ApiModelProperty(value = "环节状态：0-未签名，1-进行中，2-已拒绝，3-已过期，4-已撤回，5-已完成")
    private Integer segmentStatus;

    @ApiModelProperty(value = "环节状态描述")
    private Integer segmentStatusDesc;

    /**
     * 签名环节指定签名人列表
     */
    @ApiModelProperty(value = "签名人")
    private List<CoordSignerDto> segmentSigners;


}
