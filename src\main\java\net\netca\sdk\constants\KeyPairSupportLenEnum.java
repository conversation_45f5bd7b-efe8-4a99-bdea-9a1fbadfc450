package net.netca.sdk.constants;

public enum KeyPairSupportLenEnum implements IBaseEnum {
    LENGTH256(256, "256"),
    LENGTH1024(1024, "1024"),
    LENGTH2048(2048, "2048"),
    LENGTH4096(4096, "4096");


    private Integer code;
    private String description;

    private KeyPairSupportLenEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static boolean isSuportLength(Integer length) {
        if (LENGTH256.getCode().equals(length) &&
                LENGTH1024.getCode().equals(length) &&
                LENGTH2048.getCode().equals(length) &&
                LENGTH4096.getCode().equals(length)) {
            return false;
        }
        return true;
    }
}
