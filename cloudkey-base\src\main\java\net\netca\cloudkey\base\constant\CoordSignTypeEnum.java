package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 协同签名类型枚举
 * 0-P7签名，1-电子签章
 */
@Getter
@AllArgsConstructor
public enum CoordSignTypeEnum implements IBaseEnum {

    /**
     * P7签名
     */
    P7(0, "P7签名"),
    SEAL(1, "电子签章");

    private final Integer code;
    private final String description;



}
