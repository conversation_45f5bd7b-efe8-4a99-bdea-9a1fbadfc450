package net.netca.cloudkey.base.dto.operator;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.operator.SignatureDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @descriptions: 用于 业务平台自动审核 的 证书请求 的签名
 * @date: 2019/8/28
 * @author: SaromChars
 */
@Data
@NoArgsConstructor
public class RequestSignatureDTO extends SignatureDTO {

    @NotNull(message = "requestTempId 不能为空")
    private Integer requestTempId;

    @NotBlank(message = "证书请求自动审核的操作员签名不能为空")
    private String requestSignature;
}
