package net.netca.test.cert;

import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.SubjectInfo;
import net.netca.sdk.message.cmp.ModifiedCertReqMessage;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2020-04-22 17:24
 */
public class TestModifiedCert extends TestCmpProtocol {
    @Test
    public void testNormalModifiedCert() throws Exception {
        String subject = "CN=test modifiedCert,L=test L,C=CN,JurisdictionState=test JurisdictionState,Name=test name,organizationIdentifier=test organizationIdentifier,description=test description";
        SubjectInfo subjectInfo = SubjectInfo.builder()
                .c("CN").cn("test modifiedCert").l("test L").name("test name")
                .organizationIdentifier("test organizationIdentifier").description("test description")
                .jurisdictionState("test JurisdictionState")
                .build();
        CmpRespResult cmpRespResult = getModifiedCertCmpRespResult(subjectInfo);
        Assert.assertNotNull(cmpRespResult.getSignCertPem());
//        Assert.assertNotNull(cmpRespResult.getEncCertPem());
        Assert.assertNotNull(cmpRespResult.getSignCertDer());
//        Assert.assertNotNull(cmpRespResult.getEncCertDer());
        X509Certificate certificate = new X509Certificate(cmpRespResult.getSignCertDer());
        Assert.assertEquals(subject, certificate.getSubject().getLdapName());
    }




    @Test
    public void testPoll() throws Exception {
        String subject = "CN=test modifiedCert,L=test L,C=CN";
        SubjectInfo subjectInfo = SubjectInfo.builder().c("cn").cn("test modifiedCert").l("test L").build();
        CmpRespResult modifiedCertCmpRespResult = getModifiedCertCmpRespResult(subjectInfo);
        Assert.assertNotNull(modifiedCertCmpRespResult);
        CmpRespResult pollCmpRespResult = getPollReqCmpRespResult(modifiedCertCmpRespResult.getRespCertRequestId(), BusinessTypeEnum.MODIFICATION);
        Assert.assertEquals(modifiedCertCmpRespResult.getSignCertPem(), pollCmpRespResult.getSignCertPem());
        Assert.assertEquals(modifiedCertCmpRespResult.getEncCertPem(), pollCmpRespResult.getEncCertPem());
        Assert.assertArrayEquals(modifiedCertCmpRespResult.getSignCertDer(), pollCmpRespResult.getSignCertDer());
        Assert.assertArrayEquals(modifiedCertCmpRespResult.getEncCertDer(), pollCmpRespResult.getEncCertDer());
        X509Certificate certificate = new X509Certificate(pollCmpRespResult.getSignCertDer());
        Assert.assertEquals(subject, certificate.getSubject().getLdapName());
    }

    private CmpRespResult getModifiedCertCmpRespResult(SubjectInfo subject) throws Exception {
        ModifiedCertReqMessage modifiedCertReqMessage = getModifiedCertReqMessage(subject);
        SingleCertReqContext context = new SingleCertReqContext(modifiedCertReqMessage);
        keyUpdateReqMessageEncoder.encode(context, modifiedCertReqMessage);
        PKIMessages pkiMessages = context.getPKIMessages();
        Assert.assertNotNull(pkiMessages);
        byte[] reqData = pkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] repsData = send(reqData);
        Assert.assertNotNull(repsData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(repsData));
        PKIMessage[] pkiMessageArray = respPkiMessages.toPKIMessageArray();
        keyUpdateReqMessageDecoder.decode(context, pkiMessageArray[0]);
        return context.getCmpRespResult();
    }


    private ModifiedCertReqMessage getModifiedCertReqMessage(SubjectInfo subject) throws Exception {
        X509Certificate signCert = getNewSignCert();
        // 获取需要变更信息的证书
        Assert.assertNotNull(signCert);
        // 待变更信息的证书的证书序列号
        String serialNumber = Hex.encode(false, signCert.getSerialNumber());
        // 被续期证书的证书颁发者
        String issuer = signCert.getIssuer().getLdapName();
        final long certRequestId =  new Random().nextInt(1000);
        // 模拟参数，具体值自行决定
        String senderStr = "OU=test";
        String recipientStr = "CN=netca";
        return ModifiedCertReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .serialNumber(serialNumber)
                .issuer(issuer)
                .subject(subject)
                .certRequestId(certRequestId)
                .build();
    }



}
