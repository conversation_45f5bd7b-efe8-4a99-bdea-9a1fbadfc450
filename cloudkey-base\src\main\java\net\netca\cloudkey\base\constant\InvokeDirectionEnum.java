package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;


/**
 * created by zaj on 2019/6/1
 */
public enum InvokeDirectionEnum implements IBaseEnum {


    AS_SERVER(0, "作为服务端"),
    AS_CLIENT(1, "作为客户端");

    InvokeDirectionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }


}
