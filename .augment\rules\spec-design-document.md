---
type: "manual"
---

### 2. Create Feature Design Document

After the user approves the Requirements, you should develop a comprehensive design document based on the feature requirements, conducting necessary research during the design process.
The design document should be based on the requirements document, so ensure it exists first.

**Constraints:**

- The model MUST use the `view` tool to check if '.kiro/specs/{feature_name}/design.md' file exists
- The model MUST use the `save-file` tool to create '.kiro/specs/{feature_name}/design.md' file if it doesn't already exist
- The model MUST identify areas where research is needed based on the feature requirements
- The model MUST conduct research using available tools (`web-search`, `codebase-retrieval`, `web-fetch`) and build up context in the conversation thread
- The model SHOULD NOT create separate research files, but instead use the research as context for the design and implementation plan
- The model MUST summarize key findings that will inform the feature design
- The model SHOULD cite sources and include relevant links in the conversation
- The model MUST create a detailed design document at 'specs/{feature_name}/design.md'
- The model MUST incorporate research findings directly into the design process
- The model MUST include the following sections in the design document:
  - Overview
  - Architecture
  - Components and Interfaces
  - Data Models
  - Error Handling
  - Testing Strategy
- The model SHOULD use the `render-mermaid` tool to create diagrams or visual representations when appropriate
- The model MUST ensure the design addresses all feature requirements identified during the clarification process
- The model SHOULD highlight design decisions and their rationales
- The model MAY ask the user for input on specific technical decisions during the design process
- After updating the design document, the model MUST ask the user "Does the design look good? If so, we can move on to the implementation plan."
- The model MUST use the `str-replace-editor` tool to make modifications to the design document if the user requests changes or does not explicitly approve
- The model MUST ask for explicit approval after every iteration of edits to the design document
- The model MUST NOT proceed to the implementation plan until receiving clear approval (such as "yes", "approved", "looks good", etc.)
- The model MUST continue the feedback-revision cycle until explicit approval is received
- The model MUST incorporate all user feedback into the design document before proceeding
- The model MUST offer to return to feature requirements clarification if gaps are identified during design

**Tool Usage Guidelines:**
- Use `view` tool to examine existing project structure and related files
- Use `codebase-retrieval` tool to understand existing codebase patterns and architecture
- Use `web-search` and `web-fetch` tools for external research when needed
- Use `save-file` tool to create the initial design document
- Use `str-replace-editor` tool to modify the design document based on feedback
- Use `render-mermaid` tool to create architectural diagrams, flowcharts, or component diagrams
- Optionally use task management tools to track design progress

**Interaction Flow:**
1. Check if requirements document exists and review its contents
2. Identify research areas needed for the design
3. Conduct necessary research using available tools
4. Create comprehensive design document incorporating research findings
5. Ask user for feedback on the design
6. Iterate on design based on user feedback using `str-replace-editor`
7. Repeat steps 5-6 until explicit approval is received
8. Proceed to implementation planning phase after approval

**Design Document Template:**
```markdown
# {Feature Name} Design Document

## 1. Overview
[Brief description of the feature and its purpose]

## 2. Architecture
[High-level architecture description with diagrams if needed]

## 3. Components and Interfaces
[Detailed component breakdown and their interfaces]

## 4. Data Models
[Data structures, schemas, and relationships]

## 5. Error Handling
[Error scenarios and handling strategies]

## 6. Testing Strategy
[Testing approach and coverage plan]

## 7. Implementation Notes
[Key implementation considerations and decisions]