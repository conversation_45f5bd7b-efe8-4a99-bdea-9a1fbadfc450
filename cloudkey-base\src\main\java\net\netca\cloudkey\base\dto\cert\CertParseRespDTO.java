package net.netca.cloudkey.base.dto.cert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class CertParseRespDTO {

    private Integer version;

    private Integer signAlgo;

    private Integer publicAlgo;

    private Integer publicBits;

    private Integer keyUsage;

    private Integer publicEccCurve;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validityEnd;

    private String sn;

    private List<CertThumbPrint> thumbprints;

    private IssuerDTO issuer;

    private SubjectDTO subject;

    private List<ExtensionDTO> extensions;
}
