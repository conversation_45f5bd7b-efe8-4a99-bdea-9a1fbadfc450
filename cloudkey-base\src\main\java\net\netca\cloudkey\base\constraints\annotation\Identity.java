package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.IdentityValidator;
import net.netca.cloudkey.base.dto.abstractclass.IIdentityReqDTO;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对证件类型+证件号码的校验注解
 * 根据type字段区分是机构证件还是个人证件
 * 注解在类上，类需要实现IIdentityReqDTO
 * @see IIdentityReqDTO
 * @see IdentityValidator
 */
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy={IdentityValidator.class})
@Documented
public @interface Identity {

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    // organization, person
    String type() default "";

}
