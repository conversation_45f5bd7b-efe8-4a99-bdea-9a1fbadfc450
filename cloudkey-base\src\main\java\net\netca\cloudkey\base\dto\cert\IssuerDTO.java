package net.netca.cloudkey.base.dto.cert;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
public class IssuerDTO {

    private String issuer;

    private String displayName;

    private String hexEncode;

    private List<String> c;

    private List<String> o;

    private List<String> ou;

    private List<String> cn;

    private List<String> email;

    private List<String> st;

    private List<String> l;

    private List<String> dc;

}
