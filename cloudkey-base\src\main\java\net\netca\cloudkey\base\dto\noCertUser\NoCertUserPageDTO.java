package net.netca.cloudkey.base.dto.noCertUser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;
import net.netca.cloudkey.base.annotation.SecurityDataField;

import java.util.Date;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2024-02-21 17:55:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Secret
public class NoCertUserPageDTO {


    /**
     * 用户
     */
    private Integer id;

    /**
     * 用户唯一标识
     */
    private String uid;


    /**
     * 用户名
     */
    private String name;

    /**
     * 手机号码
     */
    @SecurityDataField
    private String phone;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 签章图片
     */
    private String sealPic;

    /**
     * 院区ID
     */
    private Integer districtId;

}
