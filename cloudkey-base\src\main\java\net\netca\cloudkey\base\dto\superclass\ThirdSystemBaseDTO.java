package net.netca.cloudkey.base.dto.superclass;

import lombok.Data;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class ThirdSystemBaseDTO extends TimestampToken {

    @NotBlank(message = "authId参数不能为空")
    private String authId;

    @NotNull(message = "authType参数不能为空")
    private Integer authType;

    private String authCode;

    @NotBlank(message = "requestInfoEncode参数不能为空")
    private String requestInfoEncode;

    @NotBlank(message = "systemSignature参数不能为空")
    private String systemSignature;

    /**
     * 附加信息
     */
    @Size(max = 60000, message = "attachment参数长度不能超过60000个字符")
    private String attachment;

}
