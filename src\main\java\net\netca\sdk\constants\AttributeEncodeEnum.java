package net.netca.sdk.constants;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1Exception;
import net.netca.pki.encoding.asn1.ASN1Object;
import net.netca.pki.encoding.asn1.pki.PostalAddress;
import net.netca.sdk.util.DateUtils;

import java.util.Objects;

public enum AttributeEncodeEnum {
    BMP_STRING("BMPString") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.BMPString(value);
        }
    },

    VISIBLE_STRING("VisibleString") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.VisibleString(value);
        }
    },

    IA5_STRING("IA5String") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.IA5String(value);
        }
    },

    PRINTABLE_STRING("PrintableString") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.PrintableString(value);
        }
    },

    UTF8_STRING("UTF8String") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.UTF8String(value);
        }
    },


    DIRECTORY_STRING("DirectoryString") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            try {
                return net.netca.pki.encoding.asn1.pki.DirectoryString.NewUTF8String(value).getASN1Object();
            } catch (PkiException e) {
                throw new ASN1Exception(e.getMessage());
            }
        }
    },

    GENERALIZED_TIME("GeneralizedTime") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            return new net.netca.pki.encoding.asn1.GeneralizedTime(DateUtils.stringToDate(value));
        }
    },

    POSTAL_ADDRESS("PostalAddress") {
        @Override
        public ASN1Object convert2ASN1Object(String value) throws ASN1Exception {
            try {
				net.netca.pki.encoding.asn1.pki.PostalAddress postalAddress = new PostalAddress();
				postalAddress.add(net.netca.pki.encoding.asn1.pki.DirectoryString.NewUTF8String(value));
				return postalAddress.getASN1Object();
            } catch (PkiException e) {
                throw new ASN1Exception(e.getMessage());
            }
        }
    };

    private final String encode;


    AttributeEncodeEnum(String encode) {
        this.encode = encode;
    }

    public static AttributeEncodeEnum getAttributeEncode(String encode) {
        for (AttributeEncodeEnum attributeEncode : AttributeEncodeEnum.values()) {
            if (Objects.equals(attributeEncode.encode, encode)) {
                return attributeEncode;
            }
        }
        return null;
    }

    public abstract ASN1Object convert2ASN1Object(String value) throws ASN1Exception;


    public String getEncode() {
        return encode;
    }
}
