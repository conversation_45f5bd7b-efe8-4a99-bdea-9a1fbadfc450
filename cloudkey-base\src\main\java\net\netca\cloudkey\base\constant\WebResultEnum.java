package net.netca.cloudkey.base.constant;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定义返回给外部的status，以及对应的msg
 * 参考:
 * {
 * "responseResult":{
 * "status":0,
 * "msg":"成功"
 * },
 * "contents":{}
 * }
 */
public enum WebResultEnum {

    // 系统通用
    SUCCESS(0, "成功", true),
    ERROR(-1, "系统错误", true),

    // 对外接口通用
    USER_TOKEN_EXPIRED(-2, "身份未认证或认证已超时"),
    PIN_ERROR(-3, "口令错误"),
    USER_LOCKED(-4, "账号被锁住,请在网证通云密钥APP主界面操作”找回密码”进行自助解锁，或联系单位管理员进行解锁"),
    LOGIN_ERROR_REFUSE(-5, "身份验证错误太频繁,被限制"),
    SMS_CODE_ERROR(-6, "短信验证码错误"),
    USER_VERIFY_INFO_EXPIRED(-7, "验证信息未生成或失效，请刷新二维码后重新扫码"),
    SMS_SEND_ERROR(-8, "下发短信失败"),
    CERTIFICATE_EXPIRED(-9, "证书未生效或已过期"),
    THIRD_SYSTEM_REPLAY(-37, "外部系统接口请求参数防重放检查异常"),
    CERTIFICATE_FREEZE(-38, "证书已冻结"),
    THIRD_SYSTEM_CAN_NOT_ACCESS_INTERFACE(-39, "外部系统无对应接口访问权限(%d, %s)"),

    //授权部分,
    ILLEGAL_AUTH_OPERATION(-10, "授权用户非法操作"),
    NO_AUTH_OPERATION(-35, "当前系统用户没有此操作权限"),
    NO_AUTH_PROJECT(-42, "当前系统用户没有此项目(%s)操作权限"),

    //电子签名系统对接
    RANDOM_REGISTER_ERROR(-11, "随机数注册失败"),
    RANDOM_REGISTER_EXPIRED(-12, "随机数注册过期"),

    SMS_CODE_EXPIRED(-13, "短信验证码错误或已过期"),
    LOGIN_APPLY_EXPIRED(-14, "登录消息已过期"),

    CERTIFICATE_HAS_UPDATED(-16, "证书状态已经更新，请更新最新证书再进行操作"),
    CERTIFICATE_HAS_REVOKED(-21, "证书已注销，不允许使用"),

    TIMESTAMP_INVALID(-22, "时间戳误差过大"),
    TIMESTAMP_TOKEN_REPEAT(-23, "重复请求"),

    IMG_VALIDCODE_EXPIRED(-17, "图形验证码已过期"),

    PARAM_ERROR(100, "入参错误"),

    // vue页面专用
    UNLOGIN(-15, "系统用户未通过登录验证或验证已过期", true),
    VERIFYSIGNATUREFAILED(-19, "验证操作员签名失败", true),
    SSO_LOGOUT_ERROR(-18, "您已在其他地方登录，请退出后重新登录", true),

    //签名验签部分
    VERIFY_SIGNATURE_FAILED(-20, "签名验证失败"),

    //消息推送异常
    NOTIFICATION_PUSH_NOT_SUPPORT(-30, "不支持消息通知"),

    SEAL_PIC_UNREVIEWED(-31, "签章图片未审核"),
    SEAL_PIC_REVIEWED_NOT_PASSED(-32, "签章图片审核不通过"),
    SEAL_PIC_DOWNLOAD_FAILURE(-33, "签章图片下载失败"),

    //设备绑定
    DEVICE_NOT_BIND(-34, "用户尚未绑定设备"),

    //数据库异常
    DB_ERROR(-36, "持久层异常"),

    // 设备解绑失败
    DEVICE_UNBIND_FAILURE(-40, "设备解绑失败"),

    // 设备绑定失败
    DEVICE_BIND_FAILURE(-41, "设备绑定失败：%s"),

    // 未配置短信平台
    NO_CONFIGURE_SMS_PLATFORM(-42, "项目（%s）未配置短信平台"),

    NO_STRONG_PIN(-43, "原密码已失效，请到“网证通云密钥APP“_”找回密码”，进行密码重置，错误原因:密码强度不足"),

    CORP_WECHAT_LOGIN_IDENTITY_INVALID(-44, "唯一标识未生成或失效，请生成新的云密钥企业微信登录二维码并进行扫描"),

    APPLICATION_NOT_EXIST(-45, "应用不存在" ),

    PRIMARY_DEVICE_FREEZE(-46, "主设备已被冻结，请进行激活操作"),
    ACCESSORY_DEVICE_FREEZE(-47, "附属设备已被冻结, 请在主设备APP的设备管理中进行激活操作"),
    PIN_EXPIRED(-48, "原密码已过期，请重置密码"),
    // 附属设备绑定失败
    ACCESSORY_DEVICE_BIND_FAILURE(-49, "附属设备绑定失败：%s"),
    ;

    WebResultEnum(Integer code, String description, boolean adminView) {
        this.code = code;
        this.description = description;
        this.adminView = adminView;
    }

    WebResultEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
        this.adminView = false;
    }

    private Integer code;
    private String description;
    private boolean adminView;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAdminView() {
        return adminView;
    }

    public void setAdminView(boolean adminView) {
        this.adminView = adminView;
    }

    public static List<Map<String, Object>> getAdminViewCodeOption() {
        return Arrays.stream(WebResultEnum.values())
                .filter(webResultEnum -> webResultEnum.adminView)
                .map(webResultEnum -> {
                    LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                    map.put("value", webResultEnum.getDescription());
                    map.put("key", webResultEnum.getCode());
                    return map;
                }).collect(Collectors.toList());
    }
}
