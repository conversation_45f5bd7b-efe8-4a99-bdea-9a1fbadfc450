package net.netca.test.asn1.format;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1Exception;
import net.netca.pki.encoding.asn1.ASN1TypeManager;
import net.netca.pki.encoding.asn1.SetOf;
import net.netca.pki.encoding.asn1.SetOfType;
import net.netca.pki.encoding.asn1.pki.RelativeDistinguishedName;
import net.netca.pki.encoding.asn1.pki.X500Name;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class RDNSequenceFormat {
	private List<RelativeDistinguishedNameFormat> relativeDistinguishedNames;

	public List<RelativeDistinguishedNameFormat> getRelativeDistinguishedNames() {
		return relativeDistinguishedNames;
	}

	public void setRelativeDistinguishedNames(List<RelativeDistinguishedNameFormat> relativeDistinguishedNames) {
		this.relativeDistinguishedNames = relativeDistinguishedNames;
	}

	public X500Name generateDN() throws ASN1Exception, PkiException {
		 List<AttributeTypeValueFormat> attributeTypeValueFormats = new ArrayList<AttributeTypeValueFormat>();
		 List<AttributeTypeValueFormat> ats = null;
		 for(RelativeDistinguishedNameFormat relativeDistinguishedNameFormat:relativeDistinguishedNames){
			 ats =  relativeDistinguishedNameFormat.getAttributeTypeValueFormat();
			 attributeTypeValueFormats.addAll(ats);
		 }
		 
		 Collections.sort(attributeTypeValueFormats);
		 
		 X500Name subject=new X500Name();

		for (AttributeTypeValueFormat atf : attributeTypeValueFormats) {
			subject.add(generateRelativeDistinguishedName(atf));
		}
		 
		 return subject;
	}
	
	public X500Name generate() throws PkiException {
		X500Name subject=new X500Name();
		RelativeDistinguishedName rdn = null;
		for(RelativeDistinguishedNameFormat relativeDistinguishedNameFormat:relativeDistinguishedNames){
			 rdn = relativeDistinguishedNameFormat.generateRelativeDistinguishedName();
			 subject.add(rdn);
		}
		return subject;
	}
	
	private RelativeDistinguishedName generateRelativeDistinguishedName(AttributeTypeValueFormat atf) throws ASN1Exception, PkiException {
		SetOf setOf=new SetOf((SetOfType) ASN1TypeManager.getInstance().get("RelativeDistinguishedName"));
		setOf.add(atf.findAttributeTypeAndValue().getASN1Object());
		return new RelativeDistinguishedName(setOf);
	}
}
