package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 国密印章状态枚举
 * -1-制章失败，0-尚未制章，1-制章完成
 */
@Getter
@AllArgsConstructor
public enum GmStampStatusEnum implements IBaseEnum {

    /**
     * 制章失败
     */
    FAIL(-1, "制章失败"),

    /**
     * 尚未制章
     */
    NOT_START(0, "尚未制章"),
    /**
     * 制章完成
     */
    COMPLETED(1, "制章完成"),
    ;

    private final Integer code;
    private final String description;


}
