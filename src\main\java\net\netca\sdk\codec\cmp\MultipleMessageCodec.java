package net.netca.sdk.codec.cmp;


import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.entity.RequestContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.cmp.CmpMessage;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;

import java.util.List;

/**
 * 多种消息的组合编码器
 *
 * @author: zys
 * @date: 2020/3/9 18:10
 */
public class MultipleMessageCodec implements MessageEncoder<List<CmpMessage>>, MessageDecoder<PKIMessages> {

    private MultipleMessageEncoder multipleMessageEncoder;

    private MultipleMessageDecoder multipleMessageDecoder;

    private MultipleMessageCodec(MultipleMessageEncoder multipleMessageEncoder) {
        this.multipleMessageEncoder = multipleMessageEncoder;
    }

    private MultipleMessageCodec(MultipleMessageDecoder multipleMessageDecoder) {
        this.multipleMessageDecoder = multipleMessageDecoder;
    }

    public static MultipleMessageCodec createEncoder(List<MessageEncoder<CmpMessage>> messageEncoders) {
        return new MultipleMessageCodec(new MultipleMessageEncoder(messageEncoders));
    }

    public static MultipleMessageCodec createDecoder(List<MessageDecoder<PKIMessage>> decoders) {
        return new MultipleMessageCodec(new MultipleMessageDecoder(decoders));
    }

    @Override
    public void decode(RequestContext context, PKIMessages message) throws Exception {
        multipleMessageDecoder.decode(context, message);
    }

    @Override
    public void encode(RequestContext context, List<CmpMessage> message) throws Exception {
        multipleMessageEncoder.encode(context, message);
    }


    final static class MultipleMessageEncoder implements MessageEncoder<List<CmpMessage>> {

        private List<MessageEncoder<CmpMessage>> messageEncoders;


        MultipleMessageEncoder(List<MessageEncoder<CmpMessage>> messageEncoders) {
            this.messageEncoders = messageEncoders;
        }

        @Override
        public void encode(RequestContext context, List<CmpMessage> messages) throws Exception {
            if (context instanceof MultipleCmpReqContext) {
                MultipleCmpReqContext multipleCmpReqContext = (MultipleCmpReqContext) context;
                List<SingleCertReqContext> contexts = multipleCmpReqContext.getContexts();
                for (CmpMessage message : messages) {
                    SingleCertReqContext singleCertReqContext = new SingleCertReqContext(message);
                    for (MessageEncoder<CmpMessage> messageEncoder : messageEncoders) {
                        messageEncoder.encode(singleCertReqContext, message);
                        if (singleCertReqContext.isEncoded()) {
                            contexts.add(singleCertReqContext);
                            break;
                        }
                    }
                }
            }
        }

        public void destroy() {
            for (MessageEncoder<CmpMessage> messageEncoder : messageEncoders) {
                ((CmpMessageCodec.CmpMessageToAnyObjectEncoder) messageEncoder).destroy();
            }
        }
    }

    final static class MultipleMessageDecoder implements MessageDecoder<PKIMessages> {
        private List<MessageDecoder<PKIMessage>> decoders;


        public MultipleMessageDecoder(List<MessageDecoder<PKIMessage>> decoders) {
            this.decoders = decoders;
        }

        @Override
        public void decode(RequestContext context, PKIMessages decodedMessages) throws Exception {
            if (context instanceof MultipleCmpReqContext) {
                MultipleCmpReqContext multipleCmpReqContext = (MultipleCmpReqContext) context;
                List<SingleCertReqContext> singleCertReqContexts = multipleCmpReqContext.getContexts();
                PKIMessage[] respPKIMessages = decodedMessages.toPKIMessageArray();
                if (singleCertReqContexts.size() != respPKIMessages.length) {
                    throw new IllegalArgumentException("the count of PKIMessage in PKIMessages is illegal");
                }
                for (int i = 0; i < respPKIMessages.length; i++) {
                    SingleCertReqContext singleCertReqContext = singleCertReqContexts.get(i);
                    for (MessageDecoder<PKIMessage> decoder : decoders) {
                        decoder.decode(singleCertReqContext, respPKIMessages[i]);
                        if (singleCertReqContext.isDecoded()) {
                            break;
                        }
                    }
                }
            }
        }

        public void destroy() {
            for (MessageDecoder<PKIMessage> decoder : decoders) {
                ((CertConfirmReqMessageCodec.CertConfirmReqMessageDecoder) decoder).destroy();
            }
        }
    }
}
