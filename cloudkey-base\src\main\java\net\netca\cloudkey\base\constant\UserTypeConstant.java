package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Arrays;
import java.util.Optional;

/**
 * <p>
 * 对外接口的系统用户
 * 目前系统只有两种用户：
 * 0：证书用户
 * 1：被授权用户
 * </p>
 * created by zaj on 2019/4/28
 */
public enum UserTypeConstant implements IBaseEnum {

    BELONG_CERT_USER(0, "证书用户"),
    BELONG_AUTHORIZED_USER(1, "被授权用户");

    UserTypeConstant(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static UserTypeConstant getUserTypeConstant(Integer code) {
        Optional<UserTypeConstant> first = Arrays.stream(UserTypeConstant.values())
                .filter(userTypeConstant -> userTypeConstant.code.equals(code))
                .findFirst();

        if (!first.isPresent()) {
            throw new CloudKeyRuntimeException(WebResultEnum.ERROR.getCode(), "非法的userTypeConstant");
        }

        return first.get();
    }

}
