# BpmsManager 接口抽象设计文档

## 概述

本设计文档描述了如何将 `BpmsManager` 类重构为基于接口的架构，通过配置驱动的方式选择具体实现，以提高系统的可扩展性和可配置性。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[调用方类] --> B[ICaManager接口]
    B --> C[CaManagerFactory]
    C --> D[ConfigKeyValueCacheUtil]
    C --> E[DefaultCaManager]
    C --> F[AlternativeCaManager]

    D --> G[配置数据库]
    E --> H[业务平台服务]
    F --> I[其他业务平台服务]
```

### 模块分层

1. **接口层 (cloudkey-base)**

   - `ICaManager` - 核心业务接口
   - `CaManagerFactory` - 工厂类

2. **实现层 (cloudkeyserver)**

   - `DefaultCaManager` - 默认实现（当前 BpmsManager 重构）
   - 其他实现类（未来扩展）

3. **配置层**
   - 配置项定义
   - 配置读取逻辑

## 组件设计

### 1. ICertificateLifecycleManager 接口

```java
package net.netca.cloudkey.base.manager;

import net.netca.cloudkey.base.po.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.netcasvs.jni.KeyPairWrapper;

/**
 * 证书生命周期管理接口
 * 提供证书申请、查询、下载、注销等核心功能
 */
public interface ICertificateLifecycleManager {

    /**
     * 申请证书
     * @param registerRequest 证书申请请求
     * @param opSignature 操作签名
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param signKeyPair 签名密钥对
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param sendSms 是否发送短信
     * @return 业务平台响应
     */
    NetcaBpmsResponse applyCertificate(RegisterRequest registerRequest,
                                      String opSignature,
                                      BusinessUser businessUser,
                                      ConfigProject configProject,
                                      KeyPairWrapper signKeyPair,
                                      String userPin,
                                      String adminPin,
                                      boolean sendSms);

    /**
     * 申请事件证书
     * @param registerRequest 证书申请请求
     * @param opSignature 操作签名
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param signKeyPair 签名密钥对
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param url 申请URL
     * @return 业务平台响应
     */
    NetcaBpmsResponse applyEventCertificate(RegisterRequest registerRequest,
                                           String opSignature,
                                           BusinessUser businessUser,
                                           ConfigProject configProject,
                                           KeyPairWrapper signKeyPair,
                                           String userPin,
                                           String adminPin,
                                           String url);

    /**
     * 查询证书申请状态
     * @param systemId 系统ID
     * @param requestId 请求ID
     * @return 业务平台响应
     * @throws Exception 查询异常
     */
    NetcaBpmsResponse queryCertificateApplicationStatus(String systemId, String requestId) throws Exception;

    /**
     * 下载证书
     * @param requestId 请求ID
     * @param systemId 系统ID
     * @return 业务平台响应
     * @throws Exception 下载异常
     */
    NetcaBpmsResponse downloadCertificate(String requestId, String systemId) throws Exception;

    /**
     * 解密管理员PIN
     * @param encryptedPin 加密的PIN
     * @param systemId 系统ID
     * @return 解密后的PIN
     */
    String decryptAdministratorPin(String encryptedPin, String systemId);

    /**
     * 注销证书
     * @param businessCertAttribute 证书属性
     * @param configProject 项目配置
     * @param authorityOperator 授权操作员
     * @return 注销结果
     * @throws Exception 注销异常
     */
    Boolean revokeCertificate(BusinessCertAttribute businessCertAttribute,
                             ConfigProject configProject,
                             AuthorityOperator authorityOperator) throws Exception;

    /**
     * 生成个人证书申请请求
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param configLinkman 联系人配置
     * @return 注册请求
     */
    RegisterRequest generatePersonalCertificateRequest(BusinessUser businessUser,
                                                      ConfigProject configProject,
                                                      ConfigLinkman configLinkman);

    /**
     * 生成员工证书申请请求
     * @param businessUser 业务用户
     * @param businessOrganization 业务组织
     * @param configProject 项目配置
     * @param configLinkman 联系人配置
     * @return 注册请求
     */
    RegisterRequest generateEmployeeCertificateRequest(BusinessUser businessUser,
                                                      BusinessOrganization businessOrganization,
                                                      ConfigProject configProject,
                                                      ConfigLinkman configLinkman);

    /**
     * 生成组织证书申请请求
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param configLinkman 联系人配置
     * @return 注册请求
     */
    RegisterRequest generateOrganizationCertificateRequest(BusinessUser businessUser,
                                                          ConfigProject configProject,
                                                          ConfigLinkman configLinkman);
}
```

### 2. 证书生命周期管理器工厂类

```java
package net.netca.cloudkey.base.manager;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.constant.ConfigKeyValueEnum;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.util.ConfigKeyValueCacheUtil;
import net.netca.cloudkey.base.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 证书生命周期管理器工厂类
 * 通过配置动态选择具体实现
 */
@Slf4j
@Component
public class CertificateLifecycleManagerFactory {

    @Autowired
    private ConfigKeyValueCacheUtil configKeyValueCacheUtil;

    @Autowired
    private ApplicationContext applicationContext;

    private static final String DEFAULT_IMPLEMENTATION = "default";

    private ICertificateLifecycleManager currentManager;

    @PostConstruct
    public void init() {
        refreshManager();
        logCurrentImplementation();
    }

    /**
     * 获取证书生命周期管理器实例
     * @return 证书生命周期管理器
     */
    public ICertificateLifecycleManager getCertificateLifecycleManager() {
        if (currentManager == null) {
            refreshManager();
        }
        return currentManager;
    }

    /**
     * 刷新管理器实例
     * 支持配置热更新
     */
    public void refreshManager() {
        try {
            String implementationType = getImplementationTypeFromConfig();
            currentManager = createCertificateLifecycleManagerImplementation(implementationType);
            log.info("成功创建证书生命周期管理器实例: {}",
                    currentManager.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("创建证书生命周期管理器失败，使用默认实现", e);
            currentManager = getDefaultImplementation();
        }
    }

    /**
     * 从配置中获取实现类型
     */
    private String getImplementationTypeFromConfig() {
        String implementationType = configKeyValueCacheUtil.selectConfigValueByKey(
            ConfigKeyValueEnum.CERTIFICATE_LIFECYCLE_MANAGER_IMPLEMENTATION_TYPE.getCode());

        if (CommonUtil.isStringEmpty(implementationType)) {
            log.info("未配置证书生命周期管理器实现类型，使用默认值: {}", DEFAULT_IMPLEMENTATION);
            implementationType = DEFAULT_IMPLEMENTATION;
        }

        log.debug("从配置获取的证书生命周期管理器实现类型: {}", implementationType);
        return implementationType;
    }

    /**
     * 根据类型创建具体实现
     */
    private ICertificateLifecycleManager createCertificateLifecycleManagerImplementation(String type) {
        // 目前只支持默认实现，未来可以根据需要扩展其他实现
        switch (type.toLowerCase()) {
            case "default":
            case "bpms":  // 向后兼容原有配置
            case "":      // 空配置默认使用原有实现
            case null:    // 无配置默认使用原有实现
                return getImplementationBean("defaultCertificateLifecycleManager");

            default:
                log.warn("未知的证书生命周期管理器实现类型: {}，使用默认实现（原 BpmsManager 实现）", type);
                return getDefaultImplementation();
        }
    }

    /**
     * 从Spring容器获取实现Bean
     */
    private ICertificateLifecycleManager getImplementationBean(String beanName) {
        try {
            return applicationContext.getBean(beanName, ICertificateLifecycleManager.class);
        } catch (Exception e) {
            log.error("获取Bean失败: {}", beanName, e);
            throw new CloudKeyRuntimeException("无法获取证书生命周期管理器实现: " + beanName, e);
        }
    }



    /**
     * 获取默认实现
     */
    private ICertificateLifecycleManager getDefaultImplementation() {
        return getImplementationBean("defaultCertificateLifecycleManager");
    }

    /**
     * 记录当前实现信息
     */
    private void logCurrentImplementation() {
        if (currentManager != null) {
            log.info("当前证书生命周期管理器实现: {}",
                    currentManager.getClass().getName());
        }
    }

    /**
     * 检查配置是否发生变化
     */
    public boolean isConfigurationChanged() {
        String currentType = getImplementationTypeFromConfig();
        String actualType = getCurrentImplementationType();
        return !currentType.equals(actualType);
    }

    /**
     * 获取当前实现的类型
     */
    private String getCurrentImplementationType() {
        if (currentManager == null) {
            return DEFAULT_IMPLEMENTATION;
        }

        // 目前只有默认实现
        return DEFAULT_IMPLEMENTATION;
    }
}
```

### 3. 自动配置类

```java
package net.netca.cloudkey.base.config;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import net.netca.cloudkeyserver.manager.DefaultCertificateLifecycleManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 证书生命周期管理器自动配置
 * 注册所有可用的实现Bean，由工厂类根据 ConfigKeyValueCacheUtil 配置选择使用哪个
 */
@Slf4j
@Configuration
public class CertificateLifecycleAutoConfiguration {

    /**
     * 默认证书生命周期管理器
     * 基于BPMS业务平台的实现（原 BpmsManager 的重构版本）
     */
    @Bean("defaultCertificateLifecycleManager")
    public ICertificateLifecycleManager defaultCertificateLifecycleManager() {
        log.info("注册默认证书生命周期管理器实现（原 BpmsManager 实现）");
        return new DefaultCertificateLifecycleManager();
    }
}
```

### 4. 配置枚举扩展

```java
package net.netca.cloudkey.base.constant;

/**
 * 配置键值枚举扩展
 * 添加证书生命周期管理器相关配置项
 */
public enum ConfigKeyValueEnum {

    // 现有配置项保持不变
    BPMS_URL_PREFIX("bpms.url.prefix", "业务平台URL前缀"),
    BPMS_REGISTER_CERT_URL("bpms.register.cert.url", "业务平台注册证书URL"),
    BPMS_REQUEST_SEARCH_URL("bpms.request.search.url", "业务平台请求查询URL"),
    BPMS_DOWNLOAD_CERT_URL("bpms.download.cert.url", "业务平台下载证书URL"),
    BPMS_REVOKE_CERT_URL("bpms.revoke.cert.url", "业务平台注销证书URL"),
    BPMS_DEC_ADMIN_PIN_URL("bpms.dec.admin.pin.url", "业务平台解密管理PIN URL"),

    // 新增证书生命周期管理器配置项（可选配置，默认使用原有实现）
    CERTIFICATE_LIFECYCLE_MANAGER_IMPLEMENTATION_TYPE("certificate.lifecycle.manager.implementation.type", "证书生命周期管理器实现类型");

    private final String code;
    private final String description;

    ConfigKeyValueEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
```

### 5. DefaultCertificateLifecycleManager 实现类

```java
package net.netca.cloudkeyserver.manager;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import net.netca.cloudkey.base.po.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.netcasvs.jni.KeyPairWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 默认证书生命周期管理器实现
 * 基于BPMS业务平台的证书生命周期管理
 *
 * 这是原 BpmsManager 的重构版本，保持所有业务逻辑不变
 */
@Slf4j
@Component("defaultCertificateLifecycleManager")
public class DefaultCertificateLifecycleManager implements ICertificateLifecycleManager {

    // 注入所有必要的依赖服务（与原 BpmsManager 相同）
    @Autowired
    private ThreadLocalEncryptionCardKeyPairManager threadLocalKeyPairManager;
    @Autowired
    private BusinessUserService businessUserService;
    @Autowired
    private BusinessLinkManService businessLinkManService;
    @Autowired
    private BusinessCertService businessCertService;
    @Autowired
    private BusinessRequestService businessRequestService;
    @Autowired
    private BusinessCertAttributeService businessCertAttributeService;
    @Autowired
    private BusinessKeypairInfoService businessKeypairInfoService;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private SystemDeviceCryptoDataUtil systemDeviceCryptoDataUtil;
    @Autowired
    private SmsHandlerContext smsHandlerContext;
    @Autowired
    private CertLifeCycleService certLifeCycleService;
    @Autowired
    private BusinessKeypairInfoEncParameterService businessKeypairInfoEncParameterService;
    @Autowired
    private ConfigKeyValueCacheUtil configKeyValueCacheUtil;
    @Autowired
    private SealWebService sealWebService;
    @Autowired
    private SealPicService sealPicService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NetcaBpmsResponse applyCertificate(RegisterRequest registerRequest,
                                             String opSignature,
                                             BusinessUser businessUser,
                                             ConfigProject configProject,
                                             KeyPairWrapper signKeyPair,
                                             String userPin,
                                             String adminPin,
                                             boolean sendSms) {
        log.info("开始申请证书，用户ID: {}, 项目ID: {}", businessUser.getId(), configProject.getId());

        // 直接调用原 BpmsManager 的 registCert2BpmsBy 方法实现
        // 这里将包含完整的原始业务逻辑
        return registCert2BpmsBy(registerRequest, opSignature, businessUser, configProject,
                                signKeyPair, userPin, adminPin, sendSms);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NetcaBpmsResponse applyEventCertificate(RegisterRequest registerRequest,
                                                  String opSignature,
                                                  BusinessUser businessUser,
                                                  ConfigProject configProject,
                                                  KeyPairWrapper signKeyPair,
                                                  String userPin,
                                                  String adminPin,
                                                  String url) {
        log.info("开始申请事件证书，用户ID: {}, 项目ID: {}, URL: {}",
                businessUser.getId(), configProject.getId(), url);

        // 直接调用原 BpmsManager 的 registEvcntCertBpmsBy 方法实现
        return registEvcntCertBpmsBy(registerRequest, opSignature, businessUser, configProject,
                                    signKeyPair, userPin, adminPin, url);
    }

    @Override
    public NetcaBpmsResponse queryCertificateApplicationStatus(String systemId, String requestId) throws Exception {
        log.info("查询证书申请状态，系统ID: {}, 请求ID: {}", systemId, requestId);

        // 直接调用原 BpmsManager 的 searchBpmsRequestStatusByBpmsSystemIdAndBpmsReqId 方法实现
        return searchBpmsRequestStatusByBpmsSystemIdAndBpmsReqId(systemId, requestId);
    }

    @Override
    public NetcaBpmsResponse downloadCertificate(String requestId, String systemId) throws Exception {
        log.info("下载证书，请求ID: {}, 系统ID: {}", requestId, systemId);

        // 直接调用原 BpmsManager 的 downCertFromBpmsByBpmsReqIdAndBpmsSystemId 方法实现
        return downCertFromBpmsByBpmsReqIdAndBpmsSystemId(requestId, systemId);
    }

    @Override
    public String decryptAdministratorPin(String encryptedPin, String systemId) {
        log.info("解密管理员PIN，系统ID: {}", systemId);

        // 直接调用原 BpmsManager 的 decryptAdminPin 方法实现
        return decryptAdminPin(encryptedPin, systemId);
    }

    @Override
    public Boolean revokeCertificate(BusinessCertAttribute businessCertAttribute,
                                    ConfigProject configProject,
                                    AuthorityOperator authorityOperator) throws Exception {
        log.info("注销证书，证书ID: {}, 项目ID: {}",
                businessCertAttribute.getCertId(), configProject.getId());

        // 直接调用原 BpmsManager 的 revokeCert 方法实现
        return revokeCert(businessCertAttribute, configProject, authorityOperator);
    }

    @Override
    public RegisterRequest generatePersonalCertificateRequest(BusinessUser businessUser,
                                                             ConfigProject configProject,
                                                             ConfigLinkman configLinkman) {
        log.info("生成个人证书申请请求，用户ID: {}, 项目ID: {}",
                businessUser.getId(), configProject.getId());

        // 直接调用原 BpmsManager 的 generatePersonRequest 方法实现
        return generatePersonRequest(businessUser, configProject, configLinkman);
    }

    @Override
    public RegisterRequest generateEmployeeCertificateRequest(BusinessUser businessUser,
                                                             BusinessOrganization businessOrganization,
                                                             ConfigProject configProject,
                                                             ConfigLinkman configLinkman) {
        log.info("生成员工证书申请请求，用户ID: {}, 组织ID: {}, 项目ID: {}",
                businessUser.getId(), businessOrganization.getId(), configProject.getId());

        // 直接调用原 BpmsManager 的 generateEmployeeRequest 方法实现
        return generateEmployeeRequest(businessUser, businessOrganization, configProject, configLinkman);
    }

    @Override
    public RegisterRequest generateOrganizationCertificateRequest(BusinessUser businessUser,
                                                                 ConfigProject configProject,
                                                                 ConfigLinkman configLinkman) {
        log.info("生成组织证书申请请求，用户ID: {}, 项目ID: {}",
                businessUser.getId(), configProject.getId());

        // 直接调用原 BpmsManager 的 generateOrgRequest 方法实现
        return generateOrgRequest(businessUser, configProject, configLinkman);
    }

    // ========== 以下是原 BpmsManager 的所有方法实现 ==========
    // 将原 BpmsManager 的所有方法完整复制到这里，保持业务逻辑不变

    /**
     * 原 registCert2BpmsBy 方法
     */
    @Transactional(rollbackFor = Exception.class)
    private NetcaBpmsResponse registCert2BpmsBy(RegisterRequest registerRequest,
                                               String opSignature,
                                               BusinessUser businessUser,
                                               ConfigProject configProject,
                                               KeyPairWrapper signKeyPair,
                                               String userPin,
                                               String adminPin,
                                               boolean sendSms) {
        // 原方法的完整实现...
        // 这里包含所有原始的业务逻辑代码
        return null; // 实际实现中返回真实响应
    }

    // ... 其他所有原 BpmsManager 的私有方法和辅助方法
    // 保持完全相同的实现，确保业务逻辑不变
}
```

### 6. 使用示例和集成方式

```java
package net.netca.cloudkeyserver.service;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkeyserver.manager.CertificateLifecycleManagerFactory;
import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import net.netca.cloudkey.base.po.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.netcasvs.jni.KeyPairWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 证书业务服务类示例
 * 展示如何使用新的证书生命周期管理器接口
 */
@Slf4j
@Service
public class CertificateBusinessService {

    @Autowired
    private CertificateLifecycleManagerFactory certificateLifecycleManagerFactory;

    /**
     * 申请个人证书
     */
    public NetcaBpmsResponse applyPersonalCertificate(BusinessUser businessUser,
                                                     ConfigProject configProject,
                                                     ConfigLinkman configLinkman,
                                                     KeyPairWrapper signKeyPair,
                                                     String userPin,
                                                     String adminPin,
                                                     boolean sendSms) {
        try {
            // 获取证书生命周期管理器实例
            ICertificateLifecycleManager manager = certificateLifecycleManagerFactory.getCertificateLifecycleManager();

            // 生成个人证书申请请求
            RegisterRequest registerRequest = manager.generatePersonalCertificateRequest(
                businessUser, configProject, configLinkman);

            // 申请证书
            return manager.applyCertificate(registerRequest, null, businessUser, configProject,
                                          signKeyPair, userPin, adminPin, sendSms);

        } catch (Exception e) {
            log.error("申请个人证书失败", e);
            throw new RuntimeException("申请个人证书失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询证书申请状态
     */
    public NetcaBpmsResponse queryCertificateStatus(String systemId, String requestId) {
        try {
            ICertificateLifecycleManager manager = certificateLifecycleManagerFactory.getCertificateLifecycleManager();
            return manager.queryCertificateApplicationStatus(systemId, requestId);
        } catch (Exception e) {
            log.error("查询证书申请状态失败", e);
            throw new RuntimeException("查询证书申请状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载证书
     */
    public NetcaBpmsResponse downloadCertificate(String requestId, String systemId) {
        try {
            ICertificateLifecycleManager manager = certificateLifecycleManagerFactory.getCertificateLifecycleManager();
            return manager.downloadCertificate(requestId, systemId);
        } catch (Exception e) {
            log.error("下载证书失败", e);
            throw new RuntimeException("下载证书失败: " + e.getMessage(), e);
        }
    }

    /**
     * 注销证书
     */
    public Boolean revokeCertificate(BusinessCertAttribute businessCertAttribute,
                                    ConfigProject configProject,
                                    AuthorityOperator authorityOperator) {
        try {
            ICertificateLifecycleManager manager = certificateLifecycleManagerFactory.getCertificateLifecycleManager();
            return manager.revokeCertificate(businessCertAttribute, configProject, authorityOperator);
        } catch (Exception e) {
            log.error("注销证书失败", e);
            throw new RuntimeException("注销证书失败: " + e.getMessage(), e);
        }
    }
}
```

## 配置模型

### 数据库配置表

使用现有的配置表结构，通过 `ConfigKeyValueCacheUtil` 读取配置：

| 配置键                                            | 配置值  | 描述                       | 是否必需 |
| ------------------------------------------------- | ------- | -------------------------- | -------- |
| certificate.lifecycle.manager.implementation.type | default | 证书生命周期管理器实现类型 | 否       |

### 配置值说明

**实现类型 (certificate.lifecycle.manager.implementation.type)**

- `default` 或 `bpms`: 使用默认的 BPMS 业务平台实现（原 BpmsManager）
- 未配置或空值: 默认使用原有的 BpmsManager 实现

**默认行为**

- **无需任何配置即可工作**：如果数据库中没有相关配置，系统将自动使用原有的 BpmsManager 实现
- **向后兼容**：支持原有的 `bpms` 配置值
- **零配置启动**：系统启动时会自动使用默认实现，无需手动配置

### 配置示例（可选）

```sql
-- 可选：显式指定使用默认实现
INSERT INTO config_key_value (config_key, config_value, config_desc)
VALUES ('certificate.lifecycle.manager.implementation.type', 'default', '证书生命周期管理器实现类型');

-- 或者保持原有配置（向后兼容）
INSERT INTO config_key_value (config_key, config_value, config_desc)
VALUES ('certificate.lifecycle.manager.implementation.type', 'bpms', '证书生命周期管理器实现类型');
```

**注意**：以上配置完全是可选的。如果不添加任何配置，系统将默认使用原有的 BpmsManager 实现，确保完全的向后兼容性。

## 错误处理

### 异常类型

1. **配置异常**

   - 配置项不存在或无效
   - 实现类型不支持

2. **实例化异常**

   - Bean 不存在
   - 依赖注入失败

3. **运行时异常**
   - 方法调用失败
   - 业务逻辑异常

### 错误处理策略

```java
public class CaManagerException extends CloudKeyException {
    public CaManagerException(String message) {
        super(message);
    }

    public CaManagerException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

## 测试策略

### 单元测试

```java
package net.netca.cloudkey.base.config;

import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 证书生命周期管理器配置测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    "cloudkey.certificate.lifecycle.implementation-type=default"
})
class CertificateLifecycleAutoConfigurationTest {

    @MockBean
    private ICertificateLifecycleManager certificateLifecycleManager;

    @Test
    void shouldCreateDefaultManager() {
        assertThat(certificateLifecycleManager).isNotNull();
    }
}

/**
 * 配置属性测试
 */
@SpringBootTest
class CertificateLifecyclePropertiesTest {

    @Autowired
    private CertificateLifecycleProperties properties;

    @Test
    void shouldLoadDefaultProperties() {
        assertThat(properties.getImplementationType()).isEqualTo("default");
        assertThat(properties.isEnableHotReload()).isFalse();
    }
}
```

### 集成测试

```java
package net.netca.cloudkeyserver.manager;

import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 证书生命周期管理器集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
class CertificateLifecycleManagerIntegrationTest {

    @Autowired
    private ICertificateLifecycleManager certificateLifecycleManager;

    @Test
    void shouldInjectCorrectImplementation() {
        assertThat(certificateLifecycleManager)
            .isInstanceOf(DefaultCertificateLifecycleManager.class);
    }

    @Test
    void shouldHandleCertificateApplication() {
        // 测试证书申请功能
        // 使用Mock数据进行测试
    }
}
```

### 配置切换测试

```java
/**
 * 不同配置下的实现切换测试
 */
@TestMethodSource("configurationProvider")
@ParameterizedTest
void shouldSwitchImplementationBasedOnConfiguration(String implementationType,
                                                   Class<?> expectedClass) {
    // 动态设置配置
    System.setProperty("cloudkey.certificate.lifecycle.implementation-type",
                      implementationType);

    // 重新加载Spring上下文
    // 验证正确的实现被注入
}

static Stream<Arguments> configurationProvider() {
    return Stream.of(
        Arguments.of("default", DefaultCertificateLifecycleManager.class),
        Arguments.of("alternative", AlternativeCertificateLifecycleManager.class)
    );
}
```

## 部署和运维

### 迁移指南

**无需迁移**：现有系统可以直接使用新的接口架构，无需任何配置变更。

**可选配置**：如果需要显式指定实现类型，可以在数据库中添加配置：

```sql
-- 可选：显式指定使用默认实现（原 BpmsManager）
INSERT INTO config_key_value (config_key, config_value, config_desc)
VALUES ('certificate.lifecycle.manager.implementation.type', 'default', '证书生命周期管理器实现类型');
```

**向后兼容**：

- 原有的 BpmsManager 调用代码无需修改
- 原有的配置保持不变
- 系统行为完全一致

## 扩展性设计

### 新实现添加示例

```java
package com.example.custom;

import net.netca.cloudkeyserver.manager.ICertificateLifecycleManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 自定义证书生命周期管理器实现
 * 例如：对接其他CA平台或使用不同的业务流程
 */
@Service("customCertificateLifecycleManager")
@ConditionalOnProperty(
    prefix = "cloudkey.certificate.lifecycle",
    name = "implementation-type",
    havingValue = "custom"
)
public class CustomCertificateLifecycleManager implements ICertificateLifecycleManager {

    @Override
    public NetcaBpmsResponse applyCertificate(RegisterRequest registerRequest,
                                             String opSignature,
                                             BusinessUser businessUser,
                                             ConfigProject configProject,
                                             KeyPairWrapper signKeyPair,
                                             String userPin,
                                             String adminPin,
                                             boolean sendSms) {
        // 自定义实现逻辑
        // 例如：对接不同的CA平台API
        return customCertificateApplication(registerRequest);
    }

    // 实现其他接口方法...

    private NetcaBpmsResponse customCertificateApplication(RegisterRequest request) {
        // 自定义的证书申请逻辑
        return null;
    }
}
```

### 配置热更新支持

```java
package net.netca.cloudkey.base.config;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkeyserver.manager.CertificateLifecycleManagerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 证书生命周期管理器配置热更新支持
 * 基于 ConfigKeyValueCacheUtil 的配置变更检测
 */
@Slf4j
@Component
public class CertificateLifecycleConfigRefresher {

    @Autowired
    private CertificateLifecycleManagerFactory certificateLifecycleManagerFactory;

    /**
     * 刷新配置
     * 当检测到配置变更时调用此方法
     */
    public void refreshConfiguration() {
        try {
            log.info("开始刷新证书生命周期管理器配置");

            // 检查配置是否发生变化
            if (certificateLifecycleManagerFactory.isConfigurationChanged()) {
                // 重新初始化管理器
                certificateLifecycleManagerFactory.refreshManager();
                log.info("证书生命周期管理器配置刷新完成");
            } else {
                log.info("配置未发生变更，无需刷新");
            }

        } catch (Exception e) {
            log.error("刷新证书生命周期管理器配置失败", e);
            throw new RuntimeException("配置刷新失败", e);
        }
    }

    /**
     * 手动触发配置检查和刷新
     * 可以通过定时任务或管理接口调用
     */
    public void checkAndRefresh() {
        refreshConfiguration();
    }
}
```

### 未来扩展考虑

当前设计已经为未来的扩展提供了良好的基础：

1. **接口抽象**：通过 `ICertificateLifecycleManager` 接口，可以轻松添加新的实现
2. **工厂模式**：`CertificateLifecycleManagerFactory` 支持根据配置选择不同实现
3. **Spring 集成**：利用 Spring 的依赖注入和自动配置特性

**如果未来需要支持多种实现，可以考虑：**

- 添加新的实现类并注册为 Spring Bean
- 在 `CertificateLifecycleManagerFactory` 中添加对应的选择逻辑
- 在数据库配置表中添加新的配置值

**当前设计的优势：**

- 简单易懂，维护成本低
- 完全向后兼容
- 为未来扩展预留了架构空间
- 遵循 KISS（Keep It Simple, Stupid）原则
