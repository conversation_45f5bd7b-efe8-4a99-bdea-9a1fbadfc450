package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.pki.Certificate;

/**
 * @Description:
 * @Author: dyr
 * @CreateDate: 2020/6/22 10:21
 */
public enum CertTypeConstant implements IBaseEnum {

    SIGN_CERT(Certificate.PURPOSE_SIGN, "签名证书"),
    ENCRYPT_CERT(Certificate.PURPOSE_ENCRYPT, "加密证书");

    private int code;
    private String description;

    CertTypeConstant(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

}
