package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 证书有效期间隔单位
 */
public enum CertIntervalUnitEnum implements IBaseEnum {

    //WARN!!!!
    // 使用小写描述图片类型
    DAY(1, "天"),
    MONTH(2, "月"),
    ;

    private Integer code;
    private String description;

    private CertIntervalUnitEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
