package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 * @Description  数据库表信息常量
 * @Date 9:16 2020/5/21
 **/
public enum TableInfoEnum implements IBaseEnum {

    BUSINESS_USER(0,"business_user");

    private Integer code;
    private String description;

    TableInfoEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
