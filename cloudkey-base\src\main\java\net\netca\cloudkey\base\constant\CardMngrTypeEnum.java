package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.EnumUtil;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * 加密卡类型
 *
 * <AUTHOR>
 * @since 2018年10月17日12:51:46
 */
public enum CardMngrTypeEnum implements IBaseEnum {

    LOCAL_SOFT(0, "本地软设备"),
    SJK1550(1, "得安加密卡1550"),
    SANWEI(2, "三未信安"),
    SANWEI_62(3, "三未信安62卡"),
    NETCA_HSM(100, "远程服务的NETCA服务器密码机"),
    NETCA_SOFT(101, "远程服务的软设备。"),
    ;

    private Integer code;
    private String description;

    CardMngrTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public int getCodeIntValue() {
        return code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        String obj = null;
        ArrayList<LinkedHashMap<String, Object>> list = EnumUtil.getEnumKVMap(CardMngrTypeEnum.class);
        for (LinkedHashMap<String, Object> linkedHashMap : list) {
            if (linkedHashMap.get("key") != null && linkedHashMap.get("key").toString().equals(String.valueOf(code))) {
                obj = (String) linkedHashMap.get("value");
                break;
            }
        }
        return obj;
    }

}
