package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

@Getter
@AllArgsConstructor
public enum MobilePlatformTypeEnum implements IBaseEnum {

    //IOS平台
    IPHONE(0, "IOS"),
    //安卓平台
    ANDROID(1, "Android"),
    //微信小程序
    WECHAT_MINI_PROGRAM(2, "微信小程序"),
    //钉钉小程序
    DINGDING_MINI_PROGRAM(3, "钉钉小程序"),
    ;

    private final Integer code;
    private final String description;
}
