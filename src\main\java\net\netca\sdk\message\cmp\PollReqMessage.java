package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import org.bouncycastle.asn1.cmp.PKIHeader;

/**
 * 证书轮询
 * @author: zys
 * @date: 2020/3/2 17:34
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
public class PollReqMessage extends CmpMessage {

    public static PollReqMessage defaultMessage() {
        return PollReqMessage.builder()
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
