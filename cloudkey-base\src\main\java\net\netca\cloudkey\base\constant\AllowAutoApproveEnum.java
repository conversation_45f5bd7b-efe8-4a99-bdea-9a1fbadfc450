package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 允许自动审核常量类
 */
public enum AllowAutoApproveEnum implements IBaseEnum {

    //WARN!!!!
    // 使用小写描述图片类型
    NOT_ALLOW(0, "不允许"),
    ALLOW(1, "允许"),
    ;

    private Integer code;
    private String description;

    private AllowAutoApproveEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
