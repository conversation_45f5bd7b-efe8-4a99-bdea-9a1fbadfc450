package net.netca.test.cert;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-12-15 16:30
 */
public class TestX509Cert {
    @Test
    public void test() throws PkiException {
        String certStr = "-----BEGIN CERTIFICATE-----\n" +
                "MIIDYjCCAkygAwIBAgIUWKT4nMfXhX2MHM+zA6BtUFnvIrQwCwYJKoZIhvcNAQEL\n" +
                "MFIxCzAJBgNVBAYTAkNOMSQwIgYDVQQKDBtORVRDQSBDZXJ0aWZpY2F0ZSBBdXRo\n" +
                "b3JpdHkxHTAbBgNVBAMMFENDUyBORVRDQSBUMiBTdWIxIENBMB4XDTE5MDQxNzAz\n" +
                "MzMzMloXDTIyMDQxNzAzMzMzMlowJjELMAkGA1UEBhMCQ04xFzAVBgNVBAMMDlJB\n" +
                "6YCa6K6v6K+B5LmmMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs69l\n" +
                "Y3kPYBOOO67OWjU4aYz/YE90qBUcGlWMA36KNI32067NFojgB8RjLBCLcHfQXNTD\n" +
                "QE+YAGt1qyPIKBLLTWw8oMU4kZ5tNxfba/9RsbzaidhkC2MqZAFifsPsQPaYJfrQ\n" +
                "/Ct1Sbro9tBmtLHOzoQmXvCsExA0mcMetEWj6vkbfbkc7yh9X1eq05tsmxCuDuDT\n" +
                "tJPSrF5ACq/FjWZMoyvLR7fa919ajHw34ja2iHGabkBK1qbZBAglmAOtVga03PLc\n" +
                "sxdtJlRPCSIQ4qIf2TtnyZPEuVUftdQGkMKsO9VBwDtBqWbNgY8Sa0HGJ1RSwJZN\n" +
                "Pf66fAzct/TdpE3UmwIDAQABo2AwXjAdBgNVHQ4EFgQUTwHvBnUnygj3S0oMutyB\n" +
                "+92QbdswDgYDVR0PAQH/BAQDAgeAMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAU\n" +
                "Brkf1Z2yDPHEhHRCwjAl4gnaVtgwCwYJKoZIhvcNAQELA4IBAQDCkm66pbbMrBi7\n" +
                "n3TvjtAVgSxNCZGurVJjfOinyyYEOcga8QQLrO4YlhAHnupy1qj1zY5KBp23ZKem\n" +
                "nKyf8Ydt8gwX36EmUVFtaoDf0eGJcrJLig+qhwm5ixtfu/elSNJmuNSk9YCLUlza\n" +
                "E2WlD31Lq/KmR3nmn3HajfEQ/L0hUH/Yh+8JTR1FxIlddWK/v2sEi8XHKxuB4Lm3\n" +
                "46b7z30GOcepVsbDIhUdnCXlST/EWEKa0cqv7LgN59dgo11ZTVniSbud+yll/u9c\n" +
                "wJr2AP7d1TozNdpkNVihY2C4LOM17KLGJB1RAYuwFSzN3FN+e5P9V+cyDdqDcNfV\n" +
                "Aqz9BWLT\n" +
                "-----END CERTIFICATE-----";
        X509Certificate cert = new X509Certificate(certStr);
        cert.getIssuer().getASN1Object().encode();
        cert.getSerialNumber();
        cert.getSubject().getASN1Object().encode();
    }
}
