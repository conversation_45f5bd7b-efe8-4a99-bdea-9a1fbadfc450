package net.netca.cloudkey.base.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * created by zaj on 2019/1/16
 */
@Data
@ApiModel(description = "服务响应结果DTO")
public class ResponseResult {

    @ApiModelProperty(value = "服务响应结果码，0：成功，其他：失败")
    private Integer status;
    @ApiModelProperty(value = "服务响应结果描述")
    private String msg;

}
