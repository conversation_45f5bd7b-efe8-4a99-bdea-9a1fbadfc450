package net.netca.cloudkey.base.dto.config.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 流程配置业务操作DTO
 * @Author: dyr
 * @CreateDate: 2020/8/11 17:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConfigProcessInsertReqDTO implements ISignature {

    /**
     * 流程类型
     */
    @NotNull(message = "流程类型不能为空")
    private Integer processType;

    /**
     * 操作顺序
     */
    @NotNull(message = "操作顺序不能为空")
    @Size(min = 1, message = "操作顺序不能为空")
    private String[] operationSequence;

    /**
     * 流程对应平台
     */
    @NotNull(message = "流程对应平台不能为空")
    @Size(min = 1, message = "流程对应平台不能为空")
    private Integer[] processPlatforms;

    public String appendOperationSequence() {
        return StringUtils.join(this.operationSequence, ",");
    }

    private transient OperatorSignature operatorSignature;
}
