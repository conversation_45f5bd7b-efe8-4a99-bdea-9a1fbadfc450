package net.netca.sdk.message.cmp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.Extension;
import net.netca.sdk.message.Validity;
import org.bouncycastle.asn1.cmp.PKIHeader;

import java.util.List;

/**
 * 证书注册 - P10形式
 *
 * @author: zys
 * @date: 2020/3/2 17:11
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Getter
public class P10CertReqMessage extends CmpMessage {


    /**
     * P10 证书请求 - base64格式
     */
    private String p10Base64;

    /**
     * 扩展
     */
    private List<Extension> requestExtensions;

    /**
     * 证书有效期
     */
    private Validity validity;

    /**
     * 自定义的扩展信息
     */
    private CustomFreeText customFreeText;


    public static P10CertReqMessage defaultMessage(){
        return P10CertReqMessage.builder()
                .businessTypeEnum(BusinessTypeEnum.P10_REGISTER)
                .pvno(PKIHeader.CMP_2000)
                .isSetSenderNonce(true)
                .isSetTransactionID(true)
                .unknownProtectionAlg(false)
                .isPublishLdap(true)
                .build();
    }
}
