package net.netca.cloudkey.base.dto.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.annotation.Secret;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 17:54 2020/12/7
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Secret
public class ApproveProgressVO {

    //当前状态
    private String status;

    private List<ApproveProgressInfoVO> info;

}
