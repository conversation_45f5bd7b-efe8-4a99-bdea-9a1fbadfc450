package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;


/**
 * created by zaj on 2019/6/1
 */
public enum InvokeInterfaceTypeEnum implements IBaseEnum {

    //
    DEFAULT_INTERFACE_TYPE(-1, "默认值", ""),

    /**
     * 1-1000作为服务端被别人调用的接口
     * 1-500是标准接口，501-1000是定制接口
     */
    ORG_REGISTER(1, "机构注册接口", "/system/interface/org/register"),
    USER_REGISTER(2, "用户注册接口", "/system/interface/user/register"),
    USER_UNLOCK(3, "用户解锁接口", "/system/interface/user/unlock/2CKV6"),
    UPLOAD_SEALPIC(4, "上传签章接口", "/system/interface/seal/certcontent/uploadsealpic/2CKV7"),
    CERT_RENEWAL(5, "证书续期接口", "/system/interface/user/certcontent/renewal/2CKV8"),
    CERT_REVOKE(6, "证书注销接口", "/system/interface/user/certcontent/revoke/2CKV11"),
    CERT_FREEZE(7, "证书冻结接口", "/system/interface/user/certcontent/status/freeze/2CKV15"),
    CERT_UNFREEZE(8, "证书解冻接口", "/system/interface/user/certcontent/status/unfreeze/2CKV15"),



    /**
     * 事件证书接口
     */
    EVENTCERT_APPLY(9, "事件证书签发接口", "/system/interface/eventCert/apply/2CKV23"),
    EVENTCERT_APPLY_SIGN(10, "事件证书签发并签名接口", "/system/interface/eventCert/applyAndSign/2CKV23"),
    EVENTCERT_SIGN(11, "事件证书签名接口", "/system/interface/eventCert/sign/2CKV23"),

    USER_UPDATE(12, "用户信息修改接口", "/system/interface/user/update/2CKV32"),

    CERT_RESERVE_CANCEL(13, "证书预约取消", "/system/interface/user/certcontent/reserve/cancel/2CKV34"),
    CERT_LIST(14, "证书列表查询", "/system/interface/cert/list/2CKV34"),
    THIRD_SYSTEM_LOG(15, "第三方系统日志查询", "/system/interface/thirdSystemLog/page"),
//    SEAL_PDF(50, "PDF同步签章", "/seal/pdfseal"),
//    SEAL_PDF_WITH_OTHER_IMG(51, "PDF签章（使用参数图片签章）", "/seal/with/sealpic/1CKV13"),
//    SEAL_PDF_VERIFY(52, "PDF验证", "/seal/verify/1CKV13"),

    /**
     * 501-1000是定制接口
     */
    DG_APPLY_EVENTCERT(501, "数广粤信签事件证书签发接口", "/dg/szca/applyEventCert/2CKV8"),
    GD_APPLY_EVENTCERT_SIGN(502, "数广粤信签事件证书签发并签名接口", "/dg/szca/applyEventCertSign/2CKV8"),
    DG_SIGNDATA(503, "数广粤信签事件证书签名接口", "/dg/szca/signData/2CKV8"),
    DG_VERIFY_DATA(504, "数广粤信签事件证书验签接口", "/dg/szca/verifyData/2CKV8"),
    DG_REVOKE_CERT(505, "数广粤信签事件证书注销接口", "/dg/szca/revokeCert/2CKV8"),


    /**
     * 1001-2000作为客户端调用别人的接口
     */
    BUSINESS_NOTIFY(1001, "业务通知接口", "/system/interface/business/notify"),

    // 协同签名相关的接口 暂定5000 - 5999 范围内

    COOP_SIGN_TEXT_TASK_CREATE_EDIT(5001, "创建/编辑文本协同签署任务接口", "/system/interface/coord-sign/textTask/submit"),
    COOP_SIGN_TASK_REVOKE(5002, "取消协同签署任务接口", "/system/interface/coord-sign/task/cancel"),
    COOP_SIGN_TASK_GET(5003, "查询签署任务接口", "/system/interface/coord-sign/task/query"),

    COOP_SIGN_TASK_UNSIGNED_GET(5005, "签署者待签任务列表接口", "/system/interface/coord-sign/task/unsigned/query"),


    COOP_SIGN_FILE_TASK_CREATE_EDIT(5006, "创建/编辑文件协同签署任务接口", "/system/interface/coord-sign/fileTask/submit"),
    COOP_SIGN_TEXT_TASK_CREATE_EDIT_WITH_TPL(5007, "创建/编辑文本协同签署任务接口（模板方式）","/system/interface/coord-sign/template/textTask/submit"),
    COOP_SIGN_FILE_TASK_CREATE_EDIT_WITH_TPL(5008, "创建/编辑文件协同签署任务接口（模板方式）","/system/interface/coord-sign/template/fileTask/submit"),
    COOP_SIGN_TASK_COMMIT(5009, "发起协同签署任务接口","/system/interface/coord-sign/task/commit"),
    COOP_SIGN_TASK_TEMPLATE_CREATE_EDIT(5010, "创建/编辑协同签署模板接口","/system/interface/coord-sign/template/create"),
    COOP_SIGN_TASK_TEMPLATE_DETAIL(5011, "查看协同签署模板详情接口","/system/interface/coord-sign/template/query"),
    COOP_SIGN_TASK_TEMPLATE_DIAGRAM(5012, "获取查看流程图令牌接口","/system/interface/coord-sign/diagram"),
    ;




    InvokeInterfaceTypeEnum(int code, String description, String uri) {
        this.code = code;
        this.description = description;
        this.uri = uri;
    }

    private Integer code;
    private String description;
    private String uri;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public String getUri() {
        return uri;
    }

    public static InvokeInterfaceTypeEnum getTypeByUri(String uri) {
        InvokeInterfaceTypeEnum result = null;
        for (InvokeInterfaceTypeEnum typeEnum : InvokeInterfaceTypeEnum.values()) {
            if (uri.contains(typeEnum.getUri())) {
                result = typeEnum;
            }
        }
        return result == null ? BUSINESS_NOTIFY : result;
    }

}
