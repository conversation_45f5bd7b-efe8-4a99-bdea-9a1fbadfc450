package net.netca.cloudkey.base.dto.cert;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * created by zaj on 2019/1/17
 */


@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
public class SearchCertListDTO {

    /**
     * 用户工号
     */
    private String jobNumber;

    /**
     * 用户UID
     */
    private String uid;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 证书序列号
     */
    private String certSN;

    /**
     * 证书主题O项
     */
    private String O;

    /**
     * 证书主题OU项
     */
    private String OU;

    /**
     * 证书主题CN项
     */
    private String CN;

    /**
     * 项目UID
     */
    private String projectUid;



}
