package net.netca.cloudkey.base.dto.cert;

import java.util.concurrent.TimeUnit;

/**
 * 用于第三方系统接口的防重放 需要接口的第一个参数实现这个接口
 * 另外参考这个注解和相关切面
 * {@link net.netca.cloudkeyserver.annotation.CheckNonceTimestamp}
 */
public interface NonceTimestampPreventReplay {

    int maxNonceSize = 32;

    // 默认随机数有效时间(5分钟) 在服务启动时，如果 CHECK_REPLAY_NONCE_VALID_TIME 配置的配置值为-1时使用
    long DEFAULT_NONCE_VALID_SECONDS = TimeUnit.MINUTES.toSeconds(5);

    String getNonce();

    Long getTimestamp();

    String getAuthId();

}
