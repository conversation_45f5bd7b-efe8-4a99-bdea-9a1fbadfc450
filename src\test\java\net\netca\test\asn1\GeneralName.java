package net.netca.test.asn1;


import lombok.Builder;
import net.netca.sdk.message.SubjectInfo;

@Builder
public class GeneralName {
	private Integer type;
	/***
	 * 当type为otherName的时候，value无值，otherName有值
	 * 
	 */
	private String value;
	private OtherName otherName;
	private SubjectInfo subject;
	
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public OtherName getOtherName() {
		return otherName;
	}
	public void setOtherName(OtherName otherName) {
		this.otherName = otherName;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	@Override
	public String toString() {
		return "GeneralName [type=" + type + ", value=" + value + ", otherName=" + otherName + "]";
	}
	public SubjectInfo getSubject() {
		return subject;
	}
	public void setSubject(SubjectInfo subject) {
		this.subject = subject;
	}
}
