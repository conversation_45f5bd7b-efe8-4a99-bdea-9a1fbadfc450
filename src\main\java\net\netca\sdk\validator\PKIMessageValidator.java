package net.netca.sdk.validator;

import lombok.extern.slf4j.Slf4j;
import net.netca.pki.encoding.asn1.pki.PublicKey;
import net.netca.pki.encoding.asn1.pki.SoftwareVerifier;
import net.netca.sdk.execption.CmpValidatingException;
import net.netca.sdk.util.CryptoUtils;
import net.netca.sdk.util.Preconditions;
import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1OutputStream;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.cmp.PKIHeader;
import org.bouncycastle.asn1.cmp.PKIMessage;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: zys
 * @date: 2020/3/2 19:38
 */
@Slf4j
public class PKIMessageValidator {

    public static void checkRespHeaderForNested(PKIMessage respMessage, PKIHeader reqPKIHeader, PKIHeader reqInlinePKIHeader, PublicKey publicKey) throws Exception {
        PKIHeader respMessageHeader = respMessage.getHeader();
        commonCheck(reqPKIHeader, respMessageHeader);
        //如果请求存在transactionId，则返回必须包含，且必须和请求一致；如果不存在，则必须包含即可
        if (reqInlinePKIHeader.getTransactionID() != null) {
            byte[] transactionId1 = reqInlinePKIHeader.getTransactionID().getOctets();
            byte[] transactionId2 = respMessageHeader.getTransactionID().getOctets();
            if (!Arrays.equals(transactionId1, transactionId2)) {
                throw new CmpValidatingException("Req's transactionId and Resp's transactionId do not match");
            }
        } else if (reqPKIHeader.getTransactionID() != null) {
            byte[] transactionId1 = reqPKIHeader.getTransactionID().getOctets();
            byte[] transactionId2 = respMessageHeader.getTransactionID().getOctets();
            if (!Arrays.equals(transactionId1, transactionId2)) {
                throw new CmpValidatingException("Req's transactionId and Resp's transactionId do not match");
            }
        }
        //protection验证
        checkProtection(respMessage, publicKey);
    }

    /**
     * 验证响应的 {@link PKIMessage} 的 {@link PKIHeader} 有效性
     *
     * @param respMessage {@link PKIMessage}
     * @param reqPKIHeader {@link PKIHeader}
     * @throws Exception e
     */
    public static void checkRespHeader(PKIMessage respMessage, PKIHeader reqPKIHeader, PublicKey publicKey) throws Exception {

        PKIHeader respMessageHeader = respMessage.getHeader();

        commonCheck(reqPKIHeader, respMessageHeader);

        //如果请求存在transactionId，则返回必须包含，且必须和请求一致；如果不存在，则必须包含即可
        if (reqPKIHeader.getTransactionID() != null) {
            byte[] transactionId1 = reqPKIHeader.getTransactionID().getOctets();
            byte[] transactionId2 = respMessageHeader.getTransactionID().getOctets();
            if (!Arrays.equals(transactionId1, transactionId2)) {
                throw new CmpValidatingException("Req's transactionId and Resp's transactionId do not match");
            }
        } else {
            Objects.requireNonNull(respMessageHeader.getTransactionID(), "Resp's transactionId isn't allow to be null");
        }

        //protection验证
        checkProtection(respMessage, publicKey);
    }

    private static void commonCheck(PKIHeader reqPKIHeader, PKIHeader respMessageHeader) throws IOException {
        byte[] sender = respMessageHeader.getSender().getEncoded();
        Objects.requireNonNull(sender, "sender isn't allow to be null");

        byte[] recipient = reqPKIHeader.getRecipient().getEncoded();
        Objects.requireNonNull(recipient, "recipient isn't allow to be null");


        //校验req的recipenter和resp的sender，确保一致
        if (!Arrays.equals(recipient, sender)) {
            throw new CmpValidatingException("Req's recipenter and Resp's sender do not match");
        }
        //如果请求存在senderNonce，响应的recipientNonce必须存在，且等于
        if (reqPKIHeader.getSenderNonce() != null) {
            Objects.requireNonNull(respMessageHeader.getRecipNonce());
            byte[] senderNonce = reqPKIHeader.getSenderNonce().getEncoded();
            byte[] recipientNonce = respMessageHeader.getRecipNonce().getEncoded();
            if (!Arrays.equals(senderNonce, recipientNonce)) {
                throw new CmpValidatingException("Req's senderNonce and Resp's recipientNonce do not match");
            }
        }
    }

    /**
     * 验证签名
     *
     * @param respMessage {@link PKIMessage}
     * @throws Exception e
     */
    private static void checkProtection(PKIMessage respMessage, PublicKey publicKey) throws Exception {

        //获取protectionAlg
        Objects.requireNonNull(respMessage.getHeader().getProtectionAlg(), "Resp's protectionAlg isn't allow to be null");
        net.netca.pki.encoding.asn1.pki.AlgorithmIdentifier algorithmIdentifier = net.netca.pki.encoding.asn1.pki.AlgorithmIdentifier
                .decode(respMessage.getHeader().getProtectionAlg().getEncoded());

        //获取protection
        byte[] protection = Optional.of(respMessage.getProtection().getBytes()).get();

        //构建签名原文
        byte[] protectionOrg = getProtection(respMessage);

        log.debug("verify PublicKey:" + CryptoUtils.hexEncode(publicKey.toSubjectPublicKeyInfo().getASN1Object().encode()));
        log.debug("algorithmIdentifier:" + algorithmIdentifier.getOid());
        log.debug("protectionOrg:" + CryptoUtils.hexEncode(protectionOrg));
        log.debug("protection:" + CryptoUtils.hexEncode(protection));

        SoftwareVerifier softwareVerifier = new SoftwareVerifier();
        boolean result = softwareVerifier.verify(publicKey,
                algorithmIdentifier, protectionOrg, 0, protectionOrg.length, protection);
        log.debug("result:" + result);
        Preconditions.checkArgument(result, "Signature verification failed");
    }


    /**
     * 获取签名值
     *
     * @param pkiMessage {@link PKIMessage}
     * @return byte[]
     * @throws Exception e
     */
    private static byte[] getProtection(PKIMessage pkiMessage) throws Exception {
        byte[] res;
        ASN1EncodableVector v = new ASN1EncodableVector();
        v.add(pkiMessage.getHeader());
        v.add(pkiMessage.getBody());
        ASN1Encodable protectedPart = new DERSequence(v);
        try (ByteArrayOutputStream bao = new ByteArrayOutputStream()) {
            ASN1OutputStream out =  ASN1OutputStream.create(bao);
            out.writeObject(protectedPart);
            res = bao.toByteArray();
        }
        return res;
    }
}
