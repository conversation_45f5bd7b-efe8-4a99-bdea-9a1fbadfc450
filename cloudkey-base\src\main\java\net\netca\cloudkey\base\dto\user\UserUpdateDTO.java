package net.netca.cloudkey.base.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.constraints.annotation.Area;
import net.netca.cloudkey.base.constraints.annotation.Email;
import net.netca.cloudkey.base.constraints.annotation.Gender;
import net.netca.cloudkey.base.constraints.annotation.MobilePhone;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Area
public class UserUpdateDTO {

    @NotBlank(message = "UID不能为空")
    @Size(min = 1, max = 128, message = "用户唯一标识的长度需要在1 - 128个字之间 ")
    private String uid;

    /**
     * 可选地允许修改新唯一标识
     */
    private String uidAlias;


    private String name;


    @Gender
    private Integer gender;

    @MobilePhone
    private String phone;

    @Email
    private String email;

    private String occupation;


    @Size(max = 32, message = "国家代码长度不超过32个字符")
    private String countryName;


    @Size(max = 64, message = "省份长度不超过64个字符")
    private String province;

    @Size(max = 64, message = "城市长度不超过64个字符")
    private String city;

    private String officialResidence;

    private String department;


    private Integer organizationId;


    private Integer identityType;


    private String identity;

}
