package net.netca.test.cert;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.Validity;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.KeyUpdateReqMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.RenewReqMessage;
import net.netca.sdk.util.StringUtils;
import net.netca.test.util.DateUtil;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 证书续期
 * @author: zys
 * @date: 2020/3/5 19:01
 */
@CommonsLog
public class TestRenewCert extends TestCmpProtocol {

    @Test
    public void testNormalRenewCertByKeyUpdateReqMessageCodec() throws Exception {
        RenewReqMessage renewMessage = getRenewReqMessage();
//        String certSn = "08ffeed7117350c124308793f7a7ac27277b1c17";
//        String issue = "CN=TEST2019 NETCA RSA CA1,O=NETCA,C=CN";
//        RenewReqMessage renewMessage = getRenewReqMessage(certSn, issue);
        CmpRespResult cmpRespResult = getRenewCertCmpRespResult(renewMessage);
        Assert.assertEquals(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
        if (cmpRespResult.getFailureReason() != null) {
            log.error(cmpRespResult.getFailureReason());
            log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
        }
        Assert.assertNotNull(cmpRespResult.getSignCertPem());
        Assert.assertNotNull(cmpRespResult.getEncCertPem());
        Assert.assertNotNull(cmpRespResult.getSignCertDer());
        Assert.assertNotNull(cmpRespResult.getEncCertDer());
        X509Certificate signCert = new X509Certificate(cmpRespResult.getSignCertPem());
        Assert.assertTrue(compare(renewMessage.getValidity().getEndTime(), signCert.getNotAfter()));
    }


    /**
     * 证书续期 - 人工审核
     *
     * @throws Exception
     */
    @Test
    public void renewCertByHumanApprove() throws Exception {
        String oldIssuer = "CN=TEST2019 NETCA RSA CA1,O=NETCA,C=CN";
        String oldCertSn = "2cba8bd16cd4882617a52553c2c0b17be9e57582";
        RenewReqMessage renewMessage = getRenewReqMessage(oldCertSn, oldIssuer);
        CmpRespResult cmpRespResult = getRenewCertCmpRespResult(renewMessage);
        Assert.assertEquals(PKIStatusEnum.WAITING, cmpRespResult.getPkiStatusEnum());
        CmpRespResult pollCmpRespResult;
        do {
            log.info("start poll....");
            long respCertRequestId = cmpRespResult.getRespCertRequestId();
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.RENEWAL;
            pollCmpRespResult = getPollReqCmpRespResult(respCertRequestId, businessTypeEnum);
            String failureReason = pollCmpRespResult.getFailureReason();
            if (StringUtils.hasText(failureReason)){
                log.info("error code: " + pollCmpRespResult.getFailureCode());
                log.info("error reason: " + failureReason);
            }
            log.info("poll end....");
            Thread.sleep(1000);
        } while (PKIStatusEnum.WAITING.equals(pollCmpRespResult.getPkiStatusEnum()));
        Assert.assertNotNull(pollCmpRespResult.getSignCertPem());
        Assert.assertNotNull(pollCmpRespResult.getEncCertPem());
        Assert.assertNotNull(pollCmpRespResult.getSignCertDer());
        Assert.assertNotNull(pollCmpRespResult.getEncCertDer());
        log.info(pollCmpRespResult.getSignCertPem());
        X509Certificate x509Certificate = new X509Certificate(pollCmpRespResult.getSignCertDer());
        String serialNumber = Hex.encode(false, x509Certificate.getSerialNumber());
        // 被续期证书的证书颁发者
        String issuer = x509Certificate.getIssuer().getLdapName();
        log.info(pollCmpRespResult.getSignCertPem());
        log.info("certSn: " + serialNumber);
        log.info("issuer: " + issuer);
    }

    /**
     * 证书续期轮询
     */
    @Test
    public void testRenewPoll() throws Exception {
        RenewReqMessage renewMessage = getRenewReqMessage();
        CmpRespResult renewCertCmpRespResult = getRenewCertCmpRespResult(renewMessage);
        Assert.assertEquals(PKIStatusEnum.ACCEPTED, renewCertCmpRespResult.getPkiStatusEnum());
        if (renewCertCmpRespResult.getFailureReason() != null) {
            log.error(renewCertCmpRespResult.getFailureReason());
            log.error("PKIFailureInfoEnum's code is : " + renewCertCmpRespResult.getFailureCode());
        }
        Assert.assertNotNull(renewCertCmpRespResult.getSignCertPem());
        Assert.assertNotNull(renewCertCmpRespResult.getEncCertPem());
        X509Certificate signCert = new X509Certificate(renewCertCmpRespResult.getSignCertPem());
        Assert.assertTrue(compare(renewMessage.getValidity().getEndTime(), signCert.getNotAfter()));
        long respCertRequestId = renewCertCmpRespResult.getRespCertRequestId();
        CmpRespResult pollCmpRespResult = getPollReqCmpRespResult(respCertRequestId, BusinessTypeEnum.RENEWAL);
        Assert.assertEquals(renewCertCmpRespResult.getSignCertPem(), pollCmpRespResult.getSignCertPem());
        Assert.assertEquals(renewCertCmpRespResult.getEncCertPem(), pollCmpRespResult.getEncCertPem());
        Assert.assertArrayEquals(renewCertCmpRespResult.getSignCertDer(), pollCmpRespResult.getSignCertDer());
        Assert.assertArrayEquals(renewCertCmpRespResult.getEncCertDer(), pollCmpRespResult.getEncCertDer());
    }



    private CmpRespResult getRenewCertCmpRespResult(RenewReqMessage renewMessage) throws Exception {
        SingleCertReqContext context = new SingleCertReqContext(renewMessage);
        keyUpdateReqMessageEncoder.encode(context, renewMessage);
        PKIMessages pkiMessages = context.getPKIMessages();
        Assert.assertNotNull(pkiMessages);
        byte[] reqData = pkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] repsData = send(reqData);
        Assert.assertNotNull(repsData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(repsData));
        PKIMessage[] pkiMessageArray = respPkiMessages.toPKIMessageArray();
        keyUpdateReqMessageDecoder.decode(context, pkiMessageArray[0]);
        return context.getCmpRespResult();
    }


    public RenewReqMessage getRenewReqMessage() throws Exception {
        // 获取需要续期的证书
        X509Certificate signCert = getNewSignCert();
        Assert.assertNotNull(signCert);
        // 被续期证书的证书序列号
        X509Certificate x509Certificate = new X509Certificate(signCert.derEncode());
        String serialNumber = Hex.encode(false, x509Certificate.getSerialNumber());
        // 被续期证书的证书颁发者
        String issuer = x509Certificate.getIssuer().getLdapName();
        return getRenewReqMessage(serialNumber, issuer);
    }

    public RenewReqMessage getRenewReqMessage(String certSn, String issuer) {
        Date now = new Date();
        Validity validity = Validity.builder()
                .startTime(now)
                .endTime(DateUtil.dateAddYear(now, 2))
                .build();
        final long certRequestId =  new Random().nextInt(1000);
        // 模拟参数，具体值自行决定
        String senderStr = "OU=test";
        String recipientStr = "CN=netca";

        return RenewReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .serialNumber(certSn)
                .issuer(issuer)
                .certRequestId(certRequestId)
                .validity(validity)
                .build();
    }


    @Test
    public void testNormalRenewCertByMultipleMessageCodec() throws Exception {
        KeyUpdateReqMessage keyUpdateReqMessage = getRenewReqMessage();
        normalRenewCert(keyUpdateReqMessage);
    }

    private void normalRenewCert(CmpMessage cmpMessage) throws Exception {
        List<CmpMessage> cmpMessageList = new ArrayList<>();
        cmpMessageList.add(cmpMessage);
        MultipleCmpReqContext context = new MultipleCmpReqContext();
        multipleMessageEncoder.encode(context, cmpMessageList);
        PKIMessages pkiMessages = context.getPKIMessages();
        Assert.assertNotNull(pkiMessages);
        byte[] reqData = pkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] repsData = send(reqData);
        Assert.assertNotNull(repsData);
        multipleMessageDecoder.decode(context, PKIMessages.getInstance(ASN1Sequence.getInstance(repsData)));
        context.getCmpRespResults().forEach(
                cmpRespResult -> {
                    Assert.assertSame(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
                    if (cmpRespResult.getFailureReason() != null) {
                        log.error(cmpRespResult.getFailureReason());
                        log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
                    }
                    Assert.assertNotNull(cmpRespResult.getSignCertPem());
                    Assert.assertNotNull(cmpRespResult.getEncCertPem());
                }
        );
    }



    @Test
    public void testRenewCertWithNestedMessage() throws Exception {
        NestedMessage nestedMessage = getNestedMessage(getRenewReqMessage(), BusinessTypeEnum.RENEWAL);
        normalRenewCert(nestedMessage);
    }



}
