package net.netca.cloudkey.base.dto.config.linkman;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.abstractclass.ISignature;
import net.netca.cloudkey.base.dto.operator.OperatorSignature;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigLinkmanReqDTO implements ISignature {

    @NotNull(message = "证件类型不能为空")
    private Integer identityType;

    @NotBlank(message = "证件号码不能为空")
    @Size(max = 128, message = "证件号码长度不能超过128个字符")
    private String identity;

    @NotBlank(message = "姓名不能为空")
    @Size(max = 32, message = "姓名长度不能超过32个字符")
    private String name;

    @Size(max = 55, message = "电子邮箱地址长度不能超过55个字符")
    private String email;

    @Size(max = 32, message = "电话号码长度不能超过32个字符")
    private String phone;

    @Size(max = 128, message = "地址长度不能超过128个字符")
    private String address;

    @NotNull(message = "院区ID不能为空")
    private Integer districtId;

    private transient OperatorSignature operatorSignature;

}
