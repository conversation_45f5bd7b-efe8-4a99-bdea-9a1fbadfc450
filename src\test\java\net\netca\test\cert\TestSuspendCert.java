package net.netca.test.cert;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import org.junit.Test;

import java.util.Objects;

/**
 * 证书挂失
 * @author: zys
 * @date: 2020/3/5 19:02
 */
@CommonsLog
public class TestSuspendCert extends TestCmpProtocol {


    @Test
    public void testSuspendCert() throws Exception {
        X509Certificate signCert = getNewSignCert();
        // 挂失
        Objects.requireNonNull(signCert);
        suspendOrUnSuspendCert(signCert, BusinessTypeEnum.SUSPEND);
    }

}
