package net.netca.cloudkey.base.constant;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Set;

/**
 * 签署状态：0-未签名，1-签名中（未满足权限签名，已经有人对这个环节进行签名或者其他操作则为该状态），2-已拒绝（多人拒绝），5-已完成
 */
@Getter
@AllArgsConstructor
public enum CoordSignSegementStatusEnum implements IBaseEnum {

    /**
     * 未签名
     */
    NOT_SIGNED(0, "未签名"),

    /**
     * 签名中
     */
    SIGNING(1, "签名中"),

    /**
     * 已拒绝
     */
    REJECTED(2, "已拒绝"),

    /**
     * 已完成
     */
    COMPLETED(5, "已完成");

    private final Integer code;
    private final String description;


    /**
     * 终结状态的任务状态
     */
    public static final Set<Integer> FINAL_SEGMENT_STATUS = Sets.newHashSet(REJECTED.code,COMPLETED.code);

    /**
     * 非终结的任务状态
     */
    public static final Set<Integer> NON_FINAL_SEGMENT_STATUS = Sets.newHashSet(NOT_SIGNED.code, SIGNING.code);


}
