package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 说明：p7 签名值来源
 *
 * <AUTHOR>
 * @date 2023-07-06 08:58:51
 */
@Getter
@AllArgsConstructor
public enum CaTypeEnum implements IBaseEnum{

    //
    USB_KEY("usbKey","key的签名值"),
    CLOUD_KEY("cloudKey","云密钥的签名值"),
    OTHERS("others","其他来源的签名值")
    ;

    private final String code;
    private final String description;


    public static CaTypeEnum getCaTypeNullAble(String code) {
        for (CaTypeEnum value : CaTypeEnum.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
