package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * 签章图片审核状态
 */
public enum ReviewStatusEnum implements IBaseEnum {
    UNREVIEWED(0, "未审核"),
    PASSED(1, "审核通过"),
    NOT_PASSED(-1, "审核不通过"),
    ;

    private Integer code;
    private String description;

    ReviewStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }
}
