package net.netca.test.asn1.pojo;


import net.netca.test.exception.ExtensionFormatException;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-03-23 11:51
 */
public class TelephoneNumberRange {

    public static final String NAME_START = "start";
    public static final String NAME_COUNT = "count";
    private String start;
    private Integer count;

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public void validate() throws ExtensionFormatException {
        if (start == null) {
            throw new ExtensionFormatException("TelephoneNumberRange's start should not be empty.");
        }
        TelephoneNumber.validate(start);

        if (start.indexOf('#') < 0 && start.indexOf('*') < 0 && count != null) {
            try {
                BigDecimal startValue = new BigDecimal(start);
                BigDecimal countValue = new BigDecimal(count);

                int min = 2;
                if (count < min) {
                    throw new ExtensionFormatException(String.format("TelephoneNumberRange's count should be at least %s.", min));
                }

                int length = startValue.toString().length();
                BigDecimal max = BigDecimal.TEN.pow(length).subtract(startValue);
                if (countValue.compareTo(max) >= 0) {
                    throw new ExtensionFormatException("invalid TelephoneNumberRange's count.");
                }
            } catch (NumberFormatException e) {
                throw new ExtensionFormatException(String.format("fail to parse %s to number.", start), e);
            }
        }
    }

    @Override
    public String toString() {
        return "TelephoneNumberRange{" +
                "start='" + start + '\'' +
                ", count=" + count +
                '}';
    }
}
