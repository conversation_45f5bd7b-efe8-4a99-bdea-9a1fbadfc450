package net.netca.cloudkey.base.dto.approve;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 17:54 2020/12/7
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserApproveVO {
    private String uid;
    private String name;
    private String phone;
    private String province;
    private String city;
    private String officialResidence;
    private String countryName;
    //private Integer gender;
    //private Integer identityType;
    private String identity;
    private String email;
    //private Integer organizationId;
    private String department;
    private String occupation;
    //private String cloudkeyProjectId;

    @TableField("gmt_create")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @TableField("gmt_modified")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private Integer certUserType;

    private String orgName;
    private String projectName;
    private String genderValue;
    private String identityTypeName;
}

