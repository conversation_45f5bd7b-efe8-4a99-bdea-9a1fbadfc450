package net.netca.cloudkey.base.dto.coord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(description = "签名记录详情")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoordSignTaskSegmentRecordDTO {

    @ApiModelProperty(value = "记录id")
    private Long recordId;
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    @ApiModelProperty(value = "第三方流水号")
    private String thirdOrderId;

    @ApiModelProperty(value = "签名类型：0-P7签名，1-电子签章")
    private Integer signType;

    @ApiModelProperty(value = "签名状态 0-未签名，2-已拒绝，4-已撤回，5-已完成")
    private Integer signStatus;
    @ApiModelProperty(value = "签名意见")
    private String signOpinion;
    @ApiModelProperty(value = "签名人名称")
    private String signerName;
    @ApiModelProperty(value = "签名人uid")
    private String signerUid;

    @ApiModelProperty(value = "签署时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "签名参数")
    private String textContent;


    @ApiModelProperty(value = "原文件名称")
    private String originalFileName;

    @ApiModelProperty(value = "预览文件名称")
    private String previewFileName;

    @ApiModelProperty(value = "签名选项")
    private String signOptionJson;

    @ApiModelProperty(value = "签章选项")
    private String sealOptionJson;
}
