package net.netca.cloudkey.base.dto.authuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description 我的申请授权信息
 * @Date Created in 10:14 2024/10/11
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyApplyAuthorizeInfoVO {
    /**
     * 申请记录ID
     */
    private Integer id;

    private String name;

    /**
     * 授权状态
     */
    private Integer authStatus;

    /**
     * 授权的证书ID
     */
    private Integer certId;


    /**
     * 有效期开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "YYYY-MM-dd HH:mm:ss")
    private Date validStart;
    /**
     * 有效期结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "YYYY-MM-dd HH:mm:ss")
    private Date validEnd;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;



    /**
     * 允许调用次数
     */
    private Integer allowCallCount;
}
