package net.netca.cloudkey.base.dto.config.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuartzConfigBo {

    private String cron;

    @NotNull(message = "immediately字段不能为空")
    private Integer immediately;

    @NotNull(message = "enable字段不能为空")
    private Integer enable;

}
