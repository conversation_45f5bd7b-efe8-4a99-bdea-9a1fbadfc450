package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;


@Getter
@AllArgsConstructor
public enum BatchTaskStatusEnum implements IBaseEnum {

    /**
     * 进行中
     */
    NOT_START(-1, "未开始"),

    /**
     * 未签名
     */
    FAIL(0, "已失败"),

    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 已取消
     */
    CANCELLED(3, "已取消");

    private final Integer code;
    private final String description;
}
