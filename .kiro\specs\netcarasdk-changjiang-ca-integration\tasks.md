# 通过netcarasdk对接长江CA平台实施任务

## 实施计划

- [x] 1. 扩展ConfigKeyValueEnum枚举
  - 在ConfigKeyValueEnum中添加11个长江CA相关的配置项
  - 包括基础URL、CA通讯证书、第三方证书、签名算法、模板ID等配置
  - 添加超时和重试相关的配置项
  - 需求参考: _需求 2.1, 2.6_

- [x] 2. 创建CjcaException基础异常类（已重构命名）
  - 创建CjcaException基础异常类，包含错误码、错误消息和原因封装
  - 提供多种构造方法支持不同的异常创建场景
  - 实现标准的异常链传递机制
  - 需求参考: _需求 2.7_

- [x] 3. 创建CjcaException子异常类（已重构命名）
  - 创建CjcaConfigException配置相关异常
  - 创建CjcaConnectionException连接相关异常
  - 创建CjcaCmpProtocolException CMP协议相关异常
  - 需求参考: _需求 2.7_

- [x] 4. 创建CjcaErrorCode错误码枚举（已重构命名）
  - 定义长江CA相关的标准化错误码
  - 包括配置错误、连接失败、CMP协议错误、证书处理错误等
  - 提供错误码到用户友好消息的映射
  - 需求参考: _需求 2.7_

- [x] 5. 创建CjcaConfigManager基础结构（已重构命名）
  - 创建CjcaConfigManager类的基础结构
  - 配置依赖注入和配置常量定义
  - 实现基础的配置项常量定义
  - 需求参考: _需求 2.6_

- [ ] 6. 实现ChangjiangCAConfigManager配置获取方法
  - 实现所有配置项的获取方法，包括基础URL、证书解析、超时参数等
  - 实现证书Base64解码和X509Certificate对象创建
  - 实现私钥解析和PrivateKeyInfo对象创建
  - 提供配置项的默认值处理
  - 需求参考: _需求 2.6_

- [ ] 7. 实现ChangjiangCAConfigManager配置验证和CMP配置创建
  - 实现validateConfiguration配置验证方法
  - 实现createCmpMessageConfigManagement方法创建CMP配置对象
  - 提供详细的配置错误信息和修复建议
  - 需求参考: _需求 2.1, 2.6_

- [x] 8. 创建CjcaResponseAdapter基础结构（已重构命名）
  - 创建CjcaResponseAdapter类的基础结构
  - 定义主要适配方法的签名和接口
  - 实现通用的错误处理适配逻辑
  - 需求参考: _需求 2.2, 2.3, 2.4, 2.5_

- [ ] 9. 实现CmpRespResultAdapter证书申请和状态查询适配
  - 实现adaptCertificateApplicationResponse方法
  - 实现adaptCertificateStatusResponse方法
  - 处理CMP响应到NetcaBpmsResponse的转换
  - 需求参考: _需求 2.2, 2.3_

- [ ] 10. 实现CmpRespResultAdapter证书下载和注销适配
  - 实现adaptCertificateDownloadResponse方法
  - 实现adaptCertificateRevocationResponse方法
  - 处理证书文件的提取和格式转换
  - 需求参考: _需求 2.4, 2.5_

- [ ] 11. 创建ChangjiangCACertificateLifecycleManager基础结构
  - 创建ChangjiangCACertificateLifecycleManager类，继承AbstractCertificateLifecycleManager
  - 配置@Component注解和条件装配@ConditionalOnClass
  - 实现依赖注入ChangjiangCAConfigManager和CmpRespResultAdapter
  - 需求参考: _需求 2.1_

- [ ] 12. 实现HTTP请求发送和CMP响应解析通用方法
  - 实现sendCmpRequest方法处理HTTP通信
  - 实现parseCmpResponse方法解析CMP响应
  - 配置OkHttpClient的超时和重试机制
  - 实现SSL证书验证和错误处理
  - 需求参考: _需求 2.1, 2.7_

- [ ] 13. 实现doApplyCertificate证书申请方法
  - 实现证书申请的完整流程
  - 构建P10CertReqMessage和相关参数
  - 实现CMP消息编码和发送
  - 处理申请响应和状态返回
  - 需求参考: _需求 2.2_

- [ ] 14. 实现doQueryCertificateStatus证书状态查询方法
  - 实现证书状态查询功能
  - 使用PollReqMessage进行状态轮询
  - 处理各种证书状态（待审核、已签发、已拒绝等）
  - 实现自动触发证书下载的逻辑
  - 需求参考: _需求 2.3_

- [ ] 15. 实现doDownloadCertificate证书下载方法
  - 实现证书下载功能
  - 处理签名证书和加密证书的下载
  - 实现证书完整性和有效性验证
  - 处理证书格式转换和存储
  - 需求参考: _需求 2.4_

- [ ] 16. 实现doRevokeCertificate证书注销方法
  - 实现证书注销功能
  - 构建RevocationReqMessage注销请求
  - 支持不同的注销原因（密钥泄露、停止使用等）
  - 更新本地证书状态为已注销
  - 需求参考: _需求 2.5_

- [ ] 17. 实现doDecryptAdministratorPin方法
  - 实现doDecryptAdministratorPin方法，抛出UnsupportedOperationException异常
  - 提供清晰的错误信息说明该功能不被长江CA支持
  - 添加详细的JavaDoc文档说明不支持的原因
  - 需求参考: _需求 2.8, 2.9_

- [ ] 18. 创建ChangjiangCAAutoConfiguration自动配置类
  - 创建ChangjiangCAAutoConfiguration自动配置类
  - 配置@ConditionalOnClass条件装配，确保netcarasdk可用
  - 注册ChangjiangCAConfigManager、CmpRespResultAdapter和ChangjiangCACertificateLifecycleManager Bean
  - 实现Bean的依赖关系和生命周期管理
  - 需求参考: _需求 2.1, 2.6_

- [ ] 19. 扩展CertificateLifecycleManagerFactory工厂类
  - 在CertificateLifecycleManagerFactory中添加CJCA类型支持
  - 实现"cjca"类型的识别和Bean获取逻辑
  - 添加长江CA实现的Bean名称常量
  - 确保与现有工厂逻辑的兼容性
  - 需求参考: _需求 2.6_

- [ ] 20. 创建证书模板枚举ChangjiangCATemplateEnum
  - 创建ChangjiangCATemplateEnum枚举定义证书模板
  - 包括个人证书、员工证书、组织证书的RSA和SM2模板
  - 提供模板ID、类型和描述的映射关系
  - 实现模板查找和验证方法
  - 需求参考: _需求 2.2_

- [ ] 21. 实现证书模板映射和选择逻辑
  - 实现根据证书类型和算法选择合适模板ID的逻辑
  - 处理默认模板和自定义模板的选择
  - 实现模板配置的验证和错误处理
  - 提供模板不存在时的回退机制
  - 需求参考: _需求 2.2_

- [ ] 22. 实现配置验证和系统健康检查方法
  - 实现系统启动时的配置完整性验证
  - 实现运行时的长江CA平台连接健康检查
  - 提供配置问题的诊断和修复建议
  - 实现配置变更的热更新检测机制
  - 需求参考: _需求 2.1, 2.6, 2.7_

- [ ] 23. 编写ChangjiangCAConfigManagerTest单元测试
  - 编写ChangjiangCAConfigManager的完整单元测试
  - 测试所有配置获取方法的正确性和异常处理
  - 测试配置验证逻辑和错误场景
  - 测试CMP配置对象的创建和验证
  - 需求参考: _需求 2.6, 2.7_

- [ ] 24. 编写CmpRespResultAdapterTest单元测试
  - 编写CmpRespResultAdapter的单元测试
  - 测试各种CMP响应到NetcaBpmsResponse的转换
  - 测试错误响应的处理和适配
  - 验证响应数据的完整性和正确性
  - 需求参考: _需求 2.2, 2.3, 2.4, 2.5_

- [ ] 25. 编写ChangjiangCACertificateLifecycleManagerTest基础测试
  - 编写ChangjiangCACertificateLifecycleManager的基础单元测试
  - 测试类的构造、初始化和依赖注入
  - 测试条件装配和Bean注册的正确性
  - 验证抽象方法的实现完整性
  - 需求参考: _需求 2.1_

- [ ] 26. 编写证书申请和查询功能的单元测试
  - 编写doApplyCertificate方法的详细单元测试
  - 编写doQueryCertificateStatus方法的详细单元测试
  - 测试CMP消息构建和编码的正确性
  - 测试各种业务场景和边界条件
  - 需求参考: _需求 2.2, 2.3_

- [ ] 27. 编写证书下载和注销功能的单元测试
  - 编写doDownloadCertificate方法的详细单元测试
  - 编写doRevokeCertificate方法的详细单元测试
  - 测试证书文件处理和验证逻辑
  - 测试注销原因和状态更新的正确性
  - 需求参考: _需求 2.4, 2.5_

- [ ] 28. 编写异常处理和边界条件测试
  - 编写各种异常场景的测试用例
  - 测试网络连接失败、超时等异常情况
  - 测试配置错误和CMP协议错误的处理
  - 验证doDecryptAdministratorPin方法抛出UnsupportedOperationException
  - 需求参考: _需求 2.7, 2.8_

- [ ] 29. 编写配置管理和工厂类集成测试
  - 编写配置管理、工厂类和自动配置的集成测试
  - 测试Spring Boot应用上下文的正确加载
  - 验证Bean的依赖注入和生命周期管理
  - 测试配置切换和实现类选择的正确性
  - 需求参考: _需求 2.6, 2.10_

- [ ] 30. 编写长江CA平台连接和协议测试
  - 编写与长江CA平台连接的Mock测试
  - 测试CMP协议消息的编码和解码
  - 验证HTTP通信的正确性和错误处理
  - 测试SSL证书验证和安全连接
  - 需求参考: _需求 2.1, 2.7_

- [ ] 31. 编写完整证书生命周期流程集成测试
  - 编写从证书申请到下载的完整流程集成测试
  - 测试证书申请、状态查询、下载、注销的端到端流程
  - 验证业务数据的一致性和完整性
  - 测试并发操作和性能表现
  - 需求参考: _需求 2.2, 2.3, 2.4, 2.5_

- [ ] 32. 编写配置切换和热更新测试
  - 编写不同CA实现之间的切换测试
  - 测试配置热更新的正确性和稳定性
  - 验证系统在配置变更时的行为
  - 测试回退机制和错误恢复
  - 需求参考: _需求 2.6, 2.10_

- [ ] 33. 编写部署配置说明和数据库脚本
  - 编写详细的部署配置说明文档
  - 创建数据库初始化脚本，包含所有长江CA配置项
  - 提供配置项的详细说明和示例值
  - 编写系统环境要求和依赖检查脚本
  - 需求参考: _需求 2.1, 2.6_

- [ ] 34. 编写使用文档和故障排查指南
  - 编写用户使用文档和操作指南
  - 创建常见问题解答和故障排查指南
  - 提供性能调优和监控建议
  - 编写API文档和集成示例
  - 需求参考: _需求 2.7, 2.10_