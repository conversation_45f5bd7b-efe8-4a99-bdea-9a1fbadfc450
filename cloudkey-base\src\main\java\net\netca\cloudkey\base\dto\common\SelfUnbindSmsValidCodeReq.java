package net.netca.cloudkey.base.dto.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.netca.cloudkey.base.dto.abstractclass.HumanReadable;
import net.netca.cloudkey.base.dto.abstractclass.IUserToken;
import net.netca.cloudkey.base.util.CommonUtil;

import javax.validation.constraints.NotBlank;

/**
 * 自助解绑设备-获取短信验证的请求
 * <AUTHOR>
 * @date 2021-10-28 14:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelfUnbindSmsValidCodeReq extends ImgValidCodeReq implements IUserToken {

    /**
     * 用户身份验证成功后的令牌
     */
    @NotBlank(message = "UserToken 不能为空")
    private String userToken;
}
