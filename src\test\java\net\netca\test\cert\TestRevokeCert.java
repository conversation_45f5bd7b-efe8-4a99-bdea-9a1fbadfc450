package net.netca.test.cert;

import lombok.extern.apachecommons.CommonsLog;
import net.netca.pki.encoding.Hex;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.constants.BusinessTypeEnum;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.MultipleCmpReqContext;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.NestedMessage;
import net.netca.sdk.message.cmp.RevocationReqMessage;
import net.netca.sdk.util.StringUtils;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 证书注销
 *
 * @author: zys
 * @date: 2020/3/5 19:01
 */
@CommonsLog
public class TestRevokeCert extends TestCmpProtocol {

    @Test
    public void testRevokeCert() throws Exception {
        RevocationReqMessage revokeCertMessage = getRevokeCertMessage();
        List<CmpRespResult> cmpRespResults = normalRevokeCert(revokeCertMessage);
        cmpRespResults.forEach(
                cmpRespResult -> {
                    Assert.assertSame(PKIStatusEnum.ACCEPTED, cmpRespResult.getPkiStatusEnum());
                    if (cmpRespResult.getFailureReason() != null) {
                        log.error(cmpRespResult.getFailureReason());
                        log.error("PKIFailureInfoEnum's code is : " + cmpRespResult.getFailureCode());
                    }
                    log.info("证书注销成功");
                }
        );
    }

    @Test
    public void revokeCertByHumanApprove() throws Exception {
        String issuer = "CN=Mock_CA_SM2_Sub,O=mock,C=CN";
        String certSn = "67803a657494c77482f1c2b14187d55484552f67";
        RevocationReqMessage revocationReqMessage = getRevocationReqMessage(certSn, issuer);
        List<CmpRespResult> cmpRespResults = normalRevokeCert(revocationReqMessage);
        for (CmpRespResult cmpRespResult : cmpRespResults) {
            Assert.assertSame(PKIStatusEnum.WAITING, cmpRespResult.getPkiStatusEnum());
            CmpRespResult pollCmpRespResult;
            do {
                log.info("start poll....");
                long respCertRequestId = cmpRespResult.getRespCertRequestId();
                BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.REVOKE;
                pollCmpRespResult = getPollReqCmpRespResult(respCertRequestId, businessTypeEnum);
                String failureReason = pollCmpRespResult.getFailureReason();
                if (StringUtils.hasText(failureReason)) {
                    log.info("error code: " + pollCmpRespResult.getFailureCode());
                    log.info("error reason: " + failureReason);
                }
                log.info("poll end....");
                Thread.sleep(1000);
            } while (PKIStatusEnum.WAITING.equals(pollCmpRespResult.getPkiStatusEnum()));
            Assert.assertEquals(PKIStatusEnum.ACCEPTED.getCode(), pollCmpRespResult.getPkiStatusEnum().getCode());
            log.info("注销成功！");
        }
    }

    private List<CmpRespResult> normalRevokeCert(CmpMessage cmpMessage) throws Exception {
        List<CmpMessage> cmpMessageList = new ArrayList<>();
        cmpMessageList.add(cmpMessage);
        MultipleCmpReqContext context = new MultipleCmpReqContext();
        multipleMessageEncoder.encode(context, cmpMessageList);
        PKIMessages reqPkiMessages = context.getPKIMessages();
        Assert.assertNotNull(reqPkiMessages);
        byte[] reqData = reqPkiMessages.getEncoded();
        Assert.assertNotNull(reqData);
        byte[] respData = send(reqData);
        Assert.assertNotNull(respData);
        PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(respData));
        multipleMessageDecoder.decode(context, respPkiMessages);
        return context.getCmpRespResults();
    }

    @Test
    public void testRevokeCertWithNestedMessage() throws Exception {
        NestedMessage nestedMessage = getNestedMessage(getRevokeCertMessage(), BusinessTypeEnum.REVOKE);
        normalRevokeCert(nestedMessage);
    }

    private RevocationReqMessage getRevokeCertMessage() throws Exception {
        X509Certificate signCert = getNewSignCert();
        Assert.assertNotNull(signCert);
        String serialNumber = Hex.encode(false, signCert.getSerialNumber());
        String issuer = signCert.getIssuer().getLdapName();
        return getRevocationReqMessage(serialNumber, issuer);
    }

    private RevocationReqMessage getRevocationReqMessage(String certSn, String issuer) {
        System.out.println("certSn：" + certSn);
        System.out.println("issue：" + issuer);
        long certRequestId = Math.abs(new Random().nextLong());
        String senderStr = "OU=netca";
        String recipientStr = "CN=test";
        CustomFreeText customFreeText = CustomFreeText.builder().certReqId(certRequestId).build();
        // 构建证书注销 CMP 请求所需的参数，只需 senderStr、recipientStr、证书序列号、certRequestId（随机数）、注销原因、颁发者
        return RevocationReqMessage.defaultMessage().toBuilder()
                .senderStr(senderStr)
                .recipientStr(recipientStr)
                .issuer(issuer)
                .certSn(certSn)
                .certRequestId(certRequestId)
                .freeText(customFreeText)
                .reason(RevokeReasonEnum.UNSPECIFIED.getCode())//自选
                .build();
    }


}
