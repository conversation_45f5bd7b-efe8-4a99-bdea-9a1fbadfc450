package net.netca.cloudkey.base.dto.seal;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SealSignInfoRespDTO {

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;
    private String certCn;
    private String certSn;
    private Integer success;
    private String projectId;

}
