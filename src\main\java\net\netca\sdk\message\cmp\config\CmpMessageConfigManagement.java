package net.netca.sdk.message.cmp.config;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;

import java.util.Objects;

/**
 * 配置类
 *
 * <AUTHOR>
 */
@SuperBuilder
@Getter
public class CmpMessageConfigManagement {
    /**
     * 第三方服务通讯证书
     */
    X509Certificate thirdPartyServerCommCert;

    /**
     * RSA的情况
     * 第三方服务通讯证书的私钥信息
     */
    PrivateKeyInfo thirdPartyServerCommCertPrivateKeyInfo;

    /**
     * 第三方服务通讯证书的CA证书
     * 采用CA证书验证策略时，此值非空
     */
    X509Certificate caCert;

    /**
     * RA 通讯证书
     */
    X509Certificate communicationCert;

    /**
     * 第三方服务通讯证书的签名算法
     */
    String thirdPartyServerCommCertSignAlgoName;

    /**
     * 验证配置是否满足编码的要求
     */
    public void verifyEncodeValid() {
        Objects.requireNonNull(thirdPartyServerCommCert, "thirdPartyServerCommCert isn't allow to be null");
        Objects.requireNonNull(thirdPartyServerCommCertPrivateKeyInfo, "thirdPartyServerCommCertPrivateKey isn't allow to be null");
        Objects.requireNonNull(thirdPartyServerCommCertSignAlgoName, "thirdPartyServerCommCertSignAlgoName isn't allow to be null");
    }

    /**
     * 验证配置是否满足解码的要求
     */
    public void verifyDecodeValid() {
        Objects.requireNonNull(communicationCert, "communicationCert isn't allow to be null");
    }


    /**
     * 获取CMP协议需要携带的证书
     *
     * @return {@link X509Certificate[]}
     */
    public X509Certificate[] getCertsNeedToCarry() {
        if (caCert != null && thirdPartyServerCommCert != null) {
            return new X509Certificate[]{caCert, thirdPartyServerCommCert};
        }

        if (thirdPartyServerCommCert != null) {
            return new X509Certificate[]{thirdPartyServerCommCert};
        }
        return null;
    }
}
