package net.netca.cloudkey.base.dto.operator;

import net.netca.cloudkey.base.exception.CloudKeyException;
import net.netca.cloudkey.base.util.CommonUtil;
import org.springframework.beans.BeanUtils;

import java.util.Date;

public class OriginalDto {
	
	private String originalText;
	
	private Date signedTime;
	private String requestContent;
	private Boolean detached;
	
	public OriginalDto() {}
	
	public OriginalDto(String originalText) throws CloudKeyException {
		if(CommonUtil.isStringEmpty(originalText)){
			throw new CloudKeyException("签名原文为空");
		}
		
		OriginalDto original = CommonUtil.parseObject(originalText, OriginalDto.class);
		
		if(original.signedTime == null){
			throw new CloudKeyException("签名的时间为空");
		}
		
		if(original.detached == null){
			throw new CloudKeyException("签名原文的是否携带原文为空");
		}
		
		BeanUtils.copyProperties(original, this);
		this.originalText = originalText;
	}

	public String getOriginalText() {
		return originalText;
	}

	public void setOriginalText(String originalText) {
		this.originalText = originalText;
	}
	
	public String getRequestContent() {
		return requestContent;
	}
	
	public void setRequestContent(String requestContent) {
		this.requestContent = requestContent;
	}

	public Date getSignedTime() {
		return signedTime;
	}

	public void setSignedTime(Date signedTime) {
		this.signedTime = signedTime;
	}
	public boolean isDetached() {
		return detached;
	}
	public void setDetached(Boolean detached) {
		this.detached = detached;
	}
}
