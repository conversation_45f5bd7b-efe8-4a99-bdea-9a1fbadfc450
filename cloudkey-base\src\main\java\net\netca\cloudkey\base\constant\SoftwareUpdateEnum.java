package net.netca.cloudkey.base.constant;

import lombok.AllArgsConstructor;
import net.netca.cloudkey.base.constant.base.IBaseEnum;

@AllArgsConstructor
public enum SoftwareUpdateEnum implements IBaseEnum {

    NOT_NEED_UPDATE(0, "不需要更新"),
    FORCE_UPDATE(1, "强制更新"),
    OPTIONAL_UPDATE(2, "可选更新");

    private Integer code;
    private String description;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
