package net.netca.cloudkey.base.constant;

/**
 * quartz 调度任务用到的相关常量包括：
 * 1、job_group job_name 常量
 * 2、(状态为)启动、立即执行常量
 */
public class QuartzJobConstant {

    // job_group
    public static final String CERT_RESERVE_GROUP = "cert_reserve_task";

    public static final String DOWN_CERT_GROUP = "down_cert";

    /** job_name {@link net.netca.cloudkeyserver.schedule.quartz.job.DownCertJob} */
    public static final String DOWN_CERT = "down_cert";

    // job_name
    public static final String CERT_RESERVE_REVOKE = "cert_revoke";

    public static final Integer ENABLE = 1;

    public static final Integer IMMEDIATELY = 1;

    // 指的是"修改"失败 如预约注销时，准备更新预约任务的数据时，定时任务将其执行了(status 修改到1)
    // 故在 update 时 有条件 where status = 1; 而导致更新的数据量为0  即这个任务更新失败
    public static final Integer UPDATE_ERROR = 0;

}
