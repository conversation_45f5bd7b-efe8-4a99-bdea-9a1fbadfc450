package net.netca.cloudkey.base.dto.inspection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.netca.cloudkey.base.dto.common.TimestampToken;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 用于接收校验用户是否存在的dto
 * @Author: dyr
 * @CreateDate: 2020/7/2 11:18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchProjectIdReqDTO extends TimestampToken {

    @NotBlank(message = "工号不能为空")
    private String uid;

}
