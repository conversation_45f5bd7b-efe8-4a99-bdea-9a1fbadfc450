package net.netca.cloudkey.base.dto.businessuser;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessUserDetailDTO  {

    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 用户唯一标识符
     */
    private String uid;
    private String name;

    private String phone;

    private String province;
    private String city;
    private String officialResidence;
    private String countryName;
    private Integer gender;
    private Integer identityType;

    private String identity;
    private Integer makeCertStatus;
    /**
     * 已制证对数
     */
    private Integer makeCertCount;
    private Integer status;

    private String email;

    private Integer organizationId;

    private String department;

    private String occupation;

    private String cloudkeyProjectId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
    /**
     * 冗余config_project的cert_user_type
     */
    private Integer certUserType;


    /**
     * 指定证书到期时间 精度天
     * 允许为空 将使用项目配置
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date certValidityEnd;

    /**
     * 院区ID
     */
    private Integer districtId;

}
