package net.netca.sdk.constants;

import net.netca.pki.encoding.asn1.ASN1Exception;
import net.netca.pki.encoding.asn1.BitString;

/**
 * CMP协议规范规定的错误原因
 */
public enum PKIFailureInfoEnum implements IBaseEnum {

    BAD_ALG(org.bouncycastle.asn1.cmp.PKIFailureInfo.badAlg, "badAlg"),
    BAD_MESSAGE_CHECK(org.bouncycastle.asn1.cmp.PKIFailureInfo.badMessageCheck, "badMessageCheck"),
    BAD_REQUEST(org.bouncycastle.asn1.cmp.PKIFailureInfo.badRequest, "badRequest"),
    BAD_TIME(org.bouncycastle.asn1.cmp.PKIFailureInfo.badTime, "badTime"),
    BAD_CERT_ID(org.bouncycastle.asn1.cmp.PKIFailureInfo.badCertId, "badCertId"),
    BAD_DATA_FORMAT(org.bouncycastle.asn1.cmp.PKIFailureInfo.badDataFormat, "badDataFormat"),
    WRONG_AUTHORITY(org.bouncycastle.asn1.cmp.PKIFailureInfo.wrongAuthority, "wrongAuthority"),
    INCORRECT_DATA(org.bouncycastle.asn1.cmp.PKIFailureInfo.incorrectData, "incorrectData"),
    MISSING_TIME_STAMP(org.bouncycastle.asn1.cmp.PKIFailureInfo.missingTimeStamp, "missingTimeStamp"),
    BAD_POP(org.bouncycastle.asn1.cmp.PKIFailureInfo.badPOP, "badPOP"),
    CERT_REVOKED(org.bouncycastle.asn1.cmp.PKIFailureInfo.certRevoked, "certRevoked"),
    CERT_CONFIRMED(org.bouncycastle.asn1.cmp.PKIFailureInfo.certConfirmed, "certConfirmed"),
    WRONG_INTEGRITY(org.bouncycastle.asn1.cmp.PKIFailureInfo.wrongIntegrity, "wrongIntegrity"),
    BAD_RECIPIENT_NONCE(org.bouncycastle.asn1.cmp.PKIFailureInfo.badRecipientNonce, "badRecipientNonce"),
    TIME_NOT_AVAILABLE(org.bouncycastle.asn1.cmp.PKIFailureInfo.timeNotAvailable, "timeNotAvailable"),
    UNACCEPTED_POLICY(org.bouncycastle.asn1.cmp.PKIFailureInfo.unacceptedPolicy, "unacceptedPolicy"),
    UNACCEPTED_EXTENSION(org.bouncycastle.asn1.cmp.PKIFailureInfo.unacceptedExtension, "unacceptedExtension"),
    ADD_INFO_NOT_AVAILABLE(org.bouncycastle.asn1.cmp.PKIFailureInfo.addInfoNotAvailable, "addInfoNotAvailable"),
    BAD_SENDER_NONCE(org.bouncycastle.asn1.cmp.PKIFailureInfo.badSenderNonce, "badSenderNonce"),
    BAD_CERT_TEMPLATE(org.bouncycastle.asn1.cmp.PKIFailureInfo.badCertTemplate, "badCertTemplate"),
    SIGNER_NOT_TRUSTED(org.bouncycastle.asn1.cmp.PKIFailureInfo.signerNotTrusted, "signerNotTrusted"),
    TRANSACTION_ID_IN_USE(org.bouncycastle.asn1.cmp.PKIFailureInfo.transactionIdInUse, "transactionIdInUse"),
    UNSUPPORTED_VERSION(org.bouncycastle.asn1.cmp.PKIFailureInfo.unsupportedVersion, "unsupportedVersion"),
    NOT_AUTHORIZED(org.bouncycastle.asn1.cmp.PKIFailureInfo.notAuthorized, "notAuthorized"),
    SYSTEM_UNAVAIL(org.bouncycastle.asn1.cmp.PKIFailureInfo.systemUnavail, "systemUnavail"),
    SYSTEM_FAILURE(org.bouncycastle.asn1.cmp.PKIFailureInfo.systemFailure, "systemFailure"),
    DUPLICATE_CERT_REQ(org.bouncycastle.asn1.cmp.PKIFailureInfo.duplicateCertReq, "duplicateCertReq");


    private Integer code;
    private String description;

    PKIFailureInfoEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static BitString createPKIFailureInfo(PKIFailureInfoEnum pkiFailureInfoEnum) throws ASN1Exception {
        boolean[] failureInfo = new boolean[27];
        failureInfo[pkiFailureInfoEnum.code] = true;
        return new BitString(failureInfo);
    }

    public static PKIFailureInfoEnum toPKIFailureInfo(int code) throws Exception {
        switch (code) {
            case 0:
                return BAD_ALG;
            case 1:
                return BAD_MESSAGE_CHECK;
            case 2:
                return BAD_REQUEST;
            case 3:
                return BAD_TIME;
            case 4:
                return BAD_CERT_ID;
            case 5:
                return BAD_DATA_FORMAT;
            case 6:
                return WRONG_AUTHORITY;
            case 7:
                return INCORRECT_DATA;
            case 8:
                return MISSING_TIME_STAMP;
            case 9:
                return BAD_ALG;
            case 10:
                return CERT_REVOKED;
            case 11:
                return CERT_CONFIRMED;
            case 12:
                return WRONG_INTEGRITY;
            case 13:
                return BAD_RECIPIENT_NONCE;
            case 14:
                return TIME_NOT_AVAILABLE;
            case 15:
                return UNACCEPTED_POLICY;
            case 16:
                return UNACCEPTED_EXTENSION;
            case 17:
                return ADD_INFO_NOT_AVAILABLE;
            case 18:
                return BAD_SENDER_NONCE;
            case 19:
                return BAD_CERT_TEMPLATE;
            case 20:
                return SIGNER_NOT_TRUSTED;
            case 21:
                return TRANSACTION_ID_IN_USE;
            case 22:
                return UNSUPPORTED_VERSION;
            case 23:
                return NOT_AUTHORIZED;
            case 24:
                return SYSTEM_UNAVAIL;
            case 25:
                return SYSTEM_FAILURE;
            case 26:
                return DUPLICATE_CERT_REQ;
            default:
                return null;
        }
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
