package net.netca.cloudkey.base.constraints.check;

import net.netca.cloudkey.base.javaconfig.RegExpConfig;

import java.util.regex.Pattern;

public enum IdentityCheck implements IChecker {

	IDNUM(new IChecker() {
		@Override
		public boolean check(String value) {// 检查是否身份证号码
			if (IdCardUtil.checkIdNum(value)) {
				return true;
			} else {
				return false;
			}
		}
	}, "身份证号", 1),

	HKIDNUM(new IChecker() {
		@Override
		public boolean check(String value) {// 检查是否身份证号码
			if (HKIdCardUtil.checkIdNum(value)) {
				return true;
			} else {
				return false;
			}
		}
	}, "香港身份证号", 8),

	TAXID(new IChecker() {
		private Pattern regex;

		@Override
		public boolean check(String value) {
			if (regex == null) {
				regex = Pattern.compile(RegExpConfig.taxId);
			}
			return regex.matcher(value).matches() && value.length() <= 20;
		}
	}, "税务登记证号", 504),

	SOCIALCREDITCODE(new IChecker() {
		@Override
		public boolean check(String value) {// 检查是否统一社会信用代码
			return SocialCreditCodeUtil.check(value);
		}
	}, "统一社会信用代码", 513),

	DEDAULT(new IChecker() {
		@Override
		public boolean check(String value) {// 检查是否统一社会信用代码
			return true;
		}
	}, "默认不校验", -1);

	private IdentityCheck(IChecker checker, String description, Integer identityType) {
		this.checker = checker;
		this.description = description;
		this.identityType = identityType;
	}
	
	private IChecker checker;
	private String description;
	private Integer identityType;

	@Override
	public boolean check(String value){
		return checker.check(value);
	}

	public static IdentityCheck getChecker(Integer identityType) {
		IdentityCheck[] values = IdentityCheck.values();
		for (IdentityCheck identityCheck : values) {
			if (identityCheck.identityType.equals(identityType)) {
				return identityCheck;
			}
		}
		return IdentityCheck.DEDAULT;
	}
	
	public String getDescription() {
		return description;
	}


}
