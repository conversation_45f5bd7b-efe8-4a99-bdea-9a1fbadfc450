package net.netca.test.asn1.format;


import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.*;
import net.netca.pki.encoding.asn1.pki.AttributeTypeAndValue;
import net.netca.test.constant.AttributeTypeValueMapping;
import net.netca.test.constant.EncodeTypeEnum;

import java.lang.Integer;
import java.util.Objects;

public class AttributeTypeValueFormat implements Comparable<AttributeTypeValueFormat>{
	private String attrOid;
	/**
	 * IA5String|VisibleString|BMPString|UTF8String|GeneralizedTime|PostalAddress
	 */
	private EncodeTypeEnum encodeType;
	private String value;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		AttributeTypeValueFormat that = (AttributeTypeValueFormat) o;
		return Objects.equals(attrOid, that.attrOid) &&
				encodeType == that.encodeType &&
				Objects.equals(value, that.value) &&
				Objects.equals(order, that.order) &&
				Objects.equals(insertOrder, that.insertOrder) &&
				Objects.equals(visualDNName, that.visualDNName);
	}

	@Override
	public int hashCode() {
		return Objects.hash(attrOid, encodeType, value, order, insertOrder, visualDNName);
	}

	private Integer order;

	private Integer insertOrder;
	
	/**
	 * 用于给系统用户查看用的DN的名称
	 */
	private String visualDNName;
	
	public EncodeTypeEnum getEncodeType() {
		return encodeType;
	}
	public void setEncodeType(EncodeTypeEnum encodeType) {
		this.encodeType = encodeType;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getAttrOid() {
		return attrOid;
	}
	public void setAttrOid(String attrOid) {
		this.attrOid = attrOid;
	}
	@Override
	public int compareTo(AttributeTypeValueFormat o) {
		 if(o == null ) return -1;
		 if(this.order == null && o.getOrder() == null ) return 0;
		 if(this.order == null) return 1;
		 if(o.getOrder() == null) return -1;
		 
		 if(this.order < o.getOrder()){
			 return 1;
		 }else if(this.order > o.getOrder()){
			 return -1;
		 }

		 //order相同，则比较insertOrder;
		 if(this.insertOrder == null){
		 	this.insertOrder = Integer.MIN_VALUE;
		 }

		 if(o.getInsertOrder() == null){
			o.setInsertOrder(Integer.MIN_VALUE);
		 }

		 if(this.insertOrder < o.getInsertOrder()){
		 	return 1;
		 }else if(this.insertOrder > o.getInsertOrder()){
		 	return -1;
		 }
		 
		 return 0;
	}
	

	public AttributeTypeAndValue findAttributeTypeAndValue() throws PkiException {
		String attribyteTypeOid = AttributeTypeValueMapping.getOidFromAttributeType(this.getAttrOid());
		
		ASN1Object value = encodeType.createASN1Object(this.getValue());
		return new AttributeTypeAndValue(attribyteTypeOid, value);
	}
	
	public Integer getOrder() {
		return order;
	}
	public void setOrder(Integer order) {
		this.order = order;
	}
	
	public static AttributeTypeValueFormat deserialize(AttributeTypeAndValue attributeTypeAndValue) throws PkiException {
		AttributeTypeValueFormat attributeTypeValueFormat = new AttributeTypeValueFormat();
		String attributeType = attributeTypeAndValue.getType();
		ASN1Object asn1Object = attributeTypeAndValue.getValue();
		String attributeValue = attributeTypeAndValue.getStringValue();
		
		attributeTypeValueFormat.setAttrOid(attributeType);
		attributeTypeValueFormat.setValue(attributeValue);
		if(asn1Object instanceof BMPString) {
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.BMPString);
		} else if(asn1Object instanceof IA5String) {
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.IA5String);
		}  else if(asn1Object instanceof PrintableString) {
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.PrintableString);
		} else if(asn1Object instanceof UTF8String || asn1Object instanceof Unknown) {
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.UTF8String);
		} else if(asn1Object instanceof VisibleString) {
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.VisibleString);
		} else if (asn1Object instanceof GeneralizedTime){
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.GeneralizedTime);
		} else if (asn1Object instanceof SequenceOf){
			attributeTypeValueFormat.setEncodeType(EncodeTypeEnum.PostalAddress);
		}
		
		attributeTypeValueFormat.setVisualDNName(AttributeTypeValueMapping.getAttributeTypeFromOid(attributeType));
		
		return attributeTypeValueFormat;
	}
	public String getVisualDNName() {
		return visualDNName;
	}
	public void setVisualDNName(String visualDNName) {
		this.visualDNName = visualDNName;
	}


	public Integer getInsertOrder() {
		return insertOrder;
	}

	public void setInsertOrder(Integer insertOrder) {
		this.insertOrder = insertOrder;
	}
}
