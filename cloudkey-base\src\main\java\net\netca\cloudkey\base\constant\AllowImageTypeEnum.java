package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.formatter.image.BmpImagerFormatter;
import net.netca.cloudkey.base.formatter.image.ImageFormatter;
import net.netca.cloudkey.base.formatter.image.JpegImageFormatter;
import net.netca.cloudkey.base.formatter.image.PngImageFormatter;
import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.util.CustomStringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * @descriptions: 云密钥允许的签章图片
 * 包括格式转换
 * @date: 2019/7/8
 * @author: SaromChars
 */
public enum AllowImageTypeEnum implements IBaseEnum {

    //WARN!!!!
    // 使用小写描述图片类型
    DEFAULT(0, new String[]{"default"}, null),
    JPG(1, new String[]{"jpg", "jpeg"}, new JpegImageFormatter()),
    BMP(2, new String[]{"bmp"}, new BmpImagerFormatter()),
    PNG(3, new String[]{"png"}, new PngImageFormatter()),
    GIF(4, new String[]{"gif"}, null),
    ;

    private Integer code;
    private String[] descriptions;
    private ImageFormatter imageFormatter;

    private AllowImageTypeEnum(int code, String[] descriptions, ImageFormatter imageFormatter) {
        this.code = code;
        this.descriptions = descriptions;

        this.imageFormatter = imageFormatter;
    }

    public static AllowImageTypeEnum getImageTypeByDescription(String description) {
        if (CustomStringUtils.isBlank(description)) {
            throw new CloudKeyRuntimeException(WebResultEnum.ERROR.getCode(), "签章图片类型读取失败");
        }
        String descriptionLowerCase = description.toLowerCase();
        Optional<AllowImageTypeEnum> first = Arrays.stream(AllowImageTypeEnum.values())
                .filter(allowImageTypeEnum -> Arrays.stream(allowImageTypeEnum.descriptions)
                        .filter(s -> s.equals(descriptionLowerCase)).findFirst().isPresent())
                .findFirst();

        checkImgType(first);

        return first.get();
    }

    private static void checkImgType(Optional<AllowImageTypeEnum> first) {
        if (!first.isPresent()) {
            String imgTypeSupportedMergeStr = Arrays.stream(AllowImageTypeEnum.values())
                    .map(allowImageTypeEnum -> Arrays.stream(allowImageTypeEnum.descriptions).reduce((s, s2) -> s + "/" + s2).get())
                    .reduce((s, s2) -> CustomStringUtils.isBlank(s) ? s2 : s + "、" + s2)
                    .orElse(":服务端暂不支持任何类型");

            throw new CloudKeyRuntimeException(WebResultEnum.ERROR.getCode(), "非法的图片类型，仅支持" + imgTypeSupportedMergeStr);
        }
    }

    public static AllowImageTypeEnum getImageTypeByCode(Integer code) {

        Optional<AllowImageTypeEnum> first = Arrays.stream(AllowImageTypeEnum.values())
                .filter(allowImageTypeEnum -> allowImageTypeEnum.code.equals(code))
                .findFirst();

        checkImgType(first);

        return first.get();
    }


    public ImageFormatter getImageFormatter() {
        return imageFormatter;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return this.descriptions[0];
    }
}
