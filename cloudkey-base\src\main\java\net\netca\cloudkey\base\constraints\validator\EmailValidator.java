package net.netca.cloudkey.base.constraints.validator;

import net.netca.cloudkey.base.constraints.annotation.Email;
import net.netca.cloudkey.base.constraints.check.CheckType;
import net.netca.cloudkey.base.util.CustomStringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EmailValidator implements ConstraintValidator<Email, String> {

    @Override
    public void initialize(Email constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        boolean isValid = true;
        if (!CustomStringUtils.isBlank(value)) {
            isValid = CheckType.EMAIL.check(value);
        }
        return isValid;
    }
}
