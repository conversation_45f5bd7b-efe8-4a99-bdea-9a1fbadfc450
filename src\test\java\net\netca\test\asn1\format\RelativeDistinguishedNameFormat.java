package net.netca.test.asn1.format;

import net.netca.pki.PkiException;
import net.netca.pki.encoding.asn1.ASN1TypeManager;
import net.netca.pki.encoding.asn1.SetOf;
import net.netca.pki.encoding.asn1.SetOfType;
import net.netca.pki.encoding.asn1.pki.RelativeDistinguishedName;

import java.util.List;

public class RelativeDistinguishedNameFormat {
	private List<AttributeTypeValueFormat> attributeTypeValueFormat;

	public List<AttributeTypeValueFormat> getAttributeTypeValueFormat() {
		return attributeTypeValueFormat;
	}

	public void setAttributeTypeValueFormat(List<AttributeTypeValueFormat> attributeTypeValueFormat) {
		this.attributeTypeValueFormat = attributeTypeValueFormat;
	}
	
	public RelativeDistinguishedName generateRelativeDistinguishedName() throws PkiException {
		SetOf setOf = null;
		setOf=new SetOf((SetOfType) ASN1TypeManager.getInstance().get("RelativeDistinguishedName"));
		for (AttributeTypeValueFormat atv : attributeTypeValueFormat) {
			setOf.add(atv.findAttributeTypeAndValue().getASN1Object());
		}
		
		return new RelativeDistinguishedName(setOf);
	}
}
