---
type: "manual"
---

### Spec Task Execution Guidelines

This rule governs how the model should handle user requests related to specification tasks, whether executing tasks or answering questions about them.

**Constraints:**

- The model MUST read the specs requirements.md, design.md, and tasks.md files before executing any tasks
- The model MUST NOT execute tasks without first understanding the requirements and design context
- The model MUST use the `view` tool to examine spec files before task execution
- The model MUST focus on ONE task at a time and never implement functionality for multiple tasks simultaneously
- The model MUST start with sub-tasks when the requested task contains them
- The model MUST verify implementation against requirements specified in the task details
- The model MUST stop after completing the requested task and wait for user review
- The model MUST NOT automatically proceed to the next task without explicit user instruction
- The model MUST distinguish between task execution requests and task information requests
- When the user doesn't specify which task to work on, the model MUST examine the task list and recommend the next appropriate task
- The model MUST provide task information without execution when users ask questions about tasks rather than requesting execution

**Tool Usage Guidelines:**
- Use `view` tool to read requirements.md, design.md, and tasks.md files before task execution
- Use `codebase-retrieval` tool to understand existing code patterns relevant to the task
- Use appropriate editing tools (`str-replace-editor`, `save-file`) for task implementation
- Use task management tools to track progress and update task status

**Interaction Flow:**
1. Determine if the user wants to execute a task or ask questions about tasks
2. For task execution:
   - Read all relevant spec files (requirements.md, design.md, tasks.md)
   - Identify the specific task and any sub-tasks
   - Execute only the requested task or its first sub-task
   - Verify implementation against task requirements
   - Stop and request user review before proceeding
3. For task questions:
   - Provide requested information about tasks
   - Do not initiate task execution unless explicitly requested
4. When task selection is unclear:
   - Examine the task list
   - Recommend the next appropriate task for execution
   - Wait for user confirmation before proceeding

**Critical Rules:**
- ONE TASK AT A TIME - Never implement multiple tasks simultaneously
- STOP AFTER COMPLETION - Always wait for user review before continuing
- READ SPECS FIRST - Never execute tasks without understanding requirements and design
- DISTINGUISH INTENT - Differentiate between execution requests and information requests
