package net.netca.cloudkey.base.constraints.check;

import net.netca.cloudkey.base.javaconfig.RegExpConfig;
import net.netca.cloudkey.base.util.CustomStringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IdCardUtil {

	private static Map<Integer, String> cityMap = null;
	
	//检查号码是否符合规范，包括长度，类型
    private static boolean isCardNo(String card) {
        //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
        Pattern regex_CardNo = Pattern.compile(RegExpConfig.idCardAll);
        return regex_CardNo.matcher(card).matches();
    };
    
    //取身份证前两位,校验省份
    private static boolean checkProvince(String card) {
        int province = Integer.parseInt(card.substring(0, 2));
        if(cityMap == null){
        	cityMap = new HashMap<Integer, String>();
        	cityMap.put(11, "北京");
    		cityMap.put(12, "天津");
    		cityMap.put(13, "河北");
    		cityMap.put(14, "山西");
    		cityMap.put(15, "内蒙古");
    		cityMap.put(21, "辽宁");
    		cityMap.put(22, "吉林");
    		cityMap.put(23, "黑龙江");
    		cityMap.put(31, "上海");
    		cityMap.put(32, "江苏");
    		cityMap.put(33, "浙江");
    		cityMap.put(34, "安徽");
    		cityMap.put(35, "福建");
    		cityMap.put(36, "江西");
    		cityMap.put(37, "山东");
    		cityMap.put(41, "河南");
    		cityMap.put(42, "湖北");
    		cityMap.put(43, "湖南");
    		cityMap.put(44, "广东");
    		cityMap.put(45, "广西");
    		cityMap.put(46, "海南");
    		cityMap.put(50, "重庆");
    		cityMap.put(51, "四川");
    		cityMap.put(52, "贵州");
    		cityMap.put(53, "云南");
    		cityMap.put(54, "西藏");
    		cityMap.put(61, "陕西");
    		cityMap.put(62, "甘肃");
    		cityMap.put(63, "青海");
    		cityMap.put(64, "宁夏");
    		cityMap.put(65, "新疆");
    		cityMap.put(71, "台湾");
    		cityMap.put(81, "香港");
    		cityMap.put(82, "澳门");
    		cityMap.put(91, "国外");
        }
        if (cityMap.get(province) == null) {
            return false;
        }
        return true;
    };
    
    //检查生日是否正确
    private static boolean checkBirthday(String card) {
        int len = card.length();
        //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
        if (len == 15) {
        	Pattern regex_fifteen = Pattern.compile(RegExpConfig.idCard15);;
            Matcher matcher_fifteen = regex_fifteen.matcher(card);
            matcher_fifteen.find();
            int year = Integer.parseInt(matcher_fifteen.group(2));
            int month = Integer.parseInt(matcher_fifteen.group(3));
            int day = Integer.parseInt(matcher_fifteen.group(4));
            Calendar birthdayC = Calendar.getInstance();
            birthdayC.set(year + 1900, month-1, day);
            return verifyBirthday(1900 + year, month, day, birthdayC.getTime());
        }
        //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
        if (len == 18) {
        	Pattern regex_eighteen = Pattern.compile(RegExpConfig.idCard18);
        	Matcher matcher_eighteen = regex_eighteen.matcher(card);
        	matcher_eighteen.find();
            int year = Integer.parseInt(matcher_eighteen.group(2));
            int month = Integer.parseInt(matcher_eighteen.group(3));
            int day = Integer.parseInt(matcher_eighteen.group(4));
            Calendar birthdayC = Calendar.getInstance();
            birthdayC.set(year, month-1, day);
            return verifyBirthday(year, month, day, birthdayC.getTime());
        }
        return false;
    };
    
    //校验日期
    private static boolean verifyBirthday(int year, int month, int day, Date birthday) {
        Calendar nowC = Calendar.getInstance();
        nowC.setTime(new Date());
        Calendar birthdayC = Calendar.getInstance();
        birthdayC.setTime(birthday);
        int now_year = nowC.get(Calendar.YEAR);
        
        //年月日是否合理
        if (birthdayC.get(Calendar.YEAR) == year && (birthdayC.get(Calendar.MONTH) + 1) == month && birthdayC.get(Calendar.DAY_OF_MONTH) == day) {
            //判断年份的范围（3岁到100岁之间)
            int time = now_year - year;
            if (time >= 3 && time <= 100) {
                return true;
            }
            return false;
        }
        return false;
    };
    
    //校验位的检测
    private static boolean checkParity(String card) {
        int len = card.length();
        if(len == 15)return true;
        if (len == 18) {
            int[] arrInt = new int[]{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
            char[] arrCh = new char[]{'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
            int cardTemp = 0;
            for (int i = 0; i < 17; i++) {
                cardTemp += Integer.parseInt(card.substring(i, i+1)) * arrInt[i];
            }
            char valnum = arrCh[cardTemp % 11];
            if (valnum == card.substring(17, 18).toUpperCase().charAt(0)){
                return true;
            }
            return false;
        }
        return false;
    };

    /**
     * 检查身份证号
     * @param idnum
     * @return
     */
    public static boolean checkIdNum(String idnum){
    	 //是否为空
        if (CustomStringUtils.isBlank(idnum)) {
            return false;
        }
        //校验长度，类型
        if (!isCardNo(idnum)) {
            return false;
        }
        //检查省份
        if (!checkProvince(idnum)) {
            return false;
        }
        //校验生日
        if (!checkBirthday(idnum)) {
            return false;
        }
        //检验位的检测
        return checkParity(idnum);
    }

    /**
     *
     * @param idnum
     * @return 0为男，1为女
     */
    public static Long getSex(String idnum){
        if(!checkIdNum(idnum)){
            return null;
        }
        if(Integer.parseInt(idnum.substring(16, 17))%2 == 0){
            return 1L;
        }else{
            return 0L;
        }
    }
}
