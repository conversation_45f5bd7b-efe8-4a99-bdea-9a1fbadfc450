package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

public enum EnablePinRuleEnum implements IBaseEnum {

    DISABLE(0, "关闭强密码校验"),
    ENABLE(1, "开启强密码校验")
    ;

    private Integer code;
    private String description;

    EnablePinRuleEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

}
