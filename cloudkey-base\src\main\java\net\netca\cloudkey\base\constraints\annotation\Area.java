package net.netca.cloudkey.base.constraints.annotation;

import net.netca.cloudkey.base.constraints.validator.AreaValidator;
import net.netca.cloudkey.base.dto.abstractclass.IAreaReqDTO;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 对区域相关字段的校验注解，包括国家、省份、城市
 * 注解在类上，类需要实现IAreaReqDTO接口
 * @see IAreaReqDTO
 * @see AreaValidator
 */
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy={AreaValidator.class})
@Documented
public @interface Area {

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
