package net.netca.sdk.codec.cmp.lambda;

import net.netca.pki.encoding.asn1.pki.CertificationRequest;
import net.netca.sdk.message.cmp.CmpMessage;

/**
 * <AUTHOR>
 * @date 2020-04-07 17:46
 */
public class P10CertReqMessageHandler {


    /*public BiPredicate<CmpProtocolContext, CmpMessage> test() {
        return (context, cmpMessage) -> context instanceof SingleCertReqContext && cmpMessage instanceof P10CertReqMessage;
    }

    public BiConsumer<CmpProtocolContext, CmpMessage> accept() {
        return (context, cmpMessage) -> {
            CmpMessageConfigManagement encoderConfigManagement = context.getCmpMessageConfigManagement();
            encoderConfigManagement.verifyEncodeValid();
            P10CertReqMessage p10CertReqMessage = (P10CertReqMessage) cmpMessage;
            ((SingleCertReqContext) context).setPkiMessage(generateRegisteredReqDataByP10(p10CertReqMessage));
        };
    }

    *//**
     * 通过 {@link CmpMessage} 产生 cmp 的证书注册 请求数据 PKIMessage
     *
     * @param p10CertReqMessage
     * @return
     * @throws Exception
     *//*
    private PKIMessage generateRegisteredReqDataByP10(P10CertReqMessage p10CertReqMessage) throws Exception {
        PKIHeader pkiHeader = generatePKIHeader(p10CertReqMessage.getSenderStr(),
                p10CertReqMessage.getRecipientStr(),
                p10CertReqMessage.getPvno(),
                p10CertReqMessage.isSetTransactionID(),
                p10CertReqMessage.isSetSenderNonce(),
                p10CertReqMessage.isUnknownProtectionAlg(),
                p10CertReqMessage.isPublishLdap(),
                p10CertReqMessage.getRequestExtensions(),
                p10CertReqMessage.getCustomFreeText()).build();
        PKIBody pkiBody = this.generateP10PKIBody(p10CertReqMessage.getP10());
        return signPKIMessage(new PKIMessage(pkiHeader, pkiBody));
    }

    *//**
     * 产生P10的PKIBody
     *
     * @param p10 {@link CertificationRequest} 证书请求
     * @return
     *//*
    private PKIBody generateP10PKIBody(CertificationRequest p10) {
        Objects.requireNonNull(p10, "p10 is allowed to be null");
        org.bouncycastle.asn1.pkcs.CertificationRequest certificationRequest = org.bouncycastle.asn1.pkcs.CertificationRequest.getInstance(p10.derEncode());
        return new PKIBody(PKIBody.TYPE_P10_CERT_REQ, certificationRequest);
    }*/


}
