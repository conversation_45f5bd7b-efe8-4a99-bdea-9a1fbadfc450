package net.netca.test.config;

/**
 * <AUTHOR>
 * @date 2020-11-02 16:31
 */
public class GMConfig implements Config {
    public static String caCommCertSha256 = "534b8c111ce13499f80c1862610b7b30bfdb01f41b0a2e32c90b95b750d557bf";

    public static String certTempleId = "T202101110001";

    public static String BASE_URL = "http://192.168.20.145:8084/raserver/cmp/";
//    public static String BASE_URL = "http://192.168.200.253:9001/raserver/cmp/";

    public static String raCommCertStr = "-----BEGIN CERTIFICATE-----\n" +
            "MIICfjCCAiSgAwIBAgIUUu2A98JvPsO4bI3/cEw6z+XRllkwCgYIKoEcz1UBg3Uw\n" +
            "ODELMAkGA1UEBhMCQ04xDjAMBgNVBAoMBU5FVENBMRkwFwYDVQQDDBBORVRDQSBT\n" +
            "TTIgT3JnIENBMB4XDTIxMDEwODAxMjAzMFoXDTI2MDEwODAxMjAzMFowQjELMAkG\n" +
            "A1UEBhMCQ04xDjAMBgNVBAoMBU5FVENBMSMwIQYDVQQDDBpORVRDQSBTTTIgUkEg\n" +
            "Q29tbXVuaWNhdGlvbjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABI68afm+nPFD\n" +
            "N1Qnanse0Qh39vZGGVTtlKMlY9ghFO5W2sRL7kOz3NKmA3iCit91cCNxOYTypG/I\n" +
            "RP03UkvFDwmjggEAMIH9ME4GCCsGAQUFBwEBBEIwQDA+BggrBgEFBQcwAYYyaHR0\n" +
            "cDovLzE5Mi4xNjguMjAwLjI1Mzo5MDAzL29jc3BtYW5hZ2VyL29jc3AvY2hlY2sw\n" +
            "HQYDVR0OBBYEFKZgmpE/bDUWFYy4DtQjI8LctExXMA4GA1UdDwEB/wQEAwIGwDAM\n" +
            "BgNVHRMBAf8EAjAAME0GA1UdHwRGMEQwQqBAoD6GPGh0dHA6Ly8xOTIuMTY4LjIw\n" +
            "MC4yNTM6OTAwMS9yYW1hbmFnZXIvY3JsdGVtcGxhdGUvZG93bmxvYWQvNjAfBgNV\n" +
            "HSMEGDAWgBSC1rs1Ux1bIi/GYwGjpzZlbfoSSjAKBggqgRzPVQGDdQNIADBFAiAY\n" +
            "CVk8lS1GSoKd/Cl241KsLrGCctJqLSzIe3T9Z3Y6WgIhAPhVZJ16cam4gz28whvU\n" +
            "l0aWrhp0Ff7Zlqmh3z2iqh2J\n" +
            "-----END CERTIFICATE-----";

    @Override
    public String getCertTempleId() {
        return certTempleId;
    }

    @Override
    public String getBaseUrl() {
        return BASE_URL;
    }

    @Override
    public String getCaCommCertSha256() {
        return caCommCertSha256;
    }

    @Override
    public String getRaCommCert() {
        return raCommCertStr;
    }
}
