package net.netca.cloudkey.base.constant;


import net.netca.cloudkey.base.constant.base.IBaseEnum;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 文件头类型
 * JPEG (jpg)，文件头：FFD8FF
 * PNG (png)，文件头：89504E47
 * GIF (gif)，文件头：47494638
 * Windows Bitmap (bmp)，文件头：424D
 * CAD (dwg)，文件头：41433130
 * Adobe Photoshop (psd)，文件头：38425053
 * Rich Text Format (rtf)，文件头：7B5C727466
 * XML (xml)，文件头：3C3F786D6C
 * HTML (html)，文件头：68746D6C3E
 * Email [thorough only] (eml)，文件头：44656C69766572792D646174653A
 * Outlook Express (dbx)，文件头：CFAD12FEC5FD746F
 * Outlook (pst)，文件头：2142444E
 * MS Access (mdb)，文件头：5374616E64617264204A
 * WordPerfect (wpd)，文件头：FF575043
 * Postscript (eps.or.ps)，文件头：252150532D41646F6265
 * Adobe Acrobat (pdf)，文件头：255044462D312E
 * Quicken (qdf)，文件头：AC9EBD8F
 * Windows Password (pwl)，文件头：E3828596
 * RAR Archive (rar)，文件头：52617221
 * Wave (wav)，文件头：57415645
 * AVI (avi)，文件头：41564920
 * Real Audio (ram)，文件头：2E7261FD
 * Real Media (rm)，文件头：2E524D46
 * MPEG (mpg)，文件头：000001BA
 * MPEG (mpg)，文件头：000001B3
 * Quicktime (mov)，文件头：6D6F6F76
 * Windows Media (asf)，文件头：3026B2758E66CF11
 * MIDI (mid)，文件头：4D546864
 */
public enum FileHeadTypeConstant implements IBaseEnum {
	JPEG("JPEG", "FFD8FF"),
	PNG("PNG", "89504E47"),
	GIF("GIF", "47494638"),
	BMP("BMP", "424D"),
	WAVE("WAV", "57415645"),
	AVI("AVI", "41564920"),
	RAM("RAM", "2E7261FD"),
	rm("RM", "2E524D46"),
	MPEG1("MPG1", "000001BA"),
	MPEG2("MPG2", "000001B3"),
	MOV("MOV", "6D6F6F76"),
	MIDI("MID", "4D546864"),

	;

	private String code;
	private String description;

	private FileHeadTypeConstant(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public static boolean isAllowType(String fileHeadType) {
		String fourBit = fileHeadType.substring(0, 4);
		String sixBit = fileHeadType.substring(0, 6);
		String eightBit = fileHeadType;

		Stream<FileHeadTypeConstant> stream = Arrays.stream(FileHeadTypeConstant.values());

		Optional<FileHeadTypeConstant> first = stream.filter(fileHeadTypeConstant ->
				fileHeadTypeConstant.description.equalsIgnoreCase(eightBit)
						|| fileHeadTypeConstant.description.equalsIgnoreCase(sixBit)
						|| fileHeadTypeConstant.description.equalsIgnoreCase(fourBit)).findFirst();
		return first.isPresent();
	}

	@Override
	public String getCode() {
		return code;
	}

	@Override
	public String getDescription() {
		return description;
	}

}
