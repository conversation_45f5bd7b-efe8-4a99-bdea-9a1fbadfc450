package net.netca.cloudkey.base.constant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description  需做脱敏处理的相关字段
 * @Date 14:24 2020/9/15
 **/
public enum SentitiveFieldEnum {

    /**
     * type:  0证件号码 1手机号码 2邮箱 3地址
     * 按要求(YMY-1191)，目前云密钥需做脱敏的相关字段仅有证件号码
     **/

    IDENTITY("identity", 0),
    LEGAL_PERSON_IDENTITY("legalPersonIdentity", 0),
    PHONE("phone", 1),
    LEGAL_PERSON_PHONE("legalPersonPhone", 1),
    EMAIL("email", 2),
    OFFICIAL_RESIDENCE("officialResidence", 3),
    ADDRESS("address", 3);

    private String field;
    private int type;


    SentitiveFieldEnum(String field, int type) {
        this.field = field;
        this.type = type;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }


    static public List<String> getFieldByType(int type){
        return Arrays.asList(SentitiveFieldEnum.values()).stream()
                .filter(sentitiveFieldEnum -> sentitiveFieldEnum.getType() == type)
                .map(sentitiveFieldEnum -> sentitiveFieldEnum.getField())
                .collect(Collectors.toList());
    }

    /**
     * @Description  获取分类为证件号码需做脱敏的相关字段
     **/
    static public List<String> getIdentityField(){
        return getFieldByType(0);
    }

    /**
     * @Description  获取分类为手机号码需做脱敏的相关字段
     **/
    static public List<String> getPhoneField(){
        return getFieldByType(1);
    }

    /**
     * @Description  获取分类为邮箱需做脱敏的相关字段
     **/
    static public List<String> getemailField(){
        return getFieldByType(2);
    }

    /**
     * @Description  获取分类为地址需做脱敏的相关字段
     **/
    static public List<String> getaddressField(){
        return getFieldByType(3);
    }
}
