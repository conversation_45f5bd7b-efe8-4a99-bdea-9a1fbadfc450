package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;

/**
 * <AUTHOR>
 */
public enum CertBindingStatusEnum implements IBaseEnum {
    /**
     * 证书绑定状态
     */
    BINDING(0, "有"),
    NOT_BINDING(1, "无");


    private int code;

    private String description;

    CertBindingStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
