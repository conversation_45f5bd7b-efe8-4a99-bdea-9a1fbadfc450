package net.netca.cloudkey.base.constant;

import net.netca.cloudkey.base.constant.base.IBaseEnum;
import net.netca.cloudkey.base.exception.CloudKeyException;

import java.util.Arrays;
import java.util.Optional;

/**
 * @descriptions: 性别常量
 * @date: 2019/1/15
 * @author: SaromChars
 */
public enum GenderEnum implements IBaseEnum {

    MALE(0, "男"), FEMALE(1, "女");

    private Integer code;
    private String description;

    GenderEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static GenderEnum getGenderEnum(Integer code) throws CloudKeyException {
        Optional<GenderEnum> first = Arrays.stream(GenderEnum.values())
                .filter(genderEnum -> genderEnum.code.equals(code))
                .findFirst();


        if (!first.isPresent()) {
            throw new CloudKeyException("非法的 GenderEnum");
        }

        return first.get();
    }
}
